// 上传服务器
// 用于处理钉钉JSAPI的文件上传请求

import express from 'express';
import multer from 'multer';
import cors from 'cors';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

// 获取当前文件的目录名
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // 使用时间戳+原始文件名生成新文件名，避免文件名冲突
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
        cb(null, uniqueSuffix + '-' + file.originalname);
    },
});

const upload = multer({ storage: storage });

// 创建Express应用
const app = express();
const PORT = 8080;
const HOST = '0.0.0.0';
const IP_ADDRESS = '**************'; // 替换为你的电脑IP地址

// 使用CORS中间件
app.use(cors());

// 处理文件上传的路由
app.post('/image/create', upload.any(), (req, res) => {
    try {
        console.log('接收到文件上传请求');

        // 打印请求信息
        console.log('请求参数:', req.query);
        console.log('请求头:', req.headers);
        console.log('请求体:', req.body);

        // 检查请求中的文件
        if (!req.files || req.files.length === 0) {
            console.log('没有接收到文件');
            console.log('请求体:', req.body);
            console.log('所有文件:', req.files);

            return res.status(400).json({
                success: false,
                errorMessage: '没有接收到文件',
            });
        }

        // 获取上传的第一个文件
        const file = req.files[0];

        // 打印文件信息
        console.log('文件信息:', {
            fieldname: file.fieldname,
            originalname: file.originalname,
            encoding: file.encoding,
            mimetype: file.mimetype,
            destination: file.destination,
            filename: file.filename,
            path: file.path,
            size: file.size,
        });

        // 构建文件URL
        const fileUrl = `http://${IP_ADDRESS}:${PORT}/uploads/${file.filename}`;

        // 返回成功响应
        // 根据钉钉JSAPI的要求返回适当的格式
        res.json({
            success: true,
            fileId: file.filename,
            fileUrl: fileUrl,
            fileName: file.originalname,
            fileSize: file.size,
            fileMime: file.mimetype,
        });

        console.log('文件上传成功，返回:', {
            success: true,
            fileId: file.filename,
            fileUrl: fileUrl,
            fileName: file.originalname,
            fileSize: file.size,
            fileMime: file.mimetype,
        });
    } catch (error) {
        console.error('处理上传请求时出错:', error);
        res.status(500).json({
            success: false,
            errorMessage: error.message,
        });
    }
});

// 提供上传文件的静态访问
app.use('/uploads', express.static(uploadDir));

// 启动服务器
app.listen(PORT, HOST, () => {
    console.log(`文件上传服务器已启动，监听: ${HOST}:${PORT}`);
    console.log(`通过IP访问: http://${IP_ADDRESS}:${PORT}/image/create`);
    console.log(`上传目录: ${uploadDir}`);
});

console.log('服务器脚本已加载');
