import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

export const useSmartDataStore = defineStore('smartData', {
    state: () => ({}),
    getters: {},
    actions: {
        // 获取级联列表第一级
        async getCascadeListLevel1(url, params) {
            try {
                const data = await httpService.get(url, params);
                if (data.result && data.result.length > 0) {
                    return data.result;
                }
                return [];
            } catch (err) {
                console.error('Error fetching child bots:', err.message);
                return [];
            }
        },

        // 获取级联列表第二级
        async getCascadeListLevel2(url, params) {
            try {
                const data = await httpService.get(url, params);
                if (data.result && data.result.length > 0) {
                    return data.result;
                }
                return [];
            } catch (err) {
                console.error('Error fetching child bots:', err.message);
                return [];
            }
        },

        // 获取品牌列表
        async getBrandList(url, params) {
            try {
                const data = await httpService.get(url, params);
                if (data.result && data.result.branditems && data.result.branditems.length > 0) {
                    return data.result.branditems;
                }
                return [];
            } catch (err) {
                console.error('Error fetching brand list:', err.message);
                return [];
            }
        }
    }
});
