import { defineStore } from 'pinia';

// 转换为选项式 API 风格
export const useIframeStore = defineStore('iframe', {
    // 状态定义为返回初始状态的函数
    state: () => ({
        // 存储最新的文字消息
        latestTextMessage: '',
        // 消息是否已被处理的标志
        isMessageProcessed: true,
        iframeHeight: null,
        // 语音输入相关状态
        voiceInputVisible: false,
        voiceInputOptions: null
    }),

    // 定义 getters
    getters: {
        // 如有需要可以添加 getters
    },

    // 定义 actions
    actions: {
        /**
         * 设置新的文字消息
         * @param {string} message - 文字消息内容
         */
        setTextMessage(message) {
            this.latestTextMessage = message;
            this.isMessageProcessed = false;
        },

        /**
         * 标记消息已处理
         */
        markMessageAsProcessed() {
            this.isMessageProcessed = true;
        },

        /**
         * 清空消息
         */
        clearMessage() {
            this.latestTextMessage = '';
            this.isMessageProcessed = true;
        },

        /**
         * 设置iframe高度
         * @param {number} height - iframe高度
         */
        setIframeHeight(height) {
            this.iframeHeight = height;
        },

        /**
         * 设置语音输入面板可见性
         * @param {boolean} visible - 是否可见
         */
        setVoiceInputVisible(visible) {
            this.voiceInputVisible = visible;
        },

        /**
         * 设置语音输入选项
         * @param {Object} options - 语音输入选项
         */
        setVoiceInputOptions(options) {
            this.voiceInputOptions = options;
        },

        /**
         * 清空语音输入相关状态
         */
        clearVoiceInput() {
            this.voiceInputVisible = false;
            this.voiceInputOptions = null;
        }
    }
});
