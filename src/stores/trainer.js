import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';
import { getStorageItem, setStorageItem } from '@/utils/dingStorageService';

// 存储键名常量
const STORAGE_KEYS = {
    AUDIO_SETTINGS: 'trainer_audio_settings',
    INTERFACE_SETTINGS: 'trainer_interface_settings'
};

export default defineStore('trainer', {
    state: () => ({
        currentPaper: null,
        currentPaperId: null,
        currentQuestionId: null,
        practiceSession: null,
        currentQuestion: null,
        currentAnswer: null,
        messageList: [],
        examCategoryList: [],
        examListMap: {}, // 存储每个分类的考试列表 { categoryId: { list: [], pageInfo: {} } }
        isAnswering: false, // 是否正在答题中
        isEvaluating: false, // 是否正在评估中
        uncompletedPapers: [], // 未完成试卷列表
        showUncompletedDialog: false, // 是否显示未完成试卷弹窗
        autoPlayAudio: true, // 是否自动播放音频
        audioPlayDelay: 2000, // 音频播放延迟时间(毫秒)
        showMilestones: true,
        isStorageInitialized: false
    }),

    getters: {
        getCurrentPaper: state => state.currentPaper,
        getCurrentQuestion: state => state.currentQuestion,
        getCurrentAnswer: state => state.currentAnswer,
        getExamListByCategoryId: state => categoryId => state.examListMap[categoryId]?.list || []
    },

    actions: {
        // 重置所有状态到初始值
        resetTrainerState() {
            this.$reset();
            this.clearCurrentSession();
        },

        // 清除当前会话相关的状态
        clearCurrentSession() {
            this.currentPaper = null;
            this.currentPaperId = null;
            this.currentQuestionId = null;
            this.currentQuestion = null;
            this.currentAnswer = null;
            this.isAnswering = false;
            this.isEvaluating = false;
            this.practiceSession = null;
        },

        // 考试分类列表
        async getExamCategoryList() {
            const url = '/trainer-api/examCategory/list';
            try {
                const response = await httpService.get(url);
                if (response.result) {
                    this.examCategoryList = response.result;
                    // 获取到分类列表后，立即获取每个分类下的考试列表
                    await this.fetchAllExamLists();
                }
                return response.result;
            } catch (error) {
                console.error('获取考试分类列表失败:', error.message);
                throw error;
            }
        },

        // 获取所有分类的考试列表
        async fetchAllExamLists() {
            const promises = this.examCategoryList.map(category =>
                this.getExamList({
                    examCategoryId: category.id,
                    pageIndex: 1,
                    pageSize: 1000 // 设置一个较大的值，确保能获取到所有数据
                })
            );
            await Promise.all(promises);
        },

        // 获取考试列表
        async getExamList(params) {
            const url = '/trainer-api/exam/page';
            try {
                const response = await httpService.get(url, params);
                if (response.result) {
                    // 将获取到的考试列表存储到对应分类的 map 中
                    this.examListMap[params.examCategoryId] = {
                        list: response.result.list || [],
                        pageInfo: {
                            rowcount: response.result.rowcount,
                            pagecount: response.result.pagecount,
                            pageindex: response.result.pageindex
                        }
                    };
                }
                return response.result;
            } catch (error) {
                console.error('获取考试列表失败:', error.message);
                throw error;
            }
        },

        // 考试详情接口
        async getExamDetail(params) {
            const url = '/trainer-api/exam/detail';
            try {
                const response = await httpService.get(url, params);
                return response.result;
            } catch (error) {
                console.error('获取考试详情失败:', error.message);
                throw error;
            }
        },

        // 创建试卷
        async createPaper(body) {
            const url =
                '/trainer-api/paper/create' +
                (body.examId ? `?examId=${body.examId}` : '') +
                (body.force ? `&force=${body.force}` : '');
            try {
                const response = await httpService.post(url);
                if (response.result) {
                    this.currentPaper = response.result;
                    this.currentPaperId = response.result.id;
                    return response.result;
                }
                return null;
            } catch (error) {
                console.error('创建试卷失败:', error.message);
                throw error;
            }
        },

        // 我的未完成试卷列表
        async getUncompletedPaperList(examId) {
            const url = '/trainer-api/paper/unCompletedList';

            try {
                const response = await httpService.get(url, {
                    examId
                });

                if (response.result) {
                    this.uncompletedPapers = response.result;
                    if (this.uncompletedPapers.length > 0) {
                        this.showUncompletedDialog = true;
                    }
                    return response.result;
                }
                return null;
            } catch (error) {
                console.error('❌ 获取未完成试卷列表失败:', error.message);
                console.error('请求参数 examId:', examId);
                throw error;
            }
        },

        // 消息（问题、回答、评价）列表页
        async getHistoryMessageList(params) {
            const url = '/trainer-api/paperQuestion/page';
            try {
                const response = await httpService.get(url, params);
                const result = response.result;
                if (result && result.list && result.list.length > 0) {
                    // 最新提问在最上面（为了前端展示。）
                    if (result.list[0].id) {
                        this.currentQuestionId = result.list[0].id;
                    }
                }
                return result;
            } catch (error) {
                console.error('获取消息列表失败:', error.message);
                throw error;
            }
        },

        // 获取一道题目
        async getQuestion(body) {
            const url =
                '/trainer-api/paperQuestion/next' +
                (body.paperId ? `?paperId=${body.paperId}` : '');
            try {
                const response = await httpService.post(url);

                if (response.result) {
                    this.currentQuestion = response.result;
                    this.currentQuestionId = response.result.id;
                    return response.result;
                }
                return null;
            } catch (error) {
                console.error('获取题目失败:', error.message);
                throw error;
            }
        },

        // 语音回答
        async sendAudioAnswer(body) {
            const url =
                '/trainer-api/paperAnswer/audio' +
                (body.paperQuestionId ? `?paperQuestionId=${body.paperQuestionId}` : '') +
                (body.remoteUrl ? `&remoteUrl=${body.remoteUrl}` : '');
            try {
                const response = await httpService.post(url, body);
                this.currentAnswer = response.result;
                return response.result;
            } catch (error) {
                console.error('语音答题失败:', error.message);
                throw error;
            }
        },

        // 钉钉语音回答
        async sendDingAudioAnswer(body) {
            const url =
                '/trainer-api/paperAnswer/dingAudio' +
                (body.paperQuestionId ? `?paperQuestionId=${body.paperQuestionId}` : '') +
                (body.mediaId ? `&mediaId=${body.mediaId}` : '') +
                (body.duration ? `&duration=${body.duration}` : '');
            try {
                const response = await httpService.post(url, body);
                return response.result;
            } catch (error) {
                console.error('钉钉语音答题失败:', error.message);
                throw error;
            }
        },

        // 获取回答信息（语音回答后获取asr文本等信息）
        async getAnswerAsr(params) {
            const url = '/trainer-api/paperAnswer/get';
            try {
                const response = await httpService.get(url, params);
                return response.result;
            } catch (error) {
                console.error('获取回答信息失败:', error.message);
                throw error;
            }
        },

        // 完成回答
        async completeAnswer(body) {
            const url =
                '/trainer-api/paperQuestion/complete' +
                (body.paperQuestionId ? `?paperQuestionId=${body.paperQuestionId}` : '');
            try {
                const response = await httpService.post(url);
                return response.result;
            } catch (error) {
                console.error('完成回答失败:', error.message);
                throw error;
            }
        },

        // 试卷列表页-可用于【我的】已完成、进行中列表页
        async getPaperList(body) {
            const url = '/trainer-api/paper/page';
            try {
                const response = await httpService.post(url, body);
                return response.result;
            } catch (error) {
                console.error('获取试卷列表失败:', error.message);
                throw error;
            }
        },

        // 获取评论和评分
        async getCommentAndScore(body) {
            const url =
                '/trainer-api/paperCriterionAssessment/assess' +
                (body.paperQuestionId ? `?paperQuestionId=${body.paperQuestionId}` : '');
            try {
                const response = await httpService.post(url);
                return response;
            } catch (error) {
                console.error('获取评论和评分失败:', error.message);
                throw error;
            }
        },

        // 结果页：答题结束后获取整体分数和维度分析接口
        async getResult(params) {
            const url = '/trainer-api/paper/ability';
            try {
                const response = await httpService.get(url, params);
                return response;
            } catch (error) {
                console.error('获取考试结果失败 from getResult:', error);
                throw error;
            }
        },

        // 设置当前试卷ID
        setCurrentPaperId(id) {
            this.currentPaperId = id;
        },

        // 排行榜接口
        async getRankList() {
            const url = '/trainer-api/paper/rank';
            try {
                const response = await httpService.get(url);
                return response.result;
            } catch (error) {
                console.error('获取排行榜失败:', error.message);
                throw error;
            }
        },

        // 获取问题数
        async getQuestionCount(params) {
            const url =
                '/trainer-api/paperQuestion/paperQuestionCount' +
                (params.paperId ? `?paperId=${params.paperId}` : '');
            try {
                const response = await httpService.get(url, params);
                return response.result;
            } catch (error) {
                console.error('获取问题数失败:', error.message);
                throw error;
            }
        },

        // 获取试卷详情接口
        async getPaperDetail(params) {
            const url =
                '/trainer-api/paper/detail' + (params.paperId ? `?paperId=${params.paperId}` : '');
            try {
                const response = await httpService.get(url, params);
                return response.result;
            } catch (error) {
                console.error('获取试卷详情失败:', error.message);
                throw error;
            }
        },

        // 获取考试分类及考试（分层）
        async getExamCategoryAndLevel() {
            const url = '/trainer-api/examCategory/all';
            try {
                const response = await httpService.get(url);
                return response.result;
            } catch (error) {
                console.error('获取考试分类及考试（分层）失败:', error.message);
                throw error;
            }
        },

        // 回答删除接口
        async deleteAnswer(body) {
            const url =
                '/trainer-api/paperAnswer/delete' +
                (body.paperAnswerId ? `?paperAnswerId=${body.paperAnswerId}` : '');
            try {
                const response = await httpService.post(url, body);
                return response.result;
            } catch (error) {
                console.error('删除回答失败:', error.message);
                throw error;
            }
        },

        // 删除题目下的回答
        async deleteAnswerByQuestionId(body) {
            const url =
                '/trainer-api/paperAnswer/deleteByPaperQuestionId' +
                (body.paperQuestionId ? `?paperQuestionId=${body.paperQuestionId}` : '');
            try {
                const response = await httpService.post(url, body);
                return response.result;
            } catch (error) {
                console.error('删除题目下的回答失败:', error.message);
                throw error;
            }
        },

        // 退出练习接口（不保存此次进度）
        async deletePractice(body) {
            const url =
                '/trainer-api/paper/delete' + (body.paperId ? `?paperId=${body.paperId}` : '');
            try {
                const response = await httpService.post(url, body);
                return response.result;
            } catch (error) {
                console.error('退出练习失败:', error.message);
                throw error;
            }
        },

        // 初始化存储
        async initializeStorage() {
            if (this.isStorageInitialized) {
                return;
            }

            try {
                // 加载音频设置
                const audioSettings = await getStorageItem(STORAGE_KEYS.AUDIO_SETTINGS);

                if (audioSettings) {
                    this.autoPlayAudio = audioSettings.autoPlay;
                    this.audioPlayDelay = audioSettings.delay;
                }

                // 加载界面设置
                const interfaceSettings = await getStorageItem(STORAGE_KEYS.INTERFACE_SETTINGS);
                if (interfaceSettings) {
                    this.showMilestones = interfaceSettings.showMilestones;
                }

                this.isStorageInitialized = true;
            } catch (error) {
                console.error('初始化存储失败:', error);
            }
        },

        // 设置音频播放配置
        async setAudioPlayConfig({ autoPlay, delay }) {
            this.autoPlayAudio = autoPlay;
            this.audioPlayDelay = delay;

            // 保存到钉钉存储
            try {
                await setStorageItem(STORAGE_KEYS.AUDIO_SETTINGS, {
                    autoPlay: this.autoPlayAudio,
                    delay: this.audioPlayDelay
                });
            } catch (error) {
                console.error('保存音频设置失败:', error);
            }
        },

        // 设置界面配置
        async setInterfaceConfig({ showMilestones }) {
            this.showMilestones = showMilestones;

            // 保存到钉钉存储
            try {
                await setStorageItem(STORAGE_KEYS.INTERFACE_SETTINGS, {
                    showMilestones: this.showMilestones
                });
            } catch (error) {
                console.error('保存界面设置失败:', error);
            }
        },

        // 重置所有设置为默认值
        async resetAllSettings() {
            // 重置音频设置
            await this.setAudioPlayConfig({
                autoPlay: true,
                delay: 2000
            });

            // 重置界面设置
            await this.setInterfaceConfig({
                showMilestones: true
            });
        }
    }
});
