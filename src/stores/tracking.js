import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

export const useTrackingStore = defineStore('tracking', {
    state: () => ({}),
    getters: {},
    actions: {
        // 统计事件
        async reportUserEvent(body) {
            const url = '/api/stat/event';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        }
    }
});
