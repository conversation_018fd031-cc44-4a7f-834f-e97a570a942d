import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';
import { getStorageItem, setStorageItem } from '@/utils/dingStorageService';

// 添加存储键名常量
const STORAGE_KEYS = {
    DEEP_THINKING: 'chat_deep_thinking',
    SESSION_ID: 'session_id',
    BOTS: 'bots'
};

export default defineStore('chat', {
    state: () => ({
        session: {},
        sessions: [], // 会话列表
        currentSessionId: null, // 当前选中的会话ID
        message: {},
        messages: [],
        elfBots: [], // 添加知识库列表
        selectedElfBot: null, // 添加选中的知识库
        childBots: [], // 添加子知识库列表
        selectedChildBot: null, // 添加选中的子知识库
        defaultBot: null, // 添加默认知识库
        tempMessage: null, // 添加临时消息，用于跨页面传递
        chatForm: {
            lastScrollTop: 0, // 存储上一次的滚动位置
            loadedAllMessages: false, // 是否全部加载完成(无最新消息)
            loading: false, // 是否正在加载
            showPreset: false, // 是否显示预设消息
            requestLock: false, // 请求锁
            loadingMore: false, // 是否正在加载更多消息
            data: {
                // 聊天数据
                message: '', // 消息
                messages: {
                    // 消息列表
                    pageIndex: 1,
                    pageSize: 5
                }
            },
            eventSource: {
                // 事件源
                close: function () {
                    // 关闭事件源的默认处理
                }
            },
            stop: {
                // 停止发送消息
                visibility: false
            }
        },
        deepThinking: false // 深度思考状态
    }),
    getters: {},
    actions: {
        // 获取知识库列表
        async getBots() {
            const url = '/api/chatMessage/elfBots';
            try {
                const data = await httpService.get(url);

                if (data.result && data.result.length > 0) {
                    // 遍历获取第一个返回isDefault为 1 的 bot，存下来，作为默认 bot
                    const defaultBot = data.result.find((bot) => bot.defaulted);
                    this.defaultBot = defaultBot;
                    this.elfBots = data.result;

                    // 将对应数据存到 localStorage 中
                    await setStorageItem(STORAGE_KEYS.BOTS, data.result);

                    return data.result;
                }

                return [];
            } catch (err) {
                console.error('Error fetching knowledge bases:', err.message);
                return [];
            }
        },

        // 设置选中的 bot
        setSelectedElfBot(bot) {
            this.selectedElfBot = bot;
        },

        // 获取子知识库列表
        async getChildBots(elfBotId) {
            if (!elfBotId) {
                return [];
            }

            const url = '/api/chatMessage/elfBots';
            try {
                const data = await httpService.get(url, { elfBotId });
                if (data.result && data.result.length > 0) {
                    this.childBots = data.result;
                    return data.result;
                }
                this.childBots = [];
                return [];
            } catch (err) {
                console.error('Error fetching child bots:', err.message);
                this.childBots = [];
                return [];
            }
        },

        // 添加设置临时消息的方法
        setTempMessage(message) {
            this.tempMessage = message;
        },

        // 添加清除临时消息的方法
        clearTempMessage() {
            this.tempMessage = null;
        },

        /**
         * 获取会话列表
         * @param {Object} params - 分页参数
         * @returns {Promise<Object>} - 会话列表结果
         */
        async getSessions(params) {
            const url = '/api/chatSession/page';
            try {
                const data = await httpService.get(url, params);
                if (data.result) {
                    this.sessions = data.result.list || [];
                    return data.result;
                }
                return { list: [] };
            } catch (err) {
                console.error('获取会话列表失败:', err.message);
                return { list: [] };
            }
        },

        /**
         * 清除当前会话ID（用于新会话）
         */
        clearCurrentSessionId() {
            this.currentSessionId = null;
        },

        /**
         * 获取聊天消息
         * @param {Object} params - 请求参数
         * @param {number} params.pageIndex - 页码
         * @param {number} params.pageSize - 每页条数
         * @param {number} [params.sessionId] - 会话ID (可选)
         * @param {boolean} [params.fromHistory] - 是否来自历史页面
         * @returns {Promise<Object>} - 消息列表结果
         */
        async getMessages(params) {
            const url = '/api/chatMessage/page';

            try {
                const requestParams = { ...params };

                if (this.currentSessionId) {
                    requestParams.sessionId = this.currentSessionId;
                }

                const data = await httpService.get(url, requestParams);

                if (data.result) {
                    let messageList = data.result.list;

                    // 根据是否来自历史页面决定是否反转列表
                    if (!params.fromHistory) {
                        messageList = messageList.reverse();
                    }

                    // 判断是否是加载更多
                    if (params.pageIndex > 1) {
                        // 加载更多时，将新消息添加到现有消息的前面或后面
                        if (params.fromHistory) {
                            this.messages = [...this.messages, ...messageList];
                        } else {
                            this.messages = [...messageList, ...this.messages];
                        }
                    } else {
                        // 首次加载时直接替换
                        this.messages = messageList;
                    }

                    // 返回完整的结果，包括分页信息
                    return data.result;
                }

                return { list: [] };
            } catch (err) {
                console.error('获取消息列表失败:', err.message);
                return { list: [] };
            }
        },
        async createMessage(body) {
            const url = '/api/chatMessage/create';
            let res = null;
            try {
                // 优先使用选中的子知识库ID，其次是父知识库ID，最后是默认知识库ID
                const elfBotId = this.selectedElfBot ? this.selectedElfBot.id : this.defaultBot.id;

                // 构建消息体，如果有当前会话ID则添加
                const messageBody = { ...body, elfBotId };
                if (this.currentSessionId) {
                    messageBody.sessionId = this.currentSessionId;
                }

                res = await httpService.post(url, messageBody);

                return res;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },
        clearMessages() {
            this.messages = [];
        },
        stopSendMessage(e) {
            e.stopPropagation(); // 阻止事件冒泡 防止触发父元素的点击事件
            this.chatForm.eventSource.close();
            this.chatForm.stop.visibility = false;
        },
        // 重置聊天状态
        resetState() {
            // 清空消息列表
            this.messages = [];

            // 清空临时消息
            this.tempMessage = null;

            // 重置聊天表单状态
            this.chatForm = {
                lastScrollTop: 0,
                loadedAllMessages: false,
                loading: false,
                showPreset: false,
                requestLock: false,
                loadingMore: false,
                data: {
                    message: '',
                    messages: {
                        pageIndex: 1,
                        pageSize: 5
                    }
                },
                eventSource: {
                    close: function () {
                        // 关闭事件源的默认处理
                    }
                },
                stop: {
                    visibility: false
                }
            };

            // 清空子bot相关状态
            this.childBots = [];
            this.selectedChildBot = null;
        },

        // 设置深度思考状态
        async setDeepThinking(value) {
            this.deepThinking = value;
            // 保存到存储
            try {
                await setStorageItem(STORAGE_KEYS.DEEP_THINKING, value);
            } catch (error) {
                console.error('保存深度思考状态失败:', error);
            }
        },

        async setSessionId(sessionId) {
            this.currentSessionId = sessionId;

            // 保存到存储
            try {
                await setStorageItem(STORAGE_KEYS.SESSION_ID, sessionId);
            } catch (error) {
                console.error('保存会话ID失败:', error);
            }
        },

        // 初始化存储
        async initializeStorage() {
            try {
                // 加载深度思考设置
                const deepThinkingValue = await getStorageItem(STORAGE_KEYS.DEEP_THINKING);
                const sessionId = await getStorageItem(STORAGE_KEYS.SESSION_ID);
                const bots = await getStorageItem(STORAGE_KEYS.BOTS);

                this.deepThinking = deepThinkingValue;
                this.currentSessionId = sessionId;
                this.elfBots = bots;
            } catch (error) {
                console.error('初始化存储失败:', error);
            }
        }
    }
});
