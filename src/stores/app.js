import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

export const useAppStore = defineStore('app', {
    state: () => ({
        authCode: '',
        isLoggedIn: false
    }),
    getters: {},
    actions: {
        async login(code) {
            const url = '/api/auth/login?code=' + code;
            try {
                const res = await httpService.post(url);
                if (res.returncode === 0) {
                    this.isLoggedIn = true;
                }
                return res;
            } catch (err) {
                console.error('Error fetching data:', err.message);
                this.isLoggedIn = false;
                throw err;
            }
        },

        // 登出方法
        logout() {
            try {
                // 这里可以添加调用登出接口的逻辑
                this.isLoggedIn = false;
                // 清除钉钉初始化状态
                sessionStorage.removeItem('DING_TALK_INITIALIZED_KEY');
            } catch (err) {
                console.error('登出失败:', err.message);
                throw err;
            }
        },

        async getUserSystems() {
            const url = '/api/crm/product/systems-byuser';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error('Error fetching data:', err.message);
                throw err;
            }
        },

        async getLoginSystems() {
            const url = '/api/crm/appquanxian/getLoginSystem';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error('Error fetching data:', err.message);
                throw err;
            }
        },

        async getProductList(body) {
            const url = '/api/crm/product/list-new-byuser';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        async getDicData(params) {
            const url = '/api/crm/dictionary/getdicdata';
            try {
                const data = await httpService.get(url, params);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        async getProductDetail(params) {
            const url = '/api/crm/product/detail-new-byuser';
            try {
                const data = await httpService.get(url, params);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        async getProductsFiles(body) {
            const url = '/api/crm/product/getProductsFiles-byUser';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        // 发送文件到钉钉机器人
        async sendFile(body) {
            const url = '/api/file/send';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        // 检查用户是否处在登录态了
        async checkIfLogin() {
            const url = '/api/auth/verifyToken';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        // 获取盯盘文件预览url
        async getDingPreviewLink(body) {
            const url = '/api/file/dingPreviewLink';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        // 获取钉钉微应用的ticket
        async getTicket() {
            const url = '/api/ticket/apply';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        // 获取测试环境的ticket
        async getTicketTest() {
            const url = '/dutyListApi/ticket/applyTest';
            try {
                const data = await httpService.get(url);
                return data;
            } catch (err) {
                console.error('获取ticket失败:', err.message);
                throw err;
            }
        },

        // ticket登录
        async ticketLogin(ticket) {
            const url = `/api/auth/ticketLogin?ticket=${ticket}`;
            try {
                const data = await httpService.get(url);
                if (data.returncode === 0) {
                    this.isLoggedIn = true;
                }
                return data;
            } catch (err) {
                console.error('ticket登录失败:', err.message);
                throw err;
            }
        },

        // 获取 jsapi 配置
        async getJsApiConfig(body) {
            const url = '/api/jsapi/config';
            try {
                const data = await httpService.post(url, body);
                return data.result;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        }
    }
});
