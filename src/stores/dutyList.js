import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

export const useDutyListStore = defineStore('dutyList', {
    state: () => ({}),
    getters: {},
    actions: {
        async getHolidayPlan(body) {
            const url = '/api/holiday/plan';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        },

        async getHolidayPlanItems(body) {
            const url = '/api/holiday/planItems';
            try {
                const data = await httpService.post(url, body);
                return data;
            } catch (err) {
                console.error(err.message);
                throw err;
            }
        }
    }
});
