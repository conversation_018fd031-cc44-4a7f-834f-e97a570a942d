import { useDingTalkBridge } from './dingTalkBridgeService';
import { useIframeStore } from '@/stores/iframe';
import { logInfo, logError } from '@/services/ftwoService';

/**
 * iframe通信服务
 * 用于管理与iframe页面之间的通信逻辑
 */
export function useIframeService() {
    // 钉钉桥接服务
    const dingTalkBridge = useDingTalkBridge();

    // 获取 iframe Store
    const iframeStore = useIframeStore();

    // 消息缓存队列，用于缓存早期收到的消息
    const messageQueue = [];
    let isIframeReady = false;

    // 内部维护的iframeWindow引用
    let currentIframeWindow = null;

    /**
     * 设置当前的iframeWindow引用
     * @param {Window} iframeWindow iframe的Window对象
     */
    const setIframeWindow = iframeWindow => {
        currentIframeWindow = iframeWindow;
    };

    /**
     * 获取当前的iframeWindow引用
     * @returns {Window|null} 当前的iframeWindow
     */
    const getCurrentIframeWindow = () => {
        return currentIframeWindow;
    };

    /**
     * 处理来自iframe的请求
     * @param {Object} request 请求对象
     * @param {string} request.action 请求动作
     * @param {Object} request.data 请求数据
     * @param {string} request.requestId 请求ID
     * @param {Window} [iframeWindow] iframe的Window对象（可选，不传则使用内部引用）
     */
    const handleIframeRequest = async (request, iframeWindow = currentIframeWindow) => {
        const { action, data, requestId } = request;

        try {
            let responseData = null;

            // 根据消息类型处理
            if (action === 'text') {
                // 处理文字类型消息
                responseData = handleTextMessage(data);
            } else if (action === 'updateHeight') {
                // 处理高度更新
                responseData = handleHeightUpdate(data);
            } else {
                // 处理API类型消息
                switch (action) {
                    case 'getLocation':
                        responseData = await dingTalkBridge.getLocation(data);
                        responseData = {
                            ...responseData,
                            timestamp: Date.now()
                        };
                        break;

                    case 'takePhoto':
                        responseData = await dingTalkBridge.takePhoto(data);
                        responseData = {
                            ...responseData,
                            timestamp: Date.now()
                        };
                        break;

                    case 'showLocationInMap':
                        responseData = await dingTalkBridge.showLocationInMap(data);
                        responseData = {
                            ...responseData,
                            timestamp: Date.now()
                        };
                        break;

                    case 'openVoice':
                        // 调用openVoice仅获取配置信息
                        await dingTalkBridge.openVoice();

                        // 显示录音面板，但不立即发送响应
                        iframeStore.setVoiceInputVisible(true);
                        iframeStore.setVoiceInputOptions({
                            ...data,
                            requestId
                        });

                        // 不在这里处理响应，而是在录音完成后由VoiceInputPanel组件触发响应
                        // 提前返回，避免发送默认响应
                        return null;

                    default:
                        throw new Error(`未知的API请求类型: ${action}`);
                }
            }

            // 发送成功响应
            if (responseData !== null) {
                sendResponseToIframe(
                    {
                        requestId,
                        success: true,
                        data: responseData
                    },
                    iframeWindow
                );

                return responseData;
            }

            return null;
        } catch (error) {
            console.error(`处理请求失败: ${error.message}`);

            // 发送错误响应
            sendResponseToIframe(
                {
                    requestId,
                    success: false,
                    error: error.message
                },
                iframeWindow
            );

            throw error;
        }
    };

    /**
     * 处理文字类型的消息
     * @param {Object} data 消息数据
     * @returns {Object} 处理结果
     */
    const handleTextMessage = data => {
        // 将消息存储到 Pinia Store
        if (data.content) {
            iframeStore.setTextMessage(data.content);
        }

        return { success: true };
    };

    /**
     * 处理iframe高度更新消息
     * @param {Object} data 高度数据
     * @returns {Object} 处理结果
     */
    const handleHeightUpdate = data => {
        if (data && typeof data.height !== 'undefined') {
            // 将高度信息存储到 Pinia Store
            iframeStore.setIframeHeight(data.height);
            return { success: true };
        }
        throw new Error('无效的高度数据');
    };

    /**
     * 向iframe发送响应
     * @param {Object} response 响应对象
     * @param {string} response.requestId 请求ID
     * @param {boolean} response.success 是否成功
     * @param {Object} response.data 响应数据
     * @param {string} response.error 错误信息
     * @param {Window} [iframeWindow] iframe的Window对象（可选，不传则使用内部引用）
     */
    const sendResponseToIframe = (response, iframeWindow = currentIframeWindow) => {
        const { requestId, success, data, error } = response;

        const message = {
            source: 'host-app',
            action: 'response',
            requestId,
            success,
            data,
            error
        };

        if (iframeWindow) {
            logInfo('iframe数据发送成功', message.action, message.requestId, message.success);
            iframeWindow.postMessage(message, '*');
        } else {
            const errorMsg = '无法发送响应: iframe窗口不存在';
            console.error(errorMsg);
            logError('iframe响应发送失败', message, 'iframe窗口不存在');
        }
    };

    /**
     * 处理来自iframe的消息
     * @param {MessageEvent} event 消息事件
     * @param {Window} [iframeWindow] iframe的Window对象（可选，不传则使用内部引用）
     */
    const handleMessage = (event, iframeWindow = currentIframeWindow) => {
        // 安全检查：在生产环境中应该指定具体的域名，而不是使用'*'
        // if (event.origin !== 'https://trusted-iframe-domain.com') return;

        const { data } = event;

        // 检查消息格式是否合法
        if (!data || typeof data !== 'object' || data.source !== 'iframe-app') {
            return;
        }

        // 添加接收消息的调试日志
        logInfo('iframe消息接收成功', data.source, data.action, data.requestId);

        // 如果是iframe准备就绪的消息
        if (data.action === 'ready') {
            isIframeReady = true;
            // 处理缓存的消息
            while (messageQueue.length > 0) {
                const cachedMessage = messageQueue.shift();
                handleIframeRequest(cachedMessage, iframeWindow);
            }
            return;
        }

        // 如果是API请求类型的消息
        if (data.action && data.requestId) {
            // 如果iframe还未准备好，将消息加入队列
            if (!isIframeReady) {
                messageQueue.push(data);
                return;
            }

            handleIframeRequest(data, iframeWindow);
        }
    };

    /**
     * 设置事件监听
     * @param {Window} iframeWindow iframe的Window对象
     */
    const setupMessageListener = iframeWindow => {
        // 设置内部的iframeWindow引用
        setIframeWindow(iframeWindow);

        const messageHandler = event => handleMessage(event, iframeWindow);
        window.addEventListener('message', messageHandler);

        // 设置超时机制，如果5秒后iframe还未发送ready消息，则自动标记为准备就绪
        const readyTimeout = setTimeout(() => {
            if (!isIframeReady) {
                console.warn('iframe未发送ready消息，自动标记为准备就绪');
                isIframeReady = true;
                // 处理缓存的消息
                while (messageQueue.length > 0) {
                    const cachedMessage = messageQueue.shift();
                    handleIframeRequest(cachedMessage, iframeWindow);
                }
            }
        }, 5000);

        // 返回一个清理函数
        return () => {
            window.removeEventListener('message', messageHandler);
            clearTimeout(readyTimeout);
        };
    };

    /**
     * 手动设置iframe为准备就绪状态
     * @param {Window} [iframeWindow] iframe的Window对象（可选，不传则使用内部引用）
     */
    const setIframeReady = (iframeWindow = currentIframeWindow) => {
        // 如果传入了iframeWindow，更新内部引用
        if (iframeWindow) {
            setIframeWindow(iframeWindow);
        }

        if (!isIframeReady) {
            isIframeReady = true;
            // 处理缓存的消息
            while (messageQueue.length > 0) {
                const cachedMessage = messageQueue.shift();
                handleIframeRequest(cachedMessage, currentIframeWindow);
            }
        }
    };

    /**
     * 重置iframe服务状态
     */
    const reset = () => {
        messageQueue.length = 0; // 清空消息队列
        isIframeReady = false; // 重置准备状态
        currentIframeWindow = null; // 清空iframe窗口引用
    };

    return {
        handleIframeRequest,
        sendResponseToIframe,
        handleMessage,
        setupMessageListener,
        setIframeReady,
        setIframeWindow,
        getCurrentIframeWindow,
        reset
    };
}
