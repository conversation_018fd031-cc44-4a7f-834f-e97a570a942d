import { useTrackingStore } from '@/stores/tracking';

/**
 * 埋点服务
 * 专门处理各种埋点逻辑
 */
export function useTrackingService() {
    const trackingStore = useTrackingStore();

    /**
     * 发送bot选择埋点
     * @param {Object} bot - 选中的bot对象
     */
    const sendBotSelectTracking = async bot => {
        try {
            await trackingStore.reportUserEvent({
                eventType: `BOT_SELECT_${bot.id}`,
                eventDetail: `用户选择了bot: ${bot.name || 'unknown'}`
            });
        } catch (error) {
            console.error('bot选择埋点发送失败:', error);
        }
    };

    return {
        sendBotSelectTracking
    };
}
