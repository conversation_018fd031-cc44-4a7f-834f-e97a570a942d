import * as dd from 'dingtalk-jsapi';
import { useAppStore } from '@/stores/app';
const appStore = useAppStore();

/**
 * 获取上传文件的URL地址，根据不同环境返回不同的URL
 * @returns {string} 上传文件的URL地址
 */
function getUploadFileUrl() {
    const currentEnv = import.meta.env.MODE;
    const apiBaseUrl = import.meta.env.VITE_BASE_URL;

    if (currentEnv === 'production') {
        return `${apiBaseUrl}/api/file/uploadDingImage`;
    } else if (currentEnv === 'test') {
        return `${apiBaseUrl}/api/file/uploadDingImage`;
    } else {
        const fallbackBaseUrl = window.location.origin;
        const hostParts = fallbackBaseUrl.split(':');
        const host = hostParts[0] + ':' + hostParts[1];
        const url = `${host}:9002/api/file/uploadDingImage`;
        return url;
    }
}

/**
 * 钉钉桥接服务
 * 封装钉钉JSAPI的调用，提供友好的接口给其他组件和服务使用
 */
export function useDingTalkBridge() {
    /**
     * 获取钉钉授权码
     * @param {Object} options 选项
     * @param {string} [options.corpId] 企业ID，默认从环境变量获取
     * @param {string} [options.clientId] 应用ID，默认从环境变量获取
     * @returns {Promise<string>} 授权码
     */
    const getAuthCode = (options = {}) => {
        return new Promise((resolve, reject) => {
            try {
                const corpId = options.corpId || import.meta.env.VITE_CORP_ID;
                const clientId = options.clientId || import.meta.env.VITE_CLIENT_ID;

                dd.requestAuthCode({
                    corpId: corpId,
                    clientId: clientId,
                    success: (res) => {
                        const { code } = res;
                        resolve(code);
                    },
                    fail: (err) => {
                        reject(
                            new Error(
                                '获取授权码失败: ' + (err.errorMessage || JSON.stringify(err))
                            )
                        );
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    };

    /**
     * 获取位置信息
     * @param {Object} options 选项
     * @param {number} [options.accuracy=200] 定位精度
     * @param {boolean} [options.useCache=true] 是否使用缓存
     * @returns {Promise<Object>} 位置信息
     */
    const getLocation = (options = {}) => {
        return new Promise((resolve, reject) => {
            try {
                // 调用钉钉定位API
                dd.getLocation({
                    targetAccuracy: options.accuracy || 200,
                    coordinate: 1, // 1：获取高德坐标
                    withReGeocode: true, // 是否需要带有逆地理编码信息
                    useCache: options.useCache !== false, // 默认是true
                    success: function (result) {
                        // 构建响应数据
                        const locationData = {
                            location: {
                                address: `${result.province}-${result.city}-${result.address}`,
                                longitude: result.longitude,
                                latitude: result.latitude,
                                province: result.province,
                                city: result.city,
                                district: result.district,
                                streetName: result.streetName,
                                streetNumber: result.streetNumber
                            }
                        };

                        resolve(locationData);
                    },
                    fail: function (err) {
                        reject(new Error('定位失败: ' + (err.errorMessage || JSON.stringify(err))));
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    };

    /**
     * 上传文件
     * @param {Object} options 选项
     * @param {string} options.filePath 本地文件路径
     * @param {string} options.url 服务器上传地址
     * @param {Object} [options.data] 额外表单数据
     * @param {string} [options.fileName] 文件名，默认为file
     * @returns {Promise<Object>} 上传结果
     */
    const postUploadFile = async (options = {}) => {
        try {
            // 获取授权码
            let formData = options.data || {};
            try {
                const ticketResponse = await appStore.getTicket();
                formData = { ...formData, ticket: ticketResponse.result };
            } catch (authError) {
                throw new Error('获取授权码失败: ' + authError.message);
            }

            return new Promise((resolve, reject) => {
                // 调用钉钉上传API
                dd.uploadFile({
                    url: options.url || getUploadFileUrl(),
                    filePath: options.filePath,
                    fileName: options.fileName || 'file',
                    fileType: 'image',
                    header: {},
                    formData: formData,
                    success: function (result) {
                        // 解析返回的数据
                        try {
                            const data =
                                typeof result.data === 'string'
                                    ? JSON.parse(result.data)
                                    : result.data;

                            resolve({ data: data.result });
                        } catch (err) {
                            reject(
                                new Error(
                                    '上传文件失败: ' + (err.errorMessage || JSON.stringify(err))
                                )
                            );
                        }
                    },
                    fail: function (err) {
                        reject(
                            new Error('上传文件失败: ' + (err.errorMessage || JSON.stringify(err)))
                        );
                    }
                });
            });
        } catch (error) {
            return Promise.reject(error);
        }
    };

    /**
     * 拍照
     * @returns {Promise<Object>} 照片信息
     */
    const takePhoto = (options = {}) => {
        return new Promise((resolve, reject) => {
            try {
                // 调用钉钉拍照API
                dd.chooseImage({
                    count: options.count || 1,
                    sourceType: ['camera'],
                    position: options.position || 'back',
                    success: async function (result) {
                        try {
                            // 上传所有照片
                            const uploadPromises = result.filePaths.map((path, index) => {
                                return postUploadFile({
                                    filePath: path,
                                    url: getUploadFileUrl(),
                                    data: {
                                        _appId: 'aiwen',
                                        fileName: `file${index}`,
                                        fileType: 'image'
                                    },
                                    fileName: `file${index}`
                                });
                            });

                            // 等待所有上传完成
                            const uploadResults = await Promise.all(uploadPromises);

                            // 构建响应数据
                            const photoData = {
                                photos: uploadResults.map((uploadResult) => ({
                                    url: uploadResult.data,
                                    timestamp: Date.now()
                                }))
                            };

                            resolve(photoData);
                        } catch (uploadError) {
                            reject(new Error('照片上传失败: ' + uploadError.message));
                        }
                    },
                    fail: function (err) {
                        reject(new Error('拍照失败: ' + (err.errorMessage || JSON.stringify(err))));
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    };

    /**
     * 显示位置地图
     * @param {Object} options 选项
     * @param {number} options.longitude 经度
     * @param {number} options.latitude 纬度
     * @param {number} [options.scope=500] 地图范围
     * @returns {Promise<Object>} 位置信息
     */
    const showLocationInMap = (options = {}) => {
        return new Promise((resolve, reject) => {
            try {
                // 参数校验
                if (!options.longitude || !options.latitude) {
                    throw new Error('缺少有效的经纬度信息');
                }

                // 调用钉钉地图API
                dd.locateInMap({
                    scope: options.scope || 500,
                    longitude: options.longitude,
                    latitude: options.latitude,
                    success: function (result) {
                        // 构建位置信息响应
                        const locationData = {
                            location: {
                                address: `${result.province}-${result.city}-${result.adName}-${result.snippet}-${result.title}`,
                                longitude: result.longitude,
                                latitude: result.latitude,
                                province: result.province,
                                city: result.city,
                                adName: result.adName,
                                snippet: result.snippet,
                                title: result.title
                            }
                        };

                        resolve(locationData);
                    },
                    fail: function (err) {
                        reject(
                            new Error('打开地图失败: ' + (err.errorMessage || JSON.stringify(err)))
                        );
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    };

    /**
     * 开启语音录制
     * @param {Object} options 选项
     * @param {boolean} [options.autoTranscribe=false] 是否自动转写为文字
     * @returns {Promise<Object>} 语音录制结果
     */
    const openVoice = () => {
        return new Promise((resolve, reject) => {
            try {
                // 直接返回成功，实际录音操作将在宿主App中处理
                resolve();
            } catch (error) {
                reject(error);
            }
        });
    };

    return {
        getLocation,
        takePhoto,
        showLocationInMap,
        postUploadFile,
        getAuthCode,
        openVoice
    };
}
