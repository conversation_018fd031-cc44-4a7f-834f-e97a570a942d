/**
 * Ftwo 日志服务
 * 统一管理项目中所有 ftwo 相关的功能，避免分散调用
 */

/**
 * 检查 ftwo 是否可用
 * @returns {boolean} ftwo 是否可用
 */
const checkFtwoAvailability = () => {
    return !!(window.ftwo);
};

/**
 * 通用日志记录函数
 * @param {string} logLevel - 日志级别 (trace, info, warn, error, debug)
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
const logMessage = (logLevel, message, attr1, attr2, attr3) => {
    if (!checkFtwoAvailability()) {
        console[logLevel] || console.log(`ftwo 不可用, ${logLevel}:`, message, attr1, attr2, attr3);
        return;
    }

    try {
        // 使用 ftwo 原生日志方法
        if (window.ftwo.log[logLevel]) {
            window.ftwo.log[logLevel](message, attr1, attr2, attr3);
        } else {
            console.error(`ftwo 不支持 ${logLevel} 日志级别`);
        }
    } catch (err) {
        console.error(`ftwo 记录 ${logLevel} 日志失败:`, err);
    }
};

/**
 * 记录 trace 日志
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
export const logTrace = (message, attr1, attr2, attr3) => {
    logMessage('trace', message, attr1, attr2, attr3);
};

/**
 * 记录 info 日志
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
export const logInfo = (message, attr1, attr2, attr3) => {
    logMessage('info', message, attr1, attr2, attr3);
};

/**
 * 记录 warn 日志
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
export const logWarn = (message, attr1, attr2, attr3) => {
    logMessage('warn', message, attr1, attr2, attr3);
};

/**
 * 记录 error 日志
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
export const logError = (message, attr1, attr2, attr3) => {
    logMessage('error', message, attr1, attr2, attr3);
};

/**
 * 记录 debug 日志
 * @param {string} message - 日志消息
 * @param {*} [attr1] - 扩展属性1
 * @param {*} [attr2] - 扩展属性2
 * @param {*} [attr3] - 扩展属性3
 */
export const logDebug = (message, attr1, attr2, attr3) => {
    logMessage('debug', message, attr1, attr2, attr3);
};



// 默认导出所有函数
export default {
    logTrace,
    logInfo,
    logWarn,
    logError,
    logDebug
};
