<template>
    <div class="audio-test-container">
        <div class="header">
            <h1>音频播放测试工具</h1>
            <p>输入音频链接URL，测试是否能正常播放</p>
        </div>

        <div class="input-section">
            <InputText
                v-model="audioUrl"
                placeholder="请输入音频URL链接"
                class="w-full mb-3"
                @keyup.enter="testAudio"
            />
            <div class="button-group">
                <Button label="测试播放" @click="testAudio" :disabled="!audioUrl" class="mr-2" />
                <Button
                    label="停止"
                    @click="stopAudio"
                    :disabled="!isPlaying"
                    class="mr-2"
                    severity="secondary"
                />
                <Button label="清空" @click="clearInput" :disabled="!audioUrl" severity="danger" />
                <Button
                    label="测试震动"
                    @click="testVibration"
                    class="vibration-btn"
                    severity="info"
                />
                <Button
                    label="分享页面"
                    @click="sharePage"
                    class="share-btn ml-2"
                    severity="help"
                    icon="pi pi-share-alt"
                />
                <Button
                    label="测试拍照"
                    @click="testTakePhoto"
                    class="photo-btn ml-2"
                    severity="warning"
                    icon="pi pi-camera"
                />
            </div>
        </div>

        <div v-if="testAttempted" class="result-section">
            <div v-if="isLoading" class="loading">
                <ProgressSpinner style="width: 50px; height: 50px" />
                <span>加载中...</span>
            </div>
            <div v-else-if="error" class="error-message">
                <i class="pi pi-times-circle" style="font-size: 2rem; color: #f44336"></i>
                <h3>播放失败</h3>
                <p>{{ error }}</p>
                <div class="error-details">
                    <h4>可能的原因：</h4>
                    <ul>
                        <li>URL格式不正确</li>
                        <li>音频文件不存在或已被删除</li>
                        <li>服务器拒绝访问（CORS问题）</li>
                        <li>音频格式不受支持</li>
                        <li>网络连接问题</li>
                    </ul>
                </div>
            </div>
            <div v-else-if="isPlaying || hasPlayed" class="success-message">
                <i class="pi pi-check-circle" style="font-size: 2rem; color: #4caf50"></i>
                <h3>{{ isPlaying ? '正在播放' : '播放成功' }}</h3>
                <div class="audio-controls">
                    <div class="progress-bar">
                        <div class="progress" :style="{ width: `${progressPercentage}%` }"></div>
                    </div>
                    <div class="time-display">
                        <span>{{ formatTime(currentTime) }}</span>
                        <span>{{ formatTime(duration) }}</span>
                    </div>
                </div>
                <div class="audio-info">
                    <p><strong>音频URL:</strong> {{ audioUrl }}</p>
                    <p><strong>音频格式:</strong> {{ audioFormat || '未知' }}</p>
                    <p><strong>音频时长:</strong> {{ formatTime(duration) }}</p>
                </div>
            </div>
        </div>

        <div class="history-section" v-if="playHistory.length > 0">
            <h3>播放历史</h3>
            <ul>
                <li v-for="(item, index) in playHistory" :key="index" class="history-item">
                    <div class="history-url" @click="loadFromHistory(item.url)">
                        {{ item.url }}
                    </div>
                    <div class="history-status" :class="item.success ? 'success' : 'failure'">
                        {{ item.success ? '成功' : '失败' }}
                    </div>
                </li>
            </ul>
            <Button label="清空历史" @click="clearHistory" severity="secondary" class="mt-3" />
        </div>

        <!-- 隐藏的音频元素 -->
        <audio
            ref="audioPlayer"
            @canplay="onCanPlay"
            @error="onError"
            @timeupdate="onTimeUpdate"
            @ended="onEnded"
            type="audio/wav"
        ></audio>
    </div>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi';
import { ref, computed, onUnmounted } from 'vue';
import { useToast } from 'primevue/usetoast';

/**
 * 音频URL输入
 * @type {import('vue').Ref<string>}
 */
const audioUrl = ref('');

/**
 * 音频播放器引用
 * @type {import('vue').Ref<HTMLAudioElement|null>}
 */
const audioPlayer = ref(null);

/**
 * 是否正在播放
 * @type {import('vue').Ref<boolean>}
 */
const isPlaying = ref(false);

/**
 * 是否已经播放过
 * @type {import('vue').Ref<boolean>}
 */
const hasPlayed = ref(false);

/**
 * 是否正在加载
 * @type {import('vue').Ref<boolean>}
 */
const isLoading = ref(false);

/**
 * 错误信息
 * @type {import('vue').Ref<string|null>}
 */
const error = ref(null);

/**
 * 是否已尝试测试
 * @type {import('vue').Ref<boolean>}
 */
const testAttempted = ref(false);

/**
 * 音频当前播放时间（秒）
 * @type {import('vue').Ref<number>}
 */
const currentTime = ref(0);

/**
 * 音频总时长（秒）
 * @type {import('vue').Ref<number>}
 */
const duration = ref(0);

/**
 * 音频格式
 * @type {import('vue').Ref<string|null>}
 */
const audioFormat = ref(null);

/**
 * 播放历史记录
 * @type {import('vue').Ref<Array<{url: string, success: boolean, timestamp: number}>>}
 */
const playHistory = ref([]);

// 使用toast通知
const toast = useToast();

/**
 * 计算播放进度百分比
 * @type {import('vue').ComputedRef<number>}
 */
const progressPercentage = computed(() => {
    if (duration.value <= 0) {
        return 0;
    }
    return (currentTime.value / duration.value) * 100;
});

/**
 * 测试音频播放
 */
const testAudio = () => {
    if (!audioUrl.value) {
        return;
    }

    // 重置状态
    error.value = null;
    isLoading.value = true;
    testAttempted.value = true;
    hasPlayed.value = false;

    try {
        // 设置音频源
        if (audioPlayer.value) {
            audioPlayer.value.src = audioUrl.value;
            audioPlayer.value.load();

            // 尝试播放
            const playPromise = audioPlayer.value.play();

            if (playPromise !== undefined) {
                playPromise
                    .then(() => {
                        isPlaying.value = true;
                        detectAudioFormat();
                    })
                    .catch((err) => {
                        handlePlayError(err);
                    });
            }
        }
    } catch (err) {
        handlePlayError(err);
    }
};

/**
 * 处理播放错误
 * @param {Error} err - 错误对象
 */
const handlePlayError = (err) => {
    isLoading.value = false;
    isPlaying.value = false;
    error.value = `播放失败: ${err.message || '未知错误'}`;

    // 添加到历史记录
    addToHistory(false);

    toast.add({
        severity: 'error',
        summary: '播放失败',
        detail: error.value,
        life: 3000
    });
};

/**
 * 停止音频播放
 */
const stopAudio = () => {
    if (audioPlayer.value) {
        audioPlayer.value.pause();
        isPlaying.value = false;
    }
};

/**
 * 清空输入
 */
const clearInput = () => {
    audioUrl.value = '';
    stopAudio();
    testAttempted.value = false;
    error.value = null;
    currentTime.value = 0;
    duration.value = 0;
    audioFormat.value = null;
};

/**
 * 当音频可以播放时触发
 */
const onCanPlay = () => {
    isLoading.value = false;
    if (audioPlayer.value) {
        duration.value = audioPlayer.value.duration;
    }
};

/**
 * 当音频播放出错时触发
 * @param {Event} e - 错误事件
 */
const onError = (e) => {
    isLoading.value = false;
    isPlaying.value = false;

    let errorMessage = '未知错误';
    if (audioPlayer.value) {
        switch (audioPlayer.value.error?.code) {
        case 1:
            errorMessage = '获取过程被用户终止';
            break;
        case 2:
            errorMessage = '网络错误';
            break;
        case 3:
            errorMessage = '解码错误，可能是不支持的音频格式';
            break;
        case 4:
            errorMessage = '音频资源不可用或格式不受支持';
            break;
        default:
            errorMessage = '未知错误';
        }
    }

    error.value = errorMessage;

    // 添加到历史记录
    addToHistory(false);

    toast.add({
        severity: 'error',
        summary: '播放失败',
        detail: errorMessage,
        life: 3000
    });
};

/**
 * 当音频播放时间更新时触发
 */
const onTimeUpdate = () => {
    if (audioPlayer.value) {
        currentTime.value = audioPlayer.value.currentTime;
    }
};

/**
 * 当音频播放结束时触发
 */
const onEnded = () => {
    isPlaying.value = false;
    hasPlayed.value = true;
    currentTime.value = 0;

    // 添加到历史记录
    addToHistory(true);

    toast.add({
        severity: 'success',
        summary: '播放成功',
        detail: '音频已播放完成',
        life: 3000
    });
};

/**
 * 格式化时间为 mm:ss 格式
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间
 */
const formatTime = (seconds) => {
    if (isNaN(seconds) || !isFinite(seconds)) {
        return '00:00';
    }

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * 检测音频格式
 */
const detectAudioFormat = () => {
    const url = audioUrl.value;

    // 尝试从URL中获取扩展名
    const urlParts = url.split('?')[0]; // 移除查询参数
    const extension = urlParts.split('.').pop().toLowerCase();

    // 检查是否是有效的音频扩展名
    const validExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];

    if (validExtensions.includes(extension)) {
        audioFormat.value = extension.toUpperCase();
    } else {
        // 如果URL中没有有效扩展名，尝试从Content-Type获取
        if (audioPlayer.value && audioPlayer.value.src) {
            // 对于没有扩展名的URL，我们可以尝试从MIME类型推断
            // 但这需要在音频加载后获取
            try {
                // 修复逻辑错误，正确检测音频格式
                const mimeTypes = {
                    'audio/mpeg': 'MP3',
                    'audio/mp3': 'MP3',
                    'audio/wav': 'WAV',
                    'audio/x-wav': 'WAV',
                    'audio/ogg': 'OGG',
                    'audio/mp4': 'M4A',
                    'audio/aac': 'AAC'
                };

                let detectedFormat = '未知';

                // 遍历检查支持的格式
                for (const [mimeType, format] of Object.entries(mimeTypes)) {
                    if (
                        audioPlayer.value.canPlayType(mimeType) === 'probably' ||
                        audioPlayer.value.canPlayType(mimeType) === 'maybe'
                    ) {
                        detectedFormat = format;
                        break;
                    }
                }

                audioFormat.value = detectedFormat;
            } catch (e) {
                audioFormat.value = '未知';
                console.error('获取音频格式失败:', e);
            }
        } else {
            audioFormat.value = '未知';
        }
    }
};

/**
 * 添加到播放历史
 * @param {boolean} success - 是否播放成功
 */
const addToHistory = (success) => {
    // 检查是否已存在相同URL
    const existingIndex = playHistory.value.findIndex((item) => item.url === audioUrl.value);

    if (existingIndex !== -1) {
        // 更新现有记录
        playHistory.value[existingIndex] = {
            url: audioUrl.value,
            success,
            timestamp: Date.now()
        };
    } else {
        // 添加新记录
        playHistory.value.unshift({
            url: audioUrl.value,
            success,
            timestamp: Date.now()
        });

        // 限制历史记录数量
        if (playHistory.value.length > 10) {
            playHistory.value = playHistory.value.slice(0, 10);
        }
    }
};

/**
 * 从历史记录加载URL
 * @param {string} url - 要加载的URL
 */
const loadFromHistory = (url) => {
    audioUrl.value = url;
    testAudio();
};

/**
 * 清空历史记录
 */
const clearHistory = () => {
    playHistory.value = [];
};

/**
 * 测试钉钉震动功能
 * 调用钉钉JSAPI使设备震动
 */
const testVibration = () => {
    dd.device.notification.vibrate({
        duration: 300,
        onSuccess: () => {
            toast.add({
                severity: 'success',
                summary: '震动成功',
                detail: '设备已震动',
                life: 2000
            });
        }
    });
};

/**
 * 分享当前页面
 * 调用钉钉JSAPI实现页面分享功能
 */
const sharePage = () => {
    // 获取当前页面信息
    const pageTitle = '音频播放测试工具';
    const pageDesc = '一个用于测试音频播放功能的工具，支持多种音频格式';
    const pageUrl = window.location.href;

    // 调用钉钉分享API
    dd.share({
        url: pageUrl,
        type: 0,
        image: 'https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/logo464.png',
        title: pageTitle,
        content: pageDesc,
        success: () => {
            // 分享成功回调
        },
        fail: () => {
            // 分享失败回调
        },
        complete: () => {
            // 分享完成回调
        }
    });
};

/**
 * 测试钉钉拍照功能
 * 调用钉钉JSAPI打开相机拍照
 */
const testTakePhoto = () => {
    dd.biz.util.chooseImage({
        count: 1,
        secret: false,
        sourceType: ['camera'],
        position: 'front',
        compression: true, // 是否压缩
        quality: 80, // 图片质量，范围0-100
        resize: 600, // 图片缩放大小
        onSuccess: (result) => {
            // 拍照成功回调
            toast.add({
                severity: 'success',
                summary: '拍照成功',
                detail: '照片已获取',
                life: 3000
            });

            console.log(result, '拍完照 后的 result');

            // 显示拍摄的照片预览
            const previewImage = document.createElement('img');
            previewImage.src = result.filePaths[0]; // 照片base64数据
            previewImage.style.maxWidth = '100%';
            previewImage.style.borderRadius = '8px';
            previewImage.style.marginTop = '10px';

            // 创建预览容器
            const previewContainer = document.createElement('div');
            previewContainer.style.textAlign = 'center';
            previewContainer.style.marginTop = '20px';
            previewContainer.appendChild(previewImage);

            // 添加标题
            const previewTitle = document.createElement('h4');
            previewTitle.textContent = '拍照预览';
            previewTitle.style.marginBottom = '10px';
            previewContainer.insertBefore(previewTitle, previewImage);

            // 清除之前的预览
            const oldPreview = document.getElementById('photo-preview');
            if (oldPreview) {
                oldPreview.remove();
            }

            // 设置ID并添加到页面
            previewContainer.id = 'photo-preview';
            document.querySelector('.result-section').appendChild(previewContainer);
        },
        onFail: (err) => {
            // 拍照失败回调
            toast.add({
                severity: 'error',
                summary: '拍照失败',
                detail: `错误: ${err.errorMessage || '未知错误'}`,
                life: 3000
            });
        }
    });
};

// 组件卸载时停止播放
onUnmounted(() => {
    if (audioPlayer.value) {
        audioPlayer.value.pause();
    }
});
</script>

<style scoped>
.audio-test-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header {
    text-align: center;
    margin-bottom: 24px;
}

.header h1 {
    color: #1976d2;
    margin-bottom: 8px;
    font-size: 24px;
}

.header p {
    color: #666;
    margin: 0;
}

.input-section {
    margin-bottom: 24px;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.result-section {
    padding: 20px;
    border-radius: 8px;
    background-color: #f5f7fa;
    margin-bottom: 24px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.error-message,
.success-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.error-message h3,
.success-message h3 {
    margin: 12px 0;
    color: #f44336;
}

.success-message h3 {
    color: #4caf50;
}

.error-details {
    margin-top: 16px;
    text-align: left;
    width: 100%;
}

.error-details h4 {
    margin-bottom: 8px;
    color: #666;
}

.error-details ul {
    padding-left: 20px;
    margin: 0;
}

.error-details li {
    margin-bottom: 4px;
    color: #666;
}

.audio-controls {
    width: 100%;
    margin: 16px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress {
    height: 100%;
    background-color: #1976d2;
    border-radius: 4px;
    transition: width 0.2s;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.audio-info {
    width: 100%;
    text-align: left;
    margin-top: 16px;
    padding: 12px;
    background-color: #e8f0fe;
    border-radius: 4px;
}

.audio-info p {
    margin: 4px 0;
    font-size: 14px;
}

.history-section {
    margin-top: 24px;
}

.history-section h3 {
    margin-bottom: 12px;
    color: #333;
    font-size: 18px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}

.history-item:hover {
    background-color: #f5f5f5;
}

.history-url {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #1976d2;
}

.history-status {
    margin-left: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.history-status.success {
    background-color: #e8f5e9;
    color: #4caf50;
}

.history-status.failure {
    background-color: #ffebee;
    color: #f44336;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.success-message i,
.error-message i {
    animation: pulse 1.5s infinite;
}

.vibration-btn {
    position: relative;
    overflow: hidden;
}

.vibration-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.vibration-btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(20);
        opacity: 0;
    }
}

/* 动画效果 */
@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-2px);
    }
    20%,
    40%,
    60%,
    80% {
        transform: translateX(2px);
    }
}

.vibration-btn:active {
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

/* 分享按钮样式 */
.share-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 分享按钮点击动画 */
@keyframes pulse-share {
    0% {
        box-shadow: 0 0 0 0 rgba(103, 58, 183, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(103, 58, 183, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(103, 58, 183, 0);
    }
}

.share-btn:active {
    animation: pulse-share 0.8s ease-out;
}

/* 拍照按钮样式 */
.photo-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.photo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 拍照按钮点击动画 */
@keyframes flash {
    0% {
        background-color: rgba(255, 193, 7, 1);
    }
    50% {
        background-color: rgba(255, 255, 255, 0.8);
    }
    100% {
        background-color: rgba(255, 193, 7, 1);
    }
}

.photo-btn:active {
    animation: flash 0.3s ease-out;
}
</style>
