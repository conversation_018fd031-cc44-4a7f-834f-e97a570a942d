<template>
    <div class="error-handler-demo">
        <h2>🚨 错误处理演示</h2>
        <p>这个页面用于测试全局错误处理功能，只在开发环境显示。</p>

        <div class="demo-section">
            <h3>1. 未捕获的Promise拒绝</h3>
            <p>触发 unhandledrejection 事件</p>
            <Button
                @click="triggerUnhandledRejection"
                severity="danger"
                icon="pi pi-exclamation-triangle"
            >
                触发 Promise 拒绝
            </Button>
        </div>

        <div class="demo-section">
            <h3>2. JavaScript 错误</h3>
            <p>触发普通的JavaScript错误</p>
            <Button @click="triggerJavaScriptError" severity="danger" icon="pi pi-times-circle">
                触发 JS 错误
            </Button>
        </div>

        <div class="demo-section">
            <h3>3. Vue 组件错误</h3>
            <p>触发Vue组件内的错误</p>
            <Button @click="triggerVueError" severity="danger" icon="pi pi-exclamation-circle">
                触发 Vue 错误
            </Button>
        </div>

        <div class="demo-section">
            <h3>4. 手动错误捕获</h3>
            <p>使用 captureError 手动上报错误</p>
            <Button @click="triggerManualCapture" severity="warning" icon="pi pi-info-circle">
                手动捕获错误
            </Button>
        </div>

        <div class="demo-section">
            <h3>5. 网络请求错误</h3>
            <p>触发HTTP请求错误</p>
            <Button @click="triggerNetworkError" severity="danger" icon="pi pi-wifi">
                触发网络错误
            </Button>
        </div>

        <div class="tips">
            <h4>💡 使用提示：</h4>
            <ul>
                <li>打开控制台查看错误日志</li>
                <li>所有错误都会被统一收集和处理</li>
                <li>错误信息包含详细的上下文信息</li>
                <li>生产环境中可以对接错误监控服务</li>
            </ul>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Button from 'primevue/button';
import { captureError } from '@/utils/errorHandler';
import httpService from '@/utils/httpService';

// 只在开发环境显示此组件
const isDevelopment = import.meta.env.MODE === 'development';

onMounted(() => {
    if (!isDevelopment) {
        console.warn('ErrorHandlerDemo 组件只在开发环境下可用');
    }
});

// 触发未捕获的Promise拒绝
const triggerUnhandledRejection = () => {
    // 创建一个被拒绝的Promise，但不处理它
    new Promise((resolve, reject) => {
        setTimeout(() => {
            reject(new Error('这是一个未处理的Promise拒绝示例'));
        }, 100);
    });
    // 故意不加 .catch()
};

// 触发JavaScript错误
const triggerJavaScriptError = () => {
    setTimeout(() => {
        // 访问不存在的对象属性
        const obj = null;
        console.log(obj.someProperty.deepProperty);
    }, 100);
};

// 触发Vue错误
const triggerVueError = () => {
    // 在下次tick中抛出错误，这样会被Vue的错误处理捕获
    setTimeout(() => {
        throw new Error('这是一个Vue组件内的错误示例');
    }, 0);
};

// 手动捕获错误
const triggerManualCapture = () => {
    try {
        throw new Error('这是一个手动捕获的错误示例');
    } catch (error) {
        captureError(error, {
            component: 'ErrorHandlerDemo',
            action: 'manual_test',
            userAction: '用户点击了手动捕获按钮',
            additionalInfo: {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            }
        });
    }
};

// 触发网络错误
const triggerNetworkError = async () => {
    try {
        // 请求一个不存在的接口
        await httpService.get('/api/nonexistent-endpoint');
    } catch (error) {
        console.log('网络错误已被全局错误处理捕获:', error.message);
    }
};
</script>

<style scoped>
.error-handler-demo {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.demo-section {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
}

.demo-section h3 {
    margin-top: 0;
    color: #333;
}

.demo-section p {
    color: #666;
    margin-bottom: 10px;
}

.tips {
    margin-top: 30px;
    padding: 15px;
    background: #e3f2fd;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.tips h4 {
    margin-top: 0;
    color: #1976d2;
}

.tips ul {
    margin-bottom: 0;
}

.tips li {
    margin-bottom: 5px;
    color: #555;
}
</style>
