<template>
    <div class="shimmer-border-demo">
        <h2>ShimmerBorder 组件演示</h2>

        <div class="demo-section">
            <h3>圆形光效 (默认)</h3>
            <div class="demo-card">
                <div class="demo-item">
                    <ShimmerBorder>
                        <div class="content-placeholder">
                            <p>圆形光效 - 默认颜色</p>
                        </div>
                    </ShimmerBorder>
                </div>

                <div class="demo-item">
                    <ShimmerBorder
                        primaryColor="rgba(255, 87, 34, 0.6)"
                        secondaryColor="rgba(255, 193, 7, 0.6)"
                    >
                        <div class="content-placeholder">
                            <p>圆形光效 - 自定义颜色</p>
                        </div>
                    </ShimmerBorder>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>线性光效</h3>
            <div class="demo-card">
                <div class="demo-item">
                    <ShimmerBorder effectType="linear">
                        <div class="content-placeholder">
                            <p>线性光效 - 默认颜色</p>
                        </div>
                    </ShimmerBorder>
                </div>

                <div class="demo-item">
                    <ShimmerBorder
                        effectType="linear"
                        primaryColor="rgba(76, 175, 80, 0.6)"
                        secondaryColor="rgba(33, 150, 243, 0.6)"
                    >
                        <div class="content-placeholder">
                            <p>线性光效 - 自定义颜色</p>
                        </div>
                    </ShimmerBorder>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>自定义样式</h3>
            <div class="demo-card">
                <div class="demo-item">
                    <ShimmerBorder borderRadius="4px" :customStyle="{ border: '1px dashed #ccc' }">
                        <div class="content-placeholder">
                            <p>自定义边框样式</p>
                        </div>
                    </ShimmerBorder>
                </div>

                <div class="demo-item">
                    <ShimmerBorder :loading="false">
                        <div class="content-placeholder">
                            <p>禁用加载效果</p>
                        </div>
                    </ShimmerBorder>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import ShimmerBorder from './ShimmerBorder.vue';
</script>

<style lang="scss" scoped>
.shimmer-border-demo {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    h2 {
        margin-bottom: 30px;
        color: $label-primary;
        text-align: center;
    }

    .demo-section {
        margin-bottom: 40px;

        h3 {
            margin-bottom: 15px;
            color: $label-secondary;
            font-weight: 500;
        }

        .demo-card {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;

            .demo-item {
                flex: 1;
                min-width: 200px;
                height: 200px;
                position: relative;
                border-radius: 12px;
                overflow: hidden;

                .content-placeholder {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: $system-background-secondary;
                    z-index: 1;

                    p {
                        padding: 10px;
                        background-color: rgba(255, 255, 255, 0.8);
                        border-radius: 4px;
                        text-align: center;
                        font-size: 14px;
                        color: $label-primary;
                    }
                }
            }
        }
    }
}
</style>
