<template>
    <div class="detail-container">
        <!-- 页面内容 -->
        <div class="detail-content">
            <ScrollPanel
                class="custom-scroll-panel"
                :pt="{
                    bary: 'hover:bg-primary-400 bg-primary-300 opacity-100'
                }"
            >
                <div class="product-detail-new">
                    <!-- 顶部卡片 -->
                    <div v-if="requestDialogConfog.visible" class="product-detail-card">
                        <Skeleton width="30%" height="34px" class="card-name" />
                        <div class="card-tag tag_orange" style="width: 50%; margin-bottom: 10px">
                            <Skeleton width="20%" height="15px" class="tag-tit" />
                        </div>
                        <div class="card-tag tag_blue" style="width: 50%">
                            <Skeleton width="20%" height="15px" class="tag-tit" />
                        </div>
                        <Skeleton class="card-info" />
                    </div>
                    <div v-if="!requestDialogConfog.visible" class="product-detail-card">
                        <p class="card-name">{{ detailItem.prodName || '' }}</p>
                        <div class="card-tag tag_orange">
                            <span class="tag-tit">售卖</span>
                            <span class="tag-detail">{{ salesRange(detailItem) }}</span>
                        </div>
                        <div class="card-tag tag_blue">
                            <span class="tag-tit">产品线</span>
                            <span class="tag-detail">{{ detailItem.typeName || '' }}</span>
                        </div>
                        <p class="card-info" v-show="detailItem.sellingPoints">
                            {{ detailItem.sellingPoints || '' }}
                        </p>
                    </div>
                    <!-- 内容区域 -->
                    <!-- loading结束时的 页面有值时的场景 -->
                    <ul
                        v-if="!requestDialogConfog.visible && !isEmptyObject(systemFiles)"
                        class="detail-file-wrap"
                    >
                        <li v-for="(value, key) in systemFiles" :key="key">
                            <div class="detail-item-tit">
                                <i
                                    class="pi pi-file"
                                    style="font-size: 1.25rem; margin-right: 5px"
                                ></i>
                                {{ dictArr[key] }}
                            </div>
                            <div>
                                <div
                                    class="detail-file"
                                    v-for="(item, index) in value"
                                    :key="index"
                                    @click="handlePreviewDingLink(item)"
                                >
                                    <img :src="item.fileIcon" alt=" " class="file-img" />
                                    <div class="file-content">
                                        <p class="file-name">
                                            {{ item.fileName }}
                                        </p>
                                        <p class="file-date">
                                            发布时间：{{ formatDate(item.createTime) }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                    <!-- loading结束时的 页面无值时的场景 -->
                    <div
                        v-if="!requestDialogConfog.visible && isEmptyObject(systemFiles)"
                        class="no-data"
                    >
                        <img src="//x.autoimg.cn/dealer/crmapp/assets/img/out-data.png" alt=" " />
                        <p class="mb-3 mt-3">暂无相关数据</p>
                    </div>
                    <!-- loading时的 骨架屏 -->
                    <ul v-if="requestDialogConfog.visible" class="detail-file-wrap">
                        <li v-for="n in 1" :key="n">
                            <div class="detail-item-tit--skeleton">
                                <Skeleton width="100%" height="20px" />
                            </div>
                            <div class="detail-file" v-for="m in 4" :key="m">
                                <Skeleton width="34px" height="39px" class="file-img" />
                                <div class="file-content">
                                    <Skeleton width="80%" class="file-name" />
                                    <Skeleton width="40%" class="file-date" />
                                </div>
                            </div>
                        </li>
                    </ul>
                    <Loading :config="sendFileDialogConfig" />
                </div>
            </ScrollPanel>
        </div>

        <!-- 底部返回首页按钮 -->
        <div class="home-button-container">
            <Button icon="pi pi-home" label="返回首页" class="home-button" @click="goHome" />
        </div>
    </div>
    <ConfirmDialog
        group="headless"
        :pt="{
            root: {
                style: { width: '80%' }
            }
        }"
    >
        <template #container="{ message, acceptCallback, rejectCallback }">
            <div
                class="flex flex-column align-items-center p-3 surface-overlay border-round relative"
            >
                <i
                    class="pi pi-times absolute cursor-pointer"
                    style="top: 12px; right: 15px"
                    @click="
                        () => {
                            confirm.close();
                        }
                    "
                ></i>
                <div
                    class="border-circle bg-primary inline-flex justify-content-center align-items-center h-4rem w-4rem -mt-6"
                >
                    <i class="pi pi-cloud-download text-4xl"></i>
                </div>
                <span
                    class="font-bold block mb-1 mt-4"
                    style="color: #3b82f6; font-size: 14px; word-break: break-all"
                    >《{{ message.header }}》</span
                >
                <p class="mb-0">{{ message.message }}</p>
                <div class="flex align-items-center w-full mt-4">
                    <Button
                        label="预览"
                        outlined
                        @click="rejectCallback"
                        class="mr-3 w-6"
                        size="large"
                    />
                    <Button label="发送" @click="acceptCallback" size="large" class="w-6" />
                </div>
            </div>
        </template>
    </ConfirmDialog>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import * as dd from 'dingtalk-jsapi';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/stores/app';
import { fileIcon, isDingTalk } from '@/utils/index';
import { useRouterService } from '@/utils/routerService';
import dayjs from 'dayjs';
import Loading from '@/components/Loading.vue';
const appStore = useAppStore();
const route = useRoute();
const { navigateTo } = useRouterService();

// 支持emit事件
const emit = defineEmits(['route-change']);

// 支持props参数传递
const props = defineProps({
    id: {
        type: [String, Number],
        default: null
    },
    name: {
        type: String,
        default: ''
    },
    systemId: {
        type: [String, Number],
        default: null
    }
});

// 优先使用props，再从route.query获取
const prodId = computed(() => props.id || route.query.id);
const systemId = computed(() => props.systemId || route.query.systemId);
const prodName = computed(() => props.name || route.query.name);

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: prodName.value
    });
}

const formatDate = dateStr => {
    return dayjs(dateStr).format('YYYY/MM/DD');
};

const isEmptyObject = obj => {
    return Object.keys(obj).length === 0;
};
const salesRange = item => {
    if (!item.systems) {
        return '';
    }
    const sb = [];
    for (let { systemType, systemTypeName } of item.systems) {
        if (systemType === 1) {
            sb.push(
                '分站-' + item.areas.map(e => (e.areaName === '' ? '全国' : e.areaName)).join('/')
            );
        } else {
            sb.push(systemTypeName);
        }
    }
    return sb.join(',');
};

const dictArr = ref({});
const getDicDataArr = async () => {
    const params = { intDicID: 1134 };
    const data = await appStore.getDicData(params);
    if (data?.Result) {
        for (let { dicTabID, dicTabValue } of data.Result) {
            dictArr.value[dicTabID] = dicTabValue;
        }
    }
};

// 用户能够查看的系统
const userSystems = ref({});
const getUserSystems = async () => {
    const data = await appStore.getUserSystems();
    if (data?.Result) {
        data.Result.forEach(item => {
            userSystems.value[item.dicTabID] = item.dicTabValue;
        });
    }
};

// 请求详情页数据
const detailItem = ref({});
const files = ref(null);
const systemType = ref(1);

const initData = async () => {
    const params = {
        prodId: prodId.value
    };
    const data = await appStore.getProductDetail(params);

    if (!data?.Result) {
        return;
    }

    detailItem.value = data.Result;

    const body = {
        prodIds: [prodId.value]
    };
    const res = await appStore.getProductsFiles(body);

    if (!res.Result) {
        return;
    }

    files.value = res.Result;

    updateRequestDialogConfig('loaded', '请求完成', false);

    for (const [key, item] of Object.entries(systemTypes.value)) {
        /**
         * 如果有分站tab的内容，优先保留分站系统tab内容
         * 落地系统 保留分站tab-OK
         * 网销系统 保留网销tab
         * 集团系统 保留网销tab
         */

        if (item.systemType === 1 || Number(systemId.value) === 1) {
            systemType.value = 1;
            break;
        } else if (Number(systemId.value) === 2 || Number(systemId.value) === 4) {
            systemType.value = 2;
        }
    }
    tabSystemFiles();
};

// 切换系统的tab
const systemFiles = ref({});
const tabSystemFiles = () => {
    systemFiles.value = {};
    files.value.forEach(item => {
        if (item.systems.some(e => e.systemType === systemType.value)) {
            if (!systemFiles.value.hasOwnProperty(item.attachType)) {
                systemFiles.value[item.attachType] = [];
            }
            item.fileIcon = fileIcon(item.fileNameEx);
            systemFiles.value[item.attachType].push(item);
        }
    });
};

const systemTypes = computed(() => {
    const systems = {};
    if (!files.value) {
        return systems;
    }
    files.value.forEach(item => {
        for (let { systemType: itemSystemType, systemTypeName } of item.systems) {
            if (userSystems.value.hasOwnProperty(itemSystemType)) {
                systems[itemSystemType] =
                    itemSystemType === 1
                        ? { systemType: 1, systemTypeName: '分站' }
                        : { systemType: itemSystemType, systemTypeName };
            }
        }
    });
    return systems;
});

// 定义当前要操作的文件
const currentFile = ref(null);

// loading config
const sendFileDialogConfig = ref({
    visible: false,
    status: true,
    message: ''
});

const requestDialogConfog = ref({
    visible: false,
    status: true,
    message: ''
});

const updateSendDialogConfig = (status, message, visible = true) => {
    sendFileDialogConfig.value.status = status;
    sendFileDialogConfig.value.message = message;
    sendFileDialogConfig.value.visible = visible;
};

const updateRequestDialogConfig = (status, message, visible = true) => {
    requestDialogConfog.value.status = status;
    requestDialogConfog.value.message = message;
    requestDialogConfog.value.visible = visible;
};

const handlePreviewDingLink = async item => {
    currentFile.value = item;

    updateSendDialogConfig('loading', '请稍后...');

    const body = {
        fileName: currentFile.value.fileName,
        fileUrl: currentFile.value.url
    };

    try {
        const data = await appStore.getDingPreviewLink(body);

        if (data?.result && data?.returncode === 0) {
            updateSendDialogConfig('loaded', '预览成功', false);
            dd.openLink({
                url: data.result,
                success: function () {
                    // 预览链接打开成功
                },
                fail: function () {
                    updateSendDialogConfig('loaded', '预览失败, 请重试');
                }
            });
        } else {
            updateSendDialogConfig('failed', data.message);
        }
    } catch (error) {
        updateSendDialogConfig('failed', '预览失败，请稍后重试');
    }

    setTimeout(() => {
        sendFileDialogConfig.value.visible = false;
    }, 3000);
};

// 返回首页
const goHome = () => {
    navigateTo('/smart-crm/home');
};

onMounted(async () => {
    updateRequestDialogConfig('loading', '请稍后...');
    await getDicDataArr();
    await getUserSystems();
    initData();
});
</script>
<style lang="scss" scoped>
@use 'sass:color';

.detail-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: $system-grouped-background-primary;
}

.detail-content {
    flex: 1;
    overflow: hidden;
}

// 底部返回首页按钮样式
.home-button-container {
    width: 100%;
    padding: 15px;
    background-color: $system-background-primary;
    border-top: 1px solid $fill-color-quaternary;
}

.home-button {
    width: 100%;
    height: 44px;
    border-radius: 22px;
    background-color: $system-blue;
    border-color: $system-blue;

    &:hover,
    &:active {
        background-color: color.mix($system-blue, black, $weight: 95%);
        border-color: color.mix($system-blue, black, $weight: 95%);
    }
}

.custom-scroll-panel {
    height: 100%;
    overflow: hidden;
    background-color: $system-grouped-background-primary;
}

//  文字超出后以...显示 支持多行
@mixin fn-ellpisis($line) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    overflow: hidden;
}

//  点击区域背景色
@mixin high-light($opacity) {
    -webkit-tap-highlight-color: rgba(0, 0, 0, $opacity);
}

.product-detail-new {
    width: 100%;
    padding-bottom: 30px;

    .product-detail-card {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.05);
        border-radius: 14px;
        margin: 15px;
        position: relative;
        padding: 20px;
        background-color: $system-background-primary;
        background-image: linear-gradient(
            120deg,
            rgba($system-teal, 0.03) 0%,
            rgba($system-blue, 0.05) 100%
        );
        cursor: initial;

        .card-name {
            font-size: 22px;
            color: $label-primary;
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }

    .card-tag {
        display: flex;
        justify-content: start;
        align-items: center;
        max-width: 100%;
        font-size: 12px;
        border-radius: 15px;
        margin-bottom: 10px;
        padding: 2px 2px 2px 0;

        .tag-tit {
            border-radius: 13px;
            color: #fff;
            padding: 5px 10px;
            margin-right: 8px;
            font-weight: 500;
        }

        .tag-detail {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 8px;
        }

        &.tag_orange {
            color: $system-orange;
            background: rgba($system-orange, 0.1);

            .tag-tit {
                background: $system-orange;
            }
        }

        &.tag_blue {
            color: $system-blue;
            background: rgba($system-blue, 0.08);
            margin-bottom: 0;

            .tag-tit {
                background: $system-blue;
            }
        }
    }

    .card-info {
        font-size: 13px;
        color: $label-secondary;
        line-height: 1.5;
        margin-top: 12px;
        padding: 0;
        @include fn-ellpisis(3);
    }

    .card-date {
        font-size: 12px;
        color: $label-tertiary;
        line-height: 14px;
    }

    .detail-item-tit {
        display: inline-flex;
        align-items: center;
        font-size: 16px;
        color: $label-primary;
        font-weight: 600;
        padding: 15px 0 10px;
        position: relative;

        .pi {
            color: $system-blue;
        }
    }

    .detail-item-tit--skeleton {
        min-width: 86px;
        display: inline-flex;
        align-items: center;
        font-size: 16px;
        color: $label-secondary;
        font-weight: bold;
        padding: 10px 0 10px;
        position: relative;
    }

    .detail-item-tit::after {
        content: '';
        position: absolute;
        right: 0;
        bottom: 7px;
        height: 2px;
        width: 100%;
        background: linear-gradient(90deg, $system-blue 0%, rgba($system-blue, 0.2) 100%);
        border-radius: 2px;
    }

    .detail-file-wrap {
        padding: 0 15px;
        list-style: none;
        margin: 0 auto 30px;

        li {
            margin-bottom: 15px;
            background-color: $system-background-primary;
            border-radius: 14px;
            padding: 0 15px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
            overflow: hidden;
        }
    }

    .detail-file {
        border-bottom: 1px solid $fill-color-quaternary;
        display: flex;
        align-items: center;
        padding: 14px 5px;
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s ease;

        &:active {
            background-color: $system-grouped-background-secondary;
        }

        &:last-child {
            border-bottom: 0;
        }

        .file-img {
            display: block;
            width: 38px;
            height: 44px;
            margin-right: 12px;
            object-fit: contain;
        }

        .file-content {
            flex: 1;
            overflow: hidden;
            padding-right: 20px;
        }

        .file-name {
            font-size: 14px;
            color: $label-primary;
            font-weight: 500;
            margin-bottom: 6px;
            @include fn-ellpisis(2);
        }

        .file-date {
            font-size: 12px;
            color: $label-tertiary;
            height: 16px;
            line-height: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &::after {
            content: '';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 7px;
            height: 7px;
            border-top: 1.5px solid $system-gray2;
            border-right: 1.5px solid $system-gray2;
            transform: translateY(-50%) rotate(45deg);
        }
    }
}

.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 100px;

    p {
        color: $label-tertiary;
        font-size: 14px;
    }

    img {
        display: block;
        width: 40%;
        max-width: 160px;
        opacity: 0.7;
    }
}

// 确认对话框美化
:deep(.p-confirm-dialog) {
    .p-dialog-content {
        border-radius: 14px;
        padding: 0;
        background-color: $system-material-thick;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .pi-cloud-download {
        color: #fff;
    }

    .border-circle {
        background-color: $system-blue !important;
    }

    .p-button {
        border-radius: 20px;

        &:not(.p-button-outlined) {
            background-color: $system-blue;
            border-color: $system-blue;
        }

        &.p-button-outlined {
            color: $system-blue;
            border-color: $system-blue;
        }
    }

    .font-bold {
        color: $system-blue !important;
    }
}
</style>
