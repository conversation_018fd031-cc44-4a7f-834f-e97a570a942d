<template>
    <div class="product-container w-full h-full flex flex-column">
        <div class="header w-full flex align-items-center justify-content-between relative">
            <InputGroup class="search-input h-full w-full">
                <InputText
                    v-model="searchKey"
                    placeholder="请输入产品名称"
                    @update:modelValue="handleSearch"
                    @focus="handleFocus"
                />
                <Button icon="pi pi-search" @click="handleSearch" />
            </InputGroup>
        </div>
        <div class="product-filter-list">
            <span
                class="filter-item"
                :class="{ current: currentSystem }"
                @click="onShowFilterMenuClick(0)"
                >{{ currentSystem.label }}</span
            >
            <span
                class="filter-item"
                :class="{ current: selectedProductType }"
                @click="onShowFilterMenuClick(1)"
                >{{ selectedProductType.typeName }}</span
            >
        </div>
        <div v-show="showFilterMenu" class="product-nav-pop">
            <div class="visit-nav-mask" @click="handleMaskClick" />
            <!-- 选择系统 -->
            <div v-show="filterMenuType === 0" class="visit-nav-con">
                <div
                    v-for="item in userSystemsList"
                    :key="item.systemId"
                    :class="{
                        active: currentSystem.systemId === item.systemId
                    }"
                    @click="handleChangeSystem(item)"
                >
                    {{ item.label }}
                </div>
            </div>
            <!-- 产品线 -->
            <div v-show="filterMenuType === 1" class="visit-nav-con">
                <div
                    v-for="item in productTypes"
                    :key="item.typeId"
                    :class="{
                        active: selectedProductType.typeId === item.typeId
                    }"
                    @click="handleChangeType(item)"
                >
                    {{ item.typeName }}
                </div>
            </div>
        </div>
        <div class="content w-full flex">
            <ScrollPanel
                class="custom-scroll-panel"
                ref="scrollPanel"
                :pt="{
                    wrapper: {
                        style: {
                            'border-right': '5px solid var(--surface-ground)'
                        }
                    },
                    bary: 'hover:bg-primary-400 bg-primary-300 opacity-100',
                    content: 'custom-scroll-panel-content'
                }"
            >
                <div class="product-list-new w-full">
                    <!-- 骨架屏 -->
                    <div v-if="requestDialogConfig.visible">
                        <div v-for="n in 10" :key="n" class="list-item">
                            <div class="item-tit">
                                <Skeleton width="20%" height="23px" />
                            </div>
                            <div class="item-info flex">
                                <div class="img-box">
                                    <Skeleton width="100%" height="84px" />
                                </div>
                                <div class="line-content">
                                    <Skeleton width="70%" height="20px" class="line mb-3" />
                                    <Skeleton width="30%" height="20px" class="line mb-3" />
                                    <Skeleton width="80%" height="20px" class="line mb-1" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div
                            class="list-item"
                            v-for="(item, index) in procuctList"
                            :key="item.prodId"
                            @click="goProductDetail(item)"
                        >
                            <div class="item-tit">
                                <h3 class="name">
                                    {{ item.prodName }}
                                </h3>
                                <span class="tag tag_orange" v-show="item.businessCount > 0"
                                    >商机<em>{{ item.businessCount }}</em
                                    >个</span
                                >
                            </div>
                            <div class="item-info flex">
                                <div class="img-box">
                                    <img
                                        v-if="shouldLazyLoad(index)"
                                        v-lazy="formatLogo(item.logo)"
                                        alt=" "
                                    />
                                    <img v-else :src="formatLogo(item.logo)" alt=" " />
                                </div>
                                <div class="line-content">
                                    <div class="line">
                                        <span class="tit">售卖范围：</span
                                        >{{ formatSalesRange(item) }}
                                    </div>
                                    <div class="line">
                                        <span class="tit">产品线：</span>{{ item.typeName }}
                                    </div>
                                    <div class="line" v-show="item.sellingPoints">
                                        <span class="tit">产品卖点：</span>{{ item.sellingPoints }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--已经到底了s -->
                <div v-if="procuctList.length > 0 && !hasNextPage" class="no-data">
                    <p>没有更多了~</p>
                </div>
                <!--已经到底了e -->
                <!--没有数据s -->
                <div v-show="showNoData" class="messageCenter-nodata">
                    <img src="//x.autoimg.cn/dealer/crmapp/assets/img/out-data.png" alt=" " />
                    <span>暂无相关数据~</span>
                </div>
                <!--没有数据e -->
            </ScrollPanel>
        </div>
    </div>
</template>

<script setup>
import _ from 'lodash';
import { onMounted, ref, watch, onActivated } from 'vue';
import * as dd from 'dingtalk-jsapi';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/stores/app';
import { cutSchema, isDingTalk } from '@/utils/index';
import { useIntersectionObserver } from '@vueuse/core';
import { useRouterService } from '@/utils/routerService';

const scrollPanel = ref(null);
const appStore = useAppStore();
const router = useRouter();

const allApp = ref({
    1: {
        id: 1,
        systemId: 1,
        name: '落地系统',
        desc: '落地顾问团队使用',
        icon: 'fall',
        path: '/firstPage/chargeIndex',
        label: '落地系统'
    },
    2: {
        id: 2,
        systemId: 2,
        name: '网销系统',
        desc: '网销销售团队使用',
        icon: 'sell',
        path: '/wx/index',
        label: '网销系统'
    },
    5: {
        id: 5,
        systemId: 4,
        name: '集团系统',
        desc: '集团销售团队使用',
        icon: 'sell',
        path: '/group/index',
        label: '集团系统'
    }
});

const isAdmin = ref(false);
const userInfo = ref(null);

const getUserInfo = async () => {
    if (userInfo.value) {
        return;
    }

    try {
        const data = await appStore.getLoginSystems();
        if (data?.returncode === 0 && data?.result) {
            userInfo.value = data.result;
            handleAdmin();
        }
    } catch (error) {}
};

const handleAdmin = () => {
    isAdmin.value =
        userInfo.value && userInfo.value.sysRoleIndex && userInfo.value.sysRoleIndex === 4;
    handleUserSystemsList();
};

let currentSystem = ref({ label: '' });
const userSystemsList = ref([]);
// 创建用户系统列表。 默认展示第一个系统，如果出现多个系统， 则展示下拉框
const handleUserSystemsList = () => {
    if (userInfo.value?.sysRoleIndexList?.length) {
        if (isAdmin.value) {
            userSystemsList.value = Object.values(allApp.value);
        } else {
            userInfo.value.sysRoleIndexList.forEach(e => {
                if (e !== 4 && allApp.value.hasOwnProperty(e)) {
                    userSystemsList.value.push(allApp.value[e]);
                }
            });
        }
        currentSystem.value = userSystemsList.value[0];
    } else {
        router.replace('/noPermission');
    }
};

const handleChangeSystem = item => {
    currentSystem.value = item;
    showFilterMenu.value = false;
};

const resetProductTypes = ref(false);
watch(
    () => currentSystem.value,
    () => {
        resetProductTypes.value = true;
        selectedProductType.value = { typeId: null, typeName: '全部产品线' };
        getProduct();
    }
);

const productConfig = ref({
    pageIndex: 1,
    pageSize: 1000,
    isAll: 0,
    sellingStatus: 1
});

const getProduct = async () => {
    updateRequestDialogConfig('loading', '请稍后...');

    productConfig.value = {
        ...productConfig.value,
        systemIds: [currentSystem.value.systemId],
        typeId: selectedProductType.value.typeId
    };
    const data = await appStore.getProductList(productConfig.value);

    // 需要重置产品线 筛选条件
    if (resetProductTypes.value) {
        handleProductTypes(data);
        resetProductTypes.value = false;
    }

    handleProductList(data);
};

const filterMenuType = ref(0);
const showFilterMenu = ref(false);
const onShowFilterMenuClick = index => {
    filterMenuType.value = index;
    showFilterMenu.value = !showFilterMenu.value;
};

const handleMaskClick = () => {
    showFilterMenu.value = false;
};

// 产品线下拉框列表数据
const selectedProductType = ref({ typeId: null, typeName: '全部产品线' });
const productTypes = ref([]);
const handleProductTypes = data => {
    if (data.Result && data.Result.length > 0) {
        // 提取产品线数据
        let typeList = data.Result.map(e => {
            return { typeId: e.typeId, typeName: e.typeName };
        });
        productTypes.value = [];
        productTypes.value.push({ typeId: null, typeName: '全部产品线' });
        productTypes.value.push(..._.uniqWith(typeList, _.isEqual));
    }
};

const handleChangeType = item => {
    selectedProductType.value = item;
    showFilterMenu.value = false;
};

watch(
    () => selectedProductType.value,
    () => {
        getProduct();
    }
);

// 处理产品列表
const hasNextPage = ref(true);
const procuctList = ref([]);
const showNoData = ref(false);
const handleProductList = data => {
    showNoData.value = false;
    const { HasNextPage, CurrentPage, Result } = data;
    hasNextPage.value = HasNextPage;
    productConfig.value.pageIndex = CurrentPage;
    if (CurrentPage > 1) {
        if (Result) {
            procuctList.value.push(...Result);
        }
    } else {
        procuctList.value = Result || [];
    }

    showNoData.value = procuctList.value.length === 0;
    updateRequestDialogConfig('loaded', '请求完成', false);

    setTimeout(() => {
        scrollToTop();
    });
};

const formatSalesRange = item => {
    const sb = [];
    for (let { systemType, systemTypeName } of item.systems) {
        if (systemType === 1) {
            sb.push(
                '分站-' + item.areas.map(e => (e.areaName === '' ? '全国' : e.areaName)).join('/')
            );
        } else {
            sb.push(systemTypeName);
        }
    }
    return sb.join(',');
};

const formatLogo = url => {
    return cutSchema(url);
};

// 定义emit事件
const emit = defineEmits(['route-change']);

// 使用路由服务
const { navigateTo } = useRouterService();

// 跳转到对应的产品详情
const goProductDetail = ({ prodId, prodName }) => {
    navigateTo('/smart-crm/detail', {
        id: prodId,
        name: prodName,
        systemId: currentSystem.value.systemId
    });
};

// 处理input搜索
const searchKey = ref('');
const handleSearch = () => {
    productConfig.value.pName = searchKey.value;
    getProduct();
};

const handleFocus = () => {
    showFilterMenu.value = false;
};

// loading config
const requestDialogConfig = ref({
    visible: false,
    status: true,
    message: ''
});

const updateRequestDialogConfig = (status, message, visible = true) => {
    requestDialogConfig.value.status = status;
    requestDialogConfig.value.message = message;
    requestDialogConfig.value.visible = visible;
};

// 处理img懒加载 v-lazy
const shouldLazyLoad = index => {
    // 假设首屏显示10个项目，可以根据实际情况调整
    return index >= 10;
};

const vLazy = {
    beforeMount(el, binding) {
        const { stop } = useIntersectionObserver(el, ([{ isIntersecting }]) => {
            if (isIntersecting) {
                el.src = binding.value;
                stop();
            }
        });
    }
};

const scrollToTop = () => {
    if (document.querySelector('.custom-scroll-panel-content')) {
        document.querySelector('.custom-scroll-panel-content').scrollTop = 0;
    }
};

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '物料获取'
    });
}

onMounted(() => {
    getUserInfo();
});

onActivated(() => {
    if (!userInfo.value) {
        getUserInfo();
    }
});
</script>

<style lang="scss" scoped>
.product-container {
    height: 100vh; /* 确保容器占满整个视口高度 */

    .header {
        width: 100%;
        height: 54px;
        padding: 10px 15px;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 10;
        background-color: $system-background-primary;

        .search-input {
            :deep(.p-inputtext) {
                border-radius: 20px 0 0 20px;
                height: 40px;
                background-color: $system-background-secondary;
                border: none;
                font-size: 14px;
                padding-left: 15px;
                color: $label-primary;

                &::placeholder {
                    color: $label-tertiary;
                }

                &:focus {
                    box-shadow: none;
                    border-color: $system-blue;
                    background-color: $system-background-primary;
                }
            }

            :deep(.p-button) {
                border-radius: 0 20px 20px 0;
                height: 40px;
                background-color: $system-blue;
                border: none;

                &:focus {
                    box-shadow: none;
                }
            }
        }
    }

    .product-filter-list {
        height: 44px;
        font-size: 0;
        white-space: nowrap;
        display: flex;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        background-color: $system-background-primary;
        position: sticky;
        top: 0;
        z-index: 5;

        .filter-item {
            flex: 1;
            text-align: center;
            height: 44px;
            line-height: 44px;
            font-size: 14px;
            color: $label-secondary;
            position: relative;
            transition: all 0.2s ease;

            &:after {
                content: '';
                display: inline-block;
                width: 0;
                height: 0;
                border-right: 4px solid transparent;
                border-left: 4px solid transparent;
                border-top: 4px solid $system-gray3;
                vertical-align: middle;
                margin-left: 5px;
                margin-top: -2px;
                transition: transform 0.2s ease;
            }

            &.current {
                color: $system-blue;
                font-weight: 500;

                &:after {
                    border-top: 4px solid $system-blue;
                }
            }

            &.slideUp {
                color: $system-blue;

                &:after {
                    transform: rotate(180deg);
                    border-top: 4px solid $system-blue;
                }
            }
        }
    }

    .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background-color: $system-grouped-background-primary;

        .custom-scroll-panel {
            overflow: hidden;
            height: 100%;
        }
    }
}
</style>
<style lang="scss" scoped>
%ellipsis-basic {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.product-list-new {
    padding: 12px 15px;

    .list-item {
        display: block;
        box-sizing: border-box;
        border-radius: 12px;
        position: relative;
        padding: 16px;
        cursor: pointer;
        margin-bottom: 12px;
        background-color: $system-background-primary;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:active {
            transform: scale(0.98);
            background-color: $system-grouped-background-secondary;
        }

        .item-tit {
            display: flex;
            margin-bottom: 12px;

            .name {
                font-size: 16px;
                color: $label-primary;
                font-weight: 600;
                flex: 1;
                height: 23px;
                line-height: 23px;
                @extend %ellipsis-basic;
            }

            .tag {
                font-size: 12px;
                border-radius: 12px;
                margin: 2px 0 0 6px;
                height: 22px;
                line-height: 22px;
                padding: 0 8px;
                font-weight: 500;

                &.tag_orange {
                    background: rgba($system-orange, 0.1);
                    color: $system-orange;
                }

                &.tag_blue {
                    background: rgba($system-blue, 0.1);
                    color: $system-blue;
                }

                em {
                    font-style: normal;
                    font-weight: 600;
                    margin: 0 2px;
                }
            }
        }

        .item-info {
            position: relative;
            width: 100%;
            overflow: hidden;
            align-items: center;

            .img-box {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100px;
                height: 60px;
                margin-right: 12px;
                background-color: $system-grouped-background-primary;
                border-radius: 8px;
                overflow: hidden;

                img {
                    display: block;
                    max-width: 90%;
                    max-height: 90%;
                }
            }

            .line-content {
                height: 60px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex: 1;
                overflow: hidden;
            }

            .line {
                font-size: 13px;
                font-weight: 400;
                margin-bottom: 5px;
                color: $label-secondary;
                @extend %ellipsis-basic;
            }

            .line:last-child {
                margin-bottom: 0;
            }

            .tit {
                display: inline-block;
                min-width: 70px;
                color: $label-primary;
                font-weight: 500;
            }
        }
    }
}

.product-nav-pop {
    position: absolute;
    top: 100px;
    width: 100%;
    height: calc(100% - 100px);
    z-index: 10;

    .visit-nav-mask {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
        animation: fadeIn 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
    }

    .visit-nav-con {
        position: absolute;
        top: 0;
        width: 100%;
        background-color: $system-material-thick;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom-left-radius: 14px;
        border-bottom-right-radius: 14px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        padding: 6px 0;
        animation: slideDown 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);

        div {
            width: 100%;
            height: 44px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: $label-secondary;
            transition: all 0.2s ease;

            &:active {
                background-color: $fill-color-tertiary;
            }
        }

        div.active {
            color: $system-blue;
            font-weight: 500;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.no-data {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 13px;
    color: $label-tertiary;
    padding: 10px 0;
}

.messageCenter-nodata {
    width: 100%;
    padding-top: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
        width: 140px;
        margin-bottom: 20px;
        opacity: 0.7;
    }

    span {
        font-size: 14px;
        color: $label-tertiary;
    }
}
</style>
