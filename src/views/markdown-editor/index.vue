<template>
    <div class="markdown-editor-page">
        <h1 class="page-title">Markdown 编辑器</h1>
        <div class="editor-container">
            <MarkdownEditor v-model="mdContent" />
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import MarkdownEditor from '@/components/markdown/MarkdownEditor.vue';

// 初始 Markdown 内容
const mdContent = ref(`## Markdown 编辑器使用示例

欢迎使用 Markdown 编辑器，这是一个简单的示例。

### 基本语法

1. **粗体文本** 或 __粗体文本__
2. *斜体文本* 或 _斜体文本_
3. [链接文本](https://example.com)
4. ![图片描述](https://example.com/image.jpg)
5. \`代码\`

### 列表

有序列表：
1. 第一项
2. 第二项
3. 第三项

无序列表：
- 项目一
- 项目二
- 项目三

### 代码块

\`\`\`javascript
function hello() {
  console.log('Hello, World!');
}
\`\`\`

### 表格

| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25   | 北京 |
| 李四 | 30   | 上海 |
| 王五 | 28   | 广州 |

### 引用

> 这是一段引用文本。
> 
> 这是引用的第二段。

---

在左侧编辑区修改内容，右侧会实时显示渲染后的效果。`);
</script>

<style lang="scss" scoped>
.markdown-editor-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 16px;
    box-sizing: border-box;
    background-color: #f5f7fa;

    .page-title {
        font-size: 2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 16px;
        text-align: center;
    }

    .editor-container {
        flex: 1;
        width: 100%;
        min-height: 500px;
        overflow: hidden;
        border-radius: 10px;
    }
}

@media (max-width: 768px) {
    .markdown-editor-page {
        height: auto;
        min-height: 100vh;

        .page-title {
            font-size: 1.5rem;
            margin-bottom: 12px;
        }
    }
}
</style>
