<template>
    <div class="markdown-demo">
        <h1 class="page-title">Markdown组件演示</h1>
        <MarkdownViewer :content="markdownContent" />
    </div>
</template>

<script setup>
import { ref } from 'vue';
import MarkdownViewer from '@/components/common/MarkdownViewer.vue';

// Markdown示例内容
const markdownContent = ref(`
# AI ：更好的开发者，还是仅仅更快了？

它们确实能帮我们更快地写代码、解决问题，但这是否也在潜移默化地改变我们的思维方式和技能成长的路径

## 1. 效率提升：显而易见的好处

* **代码生成与补全**：以前可能需要半小时甚至更久的编码任务，现在可能几分钟就能搞定。
* **快速解决技术难题**：减少了在 掘金 或 Google 上漫无目的搜索的时间。
* **自动化琐碎工作**：比如生成文档、代码重构等，这些过去耗时的事情现在能更快完成。

从学习新领域的经历来说，AI 确实让学习曲线平缓了很多。遇到问题时，我可以直接描述场景，获取针对性的解决方案，而不必像以前那样翻阅大量文档和博客，大大降低了跨领域学习的门槛和畏难情绪。

## 2. 思维转变：潜在的依赖与改变

AI 正在悄悄改变人的思考习惯：

* 遇到问题，第一反应可能是“问问 AI”，而不是先自己深入思考。
* 有时会直接使用 AI 生成的代码，但对其背后的原理理解不够透彻。
* 问题解决后，容易直接进行下一步，缺乏复盘和反思，错失了深化理解的机会。

这种依赖是否正在弱化我们作为开发者的核心能力？

## 3. 学习模式：新的路径与陷阱

AI 也改变了我获取知识的方式：

* **交互式学习**：与 AI 对话让学习过程更个性化、反馈更及时。
* **知识面拓展**：能快速了解不同领域的技术，降低了尝试新技术的门槛。
* **示例驱动学习**：通过 AI 生成的高质量代码示例来学习最佳实践。

存在“学习陷阱”：快速获得知识和代码的便利性，容易让我产生“已经掌握”的错觉。当 AI 帮我解决了问题，表面上看任务完成了，但这种理解往往是浅层的。没有经过自己思考、消化和实践内化的知识，终究不牢固。只有当我能够不依赖 AI 独立解决类似问题时，才算真正掌握了。

## 4. 我的实践方法

经过一段时间的思考和调整，找到了一些对我有效的方法，力求在效率和深度学习之间找到平衡：

* **先思考，后求助**：遇到问题，给自己设定一个独立思考的时间（比如 5-15 分钟），尝试自己解决，实在不行再求助于 AI。
* **主动拆解问题**：将复杂问题分解，只在真正卡壳或者需要灵感的部分寻求 AI 帮助，而不是把整个任务丢给它。
* **追问原理**：不仅要解决方案，更要向 AI 提问“为什么”是这样解决的，理解背后的逻辑。
* **定期“戒断”**：可以尝试安排一些时间（比如每周一天或某个任务）完全不使用 AI 编程，检验和锻炼自己的独立编码能力。
* **复盘与批判性思考**：对 AI 提供的解决方案进行审视、理解、甚至重构，将其内化为自己的知识。

把 AI 看作是一个**经验丰富的顾问**，而不是替代者。想象一下，如果团队里有个技术大佬，你每次遇到问题都直接让他帮你写代码，虽然短期内效率很高，但长期来看，你自身的成长必然受限。更好的方式是：自己先思考尝试，带着初步方案和具体问题去请教，理解其思路，然后自己动手实现。这样，AI 工具才能真正成为学习的催化剂，而非技能发展的绊脚石。

## 写在最后

AI 工具就像一个放大镜，它既能放大我们的能力，也能放大我们的习惯——无论是好的还是坏的。关键不在于用不用 AI，而在于**如何有意识地去使用它**。

当我把 AI 视为学习伙伴而非单纯的“代码生成器”时，技术的最终目的是增强人的能力，而不是取代人的思考。在这个 AI 飞速发展的时代，保持好奇心、学习的热情和批判性思维，或许比掌握任何一项具体的技术更为重要。
`);
</script>

<style lang="scss" scoped>
.markdown-demo {
    padding: 16px;
    height: 100%;
    overflow-y: scroll;
    margin: 0 auto;

    .page-title {
        font-size: 2.5em;
        margin-bottom: 24px;
        color: #333;
        text-align: center;
    }
}
</style>
