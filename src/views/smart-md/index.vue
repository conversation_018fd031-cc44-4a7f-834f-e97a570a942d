<template>
    <div class="markdown-demo">
        <h1 class="page-title">Markdown组件演示</h1>
        <MarkdownViewer :content="markdownContent" />
    </div>
</template>

<script setup>
import { ref } from 'vue';
import MarkdownViewer from '@/components/common/MarkdownViewer.vue';

// Markdown示例内容
const markdownContent = ref(`
## 1. container.vue - 底部面板容器组件

### 对接优势：
- 使用Teleport实现了与DOM层级无关的渲染，可以将内容渲染到body下，避免嵌套环境中的样式和事件冒泡问题
- 提供了灵活的插槽机制，允许自定义面板内容和头部操作区
- 通过v-model双向绑定控制面板显示状态

### 技术亮点：
- 实现了自适应高度调整功能，支持通过拖拽调整面板高度
- 优秀的手势交互，同时支持鼠标和触摸事件
- 流畅的过渡动画，提升用户体验
- 支持全屏/半屏模式切换
- 精细化的事件处理和清理机制，防止内存泄漏

## 2. iframe.vue - 通用iframe加载组件

{
   "id": 6,
   "name": "智能问数",
   "avatar": "https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-data2.png",
   "defaulted": false,
   "elfType": 2,
   "elfUrl": "http://copilot-web.terra.corpautohome.com/smart-data?panelHeightPercent=0.95"
},

### 对接优势：
- 简化外部链接嵌入流程，提供统一的票据(ticket)处理机制
- 支持通过props传递自定义iframe属性
- 集成加载状态管理和错误处理

### 技术亮点：
- 自动处理票据(ticket)注入，实现与外部系统安全对接
- 集成了iframe与父窗口的双向通信机制
- 优雅的加载动画，使用ShimmerBorder提供视觉反馈
- CSS动画平滑过渡，提升用户体验
- 完善的资源清理机制，组件卸载时自动移除事件监听

## 3. common-iframe.vue - 消息内容iframe组件

### 对接优势：
- 专为消息内容展示设计，适合展示结构化内容
- 支持通过postMessage实现自适应高度调整
- 与整体消息流无缝集成

### 技术亮点：
- 基于Pinia的状态管理，与useIframeStore集成
- 智能高度自适应功能，iframe内容变化时自动调整高度
- 高度变化时使用平滑过渡动画
- 设计了优雅的加载过渡，使用ShimmerBorder提供加载状态反馈
- 完善的错误处理机制

## 4. router.vue - 面板内路由组件

{
   "id": 7,
   "name": "物料获取",
   "avatar": "https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-crm.png",
   "defaulted": false,
   "elfType": 3,
   "elfUrl": "/smart-crm?panelHeightPercent=1"
},

### 对接优势：
- 创建一个独立的路由系统，实现面板内多视图导航
- 支持懒加载组件，提升性能和用户体验
- 提供完整的路由历史管理

### 技术亮点：
- 实现了自定义路由系统，不依赖vue-router
- 使用异步组件和动态导入实现按需加载
- 维护内部历史堆栈，支持前进后退操作
- 组件间通信使用provide/inject，降低组件间耦合
- 支持组件切换间的上下文保持和参数传递

## 5. chat.vue - 内置知识库对话

{
   "id": 1,
   "name": "智能客服",
   "avatar": "https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-chat2.png",
   "defaulted": true,
   "elfType": 1,
   "elfUrl": null
},

## 综合技术亮点

1. **微前端架构支持**：
   - 采用iframe隔离方式，实现了不同业务模块间的独立部署和运行
   - 建立了完善的跨窗口通信机制，支持多种消息类型

2. **消息通信机制**：
   - 实现了基于postMessage的安全通信框架
   - 支持请求-响应模式，类似HTTP通信
   - 使用requestId确保消息的准确匹配和追踪

3. **高效状态管理**：
   - 使用Pinia进行状态管理，实现组件间高效数据共享
   - 通过useIframeStore管理iframe相关状态

4. **优化的性能考虑**：
   - 使用shallowRef优化大型组件引用性能
   - 资源按需加载和懒加载策略
   - 合理的事件绑定和清理机制，防止内存泄漏

5. **流畅的用户体验**：
   - 精心设计的加载状态和过渡动画
   - 高度自适应和平滑过渡效果
   - 统一的错误处理机制

6. **统一的安全机制**：
   - ticket票据自动注入，确保跨系统调用的安全性
   - 完善的错误处理和安全降级策略

这些组件共同构建了一个灵活且可扩展的面板系统，能够无缝集成各种业务模块，并提供一致的用户体验。同时，通过微前端架构设计，实现了业务隔离和独立发布，提高了开发效率和系统稳定性。
`);
</script>

<style lang="scss" scoped>
.markdown-demo {
    padding: 16px;
    height: 100%;
    overflow-y: scroll;
    margin: 0 auto;

    .page-title {
        font-size: 2.5em;
        margin-bottom: 24px;
        color: #333;
        text-align: center;
    }
}
</style>
