<template>
    <div class="markdown-demo">
        <h1 class="page-title">Markdown组件演示</h1>
        <MarkdownViewer :content="markdownContent" />
    </div>
</template>

<script setup>
import { ref } from 'vue';
import MarkdownViewer from '@/components/common/MarkdownViewer.vue';

// Markdown示例内容
const markdownContent = ref(`
# 录音组件技术亮点分析
1. **交互体验优化**
    - 多状态触控处理：实现了按住开始录音、上滑锁定、拖动取消等复杂触控交互
    - 精确的触摸区域识别：通过 getBoundingClientRect() 实现精确的触碰区域检测，增强用户体验
    - 防抖动与冷却机制：设置触摸时间阈值和录音冷却期，防止误触和重复操作
    - 振动反馈：在达到最大录制时长时触发振动，提供触觉反馈 。部分机型下不生效。
3. **视觉反馈系统**
    - 多层次动画效果：结合CSS动画和JS控制实现加载、录音过程、声波等丰富视觉效果
    - 声纳波效果：实现类似声纳的波纹动画，增强录音过程的视觉反馈
    - 倒计时警告：在接近最大录音时长时显示倒计时并改变颜色，提供时间压力提示
    - 过渡动画：使用 cubic-bezier 曲线实现流畅的UI过渡效果
4. **性能优化**
    - 资源预加载：预加载关键图片资源，减少使用过程中的加载延迟
    - 性能属性应用：使用 will-change、transform: translateZ(0)、backface-visibility: hidden 等优化动画性能
    - 计算属性优化：通过 computed 属性缓存计算结果，减少重复计算
    - 条件渲染：根据状态条件性展示UI元素，减少不必要的DOM渲染
5. **钉钉API集成**
    - 无缝对接钉钉录音API：深度集成钉钉的录音功能，支持启动、停止、获取录音结果
    - 异常处理机制：针对钉钉API可能出现的各种异常情况进行处理
    - 资源清理：在组件卸载时自动清理钉钉录音资源，防止内存泄漏
6. **用户体验设计**
    - 渐变与光影效果：使用多层次渐变和阴影，创造立体感和专业感
    - 自适应反馈：根据不同操作状态提供相应的视觉和文字提示
    - 优雅降级：在录音失败时提供友好的错误提示和自动恢复机制
    - 动态文本反馈：使用省略号动画效果增强加载状态的视觉体验

## 未来优化方向
1. **功能增强**
    - 语音识别实时反馈：集成实时语音识别，在录音过程中展示语音转文字结果
    - 音量可视化效果：添加实时音量波形图，增强录音过程中的视觉反馈
2. **用户体验优化**
    - 自定义主题支持：提供主题定制功能，适应不同产品风格
    - 无障碍支持：增加屏幕阅读器兼容性
4. **跨平台适配**
    - 微信小程序适配：开发对应的微信小程序版本，支持更多场景
    - Web端降级适配：为不支持高级特性的浏览器提供降级方案
5. **智能功能**
    - 情感分析：分析语音情感色彩，提供情感标签
`);
</script>

<style lang="scss" scoped>
.markdown-demo {
    padding: 16px;
    height: 100%;
    overflow-y: scroll;
    margin: 0 auto;

    .page-title {
        font-size: 2.5em;
        margin-bottom: 24px;
        color: #333;
        text-align: center;
    }
}
</style>
