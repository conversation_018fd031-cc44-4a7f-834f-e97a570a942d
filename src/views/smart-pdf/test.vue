<template>
    <div>
        <h1>测试</h1>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as dd from 'dingtalk-jsapi';

onMounted(() => {
    dd.downloadFile({
        url: 'https://festatic.corpautohome.com/source-file/64b63af8343ae99bcf2930a1/assets/chat-knowledge-h5/cdnjs/test.pdf',
        header: { 'content-type': 'application/pdf' },
        success: (res) => {
            const { filePath } = res;

            dd.openDocument({
                filePath: filePath,
                fileType: 'pdf'
            });
        },
        fail: () => {
            // 下载失败处理
        },
        complete: () => {
            // 下载完成处理
        }
    });
});
</script>
