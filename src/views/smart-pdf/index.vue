<template>
    <div class="pdf-viewer-container">
        <!-- PDF预览区域 -->
        <div class="pdf-content">
            <!-- 加载状态指示器 -->
            <div v-if="isLoading" class="pdf-loading">
                <ProgressSpinner />
                <p>加载中，请稍候...</p>
            </div>

            <!-- 错误提示 -->
            <div v-if="errorMessage" class="pdf-error">
                <i class="pi pi-exclamation-triangle"></i>
                <p>{{ errorMessage }}</p>
                <Button label="重试" @click="loadPdf" />
            </div>

            <!-- PDF渲染区域 -->
            <div v-show="!isLoading && !errorMessage" class="pdf-render-area">
                <VuePdfEmbed
                    ref="pdfViewer"
                    :source="pdfUrl"
                    :page="viewMode === 'paged' ? currentPage : null"
                    annotation-layer
                    text-layer
                    @loaded="onPdfLoaded"
                    @loading-failed="onPdfLoadingFailed"
                />
            </div>
        </div>

        <!-- 底部区域 -->
        <div class="pdf-footer">
            <!-- 页码信息 -->
            <div class="page-info">
                <span v-if="viewMode === 'paged'">{{ currentPage }} / {{ totalPages }}</span>
                <span v-else>共 {{ totalPages }} 页</span>
            </div>

            <!-- 功能按钮区域 -->
            <div class="footer-controls">
                <!-- 分页模式下的页码切换 -->
                <div v-if="viewMode === 'paged'" class="navigation-controls">
                    <Button
                        icon="pi pi-chevron-left"
                        :disabled="currentPage <= 1"
                        @click="prevPage"
                        class="nav-button"
                        aria-label="上一页"
                    />
                    <Button
                        icon="pi pi-chevron-right"
                        :disabled="currentPage >= totalPages"
                        @click="nextPage"
                        class="nav-button"
                        aria-label="下一页"
                    />
                </div>
                <!-- 视图模式切换 -->
                <Button
                    :icon="viewMode === 'paged' ? 'pi pi-file' : 'pi pi-th-large'"
                    :class="['mode-button', { active: viewMode === 'tiled' }]"
                    @click="toggleViewMode"
                    class="p-button-outlined p-button-sm"
                    :aria-label="viewMode === 'paged' ? '切换到平铺模式' : '切换到分页模式'"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import VuePdfEmbed from 'vue-pdf-embed';
// 导入可选的样式
import 'vue-pdf-embed/dist/styles/annotationLayer.css';
import 'vue-pdf-embed/dist/styles/textLayer.css';

// 组件状态
const isLoading = ref(true);
const errorMessage = ref('');
const currentPage = ref(1);
const totalPages = ref(0);
const layoutReady = ref(false);
const viewMode = ref('tiled'); // 默认为分页模式
const pdfViewer = ref(null);

// 测试用的PDF链接
const pdfUrl = '/trainer-api/file/ai_trainer/18.pdf';

/**
 * PDF加载事件处理函数
 * @param {Object} doc - PDF文档代理对象
 */
function onPdfLoaded(doc) {
    totalPages.value = doc.numPages;
    // 计算PDF显示尺寸并完成加载
    nextTick(() => {
        layoutReady.value = true;
        isLoading.value = false;
        errorMessage.value = '';
    });
}

/**
 * PDF加载失败事件处理函数
 * @param {Error} error - 错误对象
 */
function onPdfLoadingFailed(error) {
    console.error('加载PDF时出错:', error);
    errorMessage.value = '加载PDF文件失败，请检查网络连接后重试';
    isLoading.value = false;
}

/**
 * 加载PDF文件
 */
function loadPdf() {
    isLoading.value = true;
    errorMessage.value = '';
    layoutReady.value = false;
}

/**
 * 跳转到下一页
 */
function nextPage() {
    if (currentPage.value < totalPages.value) {
        currentPage.value++;
    }
}

/**
 * 跳转到上一页
 */
function prevPage() {
    if (currentPage.value > 1) {
        currentPage.value--;
    }
}

/**
 * 切换视图模式
 */
function toggleViewMode() {
    viewMode.value = viewMode.value === 'paged' ? 'tiled' : 'paged';
}

// 监听视图模式变化
watch(viewMode, (newMode) => {
    // 从平铺切换到分页时，确保当前页面是可见的
    if (newMode === 'paged' && currentPage.value > totalPages.value) {
        currentPage.value = 1;
    }
});

/**
 * 组件挂载后初始化
 */
onMounted(() => {
    // 加载PDF
    loadPdf();
});
</script>

<style lang="scss" scoped>
.pdf-viewer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pdf-footer {
    padding: 12px 15px;
    background-color: $system-blue;
    color: $system-background-primary;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-info {
    font-size: 14px;
    font-weight: 500;
}

.footer-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.navigation-controls {
    display: flex;
    gap: 8px;

    .nav-button {
        width: 32px;
        height: 32px;
        color: $system-background-primary;
        border-color: rgba(255, 255, 255, 0.5);

        &:disabled {
            opacity: 0.5;
        }

        &:hover:not(:disabled) {
            background-color: rgba(255, 255, 255, 0.3);
        }
    }
}

.mode-button {
    width: 32px;
    height: 32px;
    color: $system-background-primary;
    border-color: rgba(255, 255, 255, 0.5);

    &.active {
        background-color: rgba(255, 255, 255, 0.2);
    }

    &:hover {
        background-color: rgba(255, 255, 255, 0.3);
    }
}

.pdf-content {
    flex: 1;
    overflow: auto;
}

.pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    p {
        margin-top: 10px;
        color: $label-secondary;
    }
}

.pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;

    i {
        font-size: 32px;
        color: $system-red;
        margin-bottom: 10px;
    }

    p {
        margin-bottom: 15px;
        color: $label-secondary;
    }
}

.pdf-render-area {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    overflow: hidden;
    overflow-y: scroll;
}

.vue-pdf-embed {
    width: 100%;
    margin: auto 0;
}
</style>
