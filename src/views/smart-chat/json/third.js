// 品牌数据mock
export const mockBrandData = {
    returncode: 0,
    message: 'success',
    data: {
        list: [
            {
                id: 425,
                name: '岚图汽车',
                firstLetter: 'L',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g4/M03/84/E0/100x100_f40_autohomecar__ChwElmEbbwWAFV-VAAANGBjR7HM397.png',
                maxRebate: 1088,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 597,
                name: '东风奕派',
                firstLetter: 'D',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g30/M08/63/48/100x100_f40_autohomecar__CjIFU2UBIcaAfxVEAAB9jQfEFn0585.png',
                maxRebate: 500,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 575,
                name: '吉利银河',
                firstLetter: 'J',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g30/M03/EE/E1/100x100_f40_autohomecar__ChxknGQRKCKAZDQCAAAJLxGjmWo651.png',
                maxRebate: 500,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 306,
                name: 'SRM鑫源',
                firstLetter: 'X',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g25/M01/2E/37/100x100_f40_autohomecar__ChxkqWStAZmAX_U5AACDxsyMTlo647.png',
                maxRebate: 400,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 33,
                name: '奥迪',
                firstLetter: 'A',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g28/M01/F6/71/100x100_f40_autohomecar__CjIFVGTIoWqAdlH5AABfFDX2Peo393.png',
                maxRebate: 200,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 25,
                name: '吉利汽车',
                firstLetter: 'J',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g28/M01/14/02/100x100_f40_autohomecar__ChxkmmTCDuGAHSjSAABF02FAqfo394.png',
                maxRebate: 500,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 319,
                name: '捷途',
                firstLetter: 'J',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g30/M07/66/63/100x100_f40_autohomecar__ChwFlGLftKiAI_l0AAAk8uRgeAo452.png',
                maxRebate: 1000,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 63,
                name: '日产',
                firstLetter: 'R',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g28/M03/0B/A6/100x100_f40_autohomecar__ChxkmmS_YSyABzW1AACF5KOWKG4298.png',
                maxRebate: 1600,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            },
            {
                id: 83,
                name: '金杯',
                firstLetter: 'J',
                brandImg:
                    '//m1.autoimg.cn/cardfs/series/g25/M09/E7/69/100x100_f40_autohomecar__ChtliGTCB3eAds7mAAIgvFKP1KU751.png',
                maxRebate: 400,
                isUserPortrait: 0,
                isMain: 0,
                extendMap: null
            }
        ]
    }
};

// 城市数据mock
export const mockCityData = {
    returncode: 0,
    message: 'success',
    data: {
        list: [
            { id: 'beijing', name: '北京', firstLetter: 'B', cityImg: '' },
            { id: 'shanghai', name: '上海', firstLetter: 'S', cityImg: '' },
            { id: 'guangzhou', name: '广州', firstLetter: 'G', cityImg: '' },
            { id: 'shenzhen', name: '深圳', firstLetter: 'S', cityImg: '' },
            { id: 'tianjin', name: '天津', firstLetter: 'T', cityImg: '' },
            { id: 'chongqing', name: '重庆', firstLetter: 'C', cityImg: '' }
        ]
    }
};
