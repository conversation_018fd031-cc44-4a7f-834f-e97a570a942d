[{"type": "text", "content": "帮我查询下："}, {"type": "inline_option", "id": "brand", "placeholder": "品牌", "action": {"type": "OPEN_SIDEBAR", "sidebar_config": {"sidebar_type": "brand_selector", "title": "请选择品牌", "data_source": {"type": "api", "url": "/smart-data-api2/v2/Base/Brand_GetAllBrands.ashx", "method": "GET", "response_mapping": {"list_path": "result.branditems", "label_key": "name", "value_key": "id"}}}}}, {"type": "text", "content": "在"}, {"type": "inline_option", "id": "city", "placeholder": "城市", "action": {"type": "OPEN_SIDEBAR", "sidebar_config": {"sidebar_type": "cascade_selector", "title": "请选择城市", "cascade": {"enabled": true, "levels": [{"title": "请选择省份", "data_source": {"type": "api", "url": "/smart-data-api/sys/common/getProvinceList", "method": "GET", "response_mapping": {"list_path": "result", "label_key": "province", "value_key": "provinceId"}}}, {"title": "请选择城市", "data_source": {"type": "api", "url": "/smart-data-api/sys/common/getCityList", "method": "GET", "params_mapping": {"provinceId": "parent_value"}, "response_mapping": {"list_path": "result", "label_key": "city", "value_key": "cityId"}}}]}}}}, {"type": "inline_option", "id": "date", "placeholder": "时间周期", "action": {"type": "OPEN_MODAL", "modal_config": {"modal_type": "date_range_picker", "title": "请选择时间周期"}}}, {"type": "text", "content": "的销量情况"}]