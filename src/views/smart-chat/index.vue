<template>
    <div class="home-wrap">
        <!-- 路由出口 -->
        <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
                <component :is="Component" />
            </transition>
        </router-view>
    </div>
</template>
<script setup>
</script>

<style lang="scss" scoped>
.home-wrap {
    width: 100%;
    height: 100%;
    position: relative;
    font-size: 15px;
    overflow: hidden;
}

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
