<template>
    <div class="thinking-process">
        <div class="thinking-expanded">
            <div class="thinking-header" @click="showThinking = !showThinking">
                <div class="header-left">
                    <div class="status-indicator">
                        <template v-if="!isFinished">
                            <img
                                class="loading-icon"
                                src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/loading.png"
                                alt="thinking"
                            />
                            <span class="thinking-text">深度思考中</span>
                        </template>
                        <template v-else>
                            <span class="thinked-text">已深度思考</span>
                        </template>
                    </div>
                    <i :class="['pi', showThinking ? 'pi-chevron-up' : 'pi-chevron-down']"></i>
                </div>
            </div>
            <ReferenceButton
                v-if="referenceCount > 0 && showThinking"
                :count="referenceCount"
                @click="$emit('showReference')"
            />
            <div class="thinking-content-container" v-if="showThinking">
                <div class="thinking-content-inner">
                    <div
                        class="thinking-content markdown-body"
                        v-html="renderMarkdown(thinkContent)"
                    ></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { md, fixMarkdownCodeBlocks } from '@/utils/chat';
import { ref } from 'vue';
import ReferenceButton from '@/views/smart-chat/components/reference/ReferenceButton.vue';

const props = defineProps({
    thinkContent: {
        type: String,
        required: true,
        default: ''
    },
    isFinished: {
        type: Boolean,
        default: false
    },
    referenceCount: {
        type: Number,
        default: 0
    },
    finishedImageUrl: {
        type: String,
        default: ''
    }
});

defineEmits(['showReference']);

const showThinking = ref(true);

const renderMarkdown = (markdownContent) => {
    return md.render(fixMarkdownCodeBlocks(markdownContent));
};
</script>

<style lang="scss">
.thinking-process {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 12px;
    font-size: 12px;
    font-weight: 500;

    .thinking-expanded {
        background-color: transparent;
        border-radius: 0;
        width: 100%;
        border: none;
        box-shadow: none;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .thinking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: transparent;
            color: $system-gray;
            position: relative;
            z-index: 2;

            .header-left {
                width: 100%;
                justify-content: space-between;
                display: flex;
                align-items: center;
                .status-indicator {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    color: $system-gray;
                    font-size: 14px;

                    .loading-icon {
                        width: 20px;
                        height: 20px;
                        animation: spin 1.5s linear infinite;
                    }
                }
            }
        }

        .thinking-content-container {
            position: relative;
            padding: 0 0 0 16px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 2px;
                background-color: rgba(0, 0, 0, 0.1);
                border-radius: 1px;
            }

            .thinking-content {
                font-size: 13px;
                color: $system-gray;

                &.markdown-body {
                    font-size: 13px !important;
                    background-color: transparent !important;
                    color: $system-gray !important;
                }
            }
        }
    }
}

.thinking-text,
.thinked-text {
    position: relative;
    color: $system-blue !important;
    padding-right: 3px;
}

.thinking-text::after {
    content: '...';
    position: absolute;
    right: -12px;
    animation: ellipsis-anim 1.5s infinite;
    width: 12px;
    text-align: left;
}

@keyframes ellipsis-anim {
    0% {
        content: '';
    }
    25% {
        content: '.';
    }
    50% {
        content: '..';
    }
    75% {
        content: '...';
    }
    100% {
        content: '';
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
