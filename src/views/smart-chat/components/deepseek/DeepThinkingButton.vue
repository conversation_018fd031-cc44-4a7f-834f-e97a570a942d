<template>
    <div
        class="deep-thinking-button"
        :class="{ 'deep-thinking-button--active': chatStore.deepThinking }"
        @click="toggleDeepThinking"
    >
        <img
            v-if="chatStore.deepThinking"
            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/deepseek-white.png"
            alt="deepseek icon"
            class="deep-thinking-icon"
        />
        <img
            v-else
            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/deepseek-gray.png"
            alt="deepseek icon"
            class="deep-thinking-icon"
        />
        <span>深度思考</span>
    </div>
</template>

<script setup>
import useChatStore from '@/stores/chat';

const chatStore = useChatStore();

const toggleDeepThinking = () => {
    chatStore.setDeepThinking(!chatStore.deepThinking);
};
</script>

<style lang="scss" scoped>
.deep-thinking-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    height: 26px;
    border-radius: 13px;
    background-color: $system-gray5;
    font-size: 12px;
    color: $system-gray;
    cursor: pointer;
    transition: all 0.2s ease;
    width: fit-content;
    gap: 4px;

    .deep-thinking-icon {
        width: 20px;
    }

    &--active {
        background-color: $system-blue;
        color: $system-background-primary;
    }

    &:hover {
        opacity: 0.9;
    }
}
</style>
