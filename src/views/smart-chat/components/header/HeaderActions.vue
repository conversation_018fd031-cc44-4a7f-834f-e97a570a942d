<template>
    <div class="header-container">
        <!-- 左侧新会话按钮 -->
        <div
            class="new-chat-section"
            @click="$emit('newChat')"
            :class="{ 'new-chat-section--disabled': !selectedBot }"
        >
            <i class="pi pi-plus plus-icon"></i>
            <span class="new-chat-text">新会话</span>
        </div>

        <!-- 右侧功能按钮 -->
        <div class="actions-section">
            <div class="history-btn" @click="$emit('openHistory')">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/history2.png"
                    alt="历史会话"
                />
                <span>历史会话</span>
            </div>
        </div>
    </div>
</template>

<script setup>
defineEmits(['openHistory', 'newChat']);

defineProps({
    selectedBot: {
        type: Object,
        default: null
    }
});
</script>

<style lang="scss" scoped>
.header-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 48px;
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    z-index: 100;
}

.new-chat-section {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 10px;
    border-radius: 16px;
    background-color: rgba(10, 122, 255, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid $system-blue;

    &:active {
        transform: scale(0.98);
        background-color: rgba(10, 122, 255, 0.15);
    }

    .plus-icon {
        font-size: 12px;
        height: 18px;
        font-weight: 500;
        color: $system-blue;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .new-chat-text {
        font-size: 13px;
        font-weight: 500;
        color: $system-blue;
    }

    &--disabled {
        opacity: 0.9;
        background-color: $system-gray6;
        cursor: default;
        border: 1px solid $system-gray6;

        .plus-icon,
        .new-chat-text {
            color: $label-tertiary;
        }

        &:active {
            transform: none;
            background-color: $system-gray6;
        }
    }
}

.actions-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .history-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 10px;
        border: 1px solid $system-gray5;
        border-radius: 16px;
        font-size: 13px;
        color: $label-secondary;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
            background-color: $system-gray6;
            transform: scale(0.98);
        }

        img {
            width: 18px;
            height: 18px;
        }
    }
}
</style>
