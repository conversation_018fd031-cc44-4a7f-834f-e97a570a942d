<template>
    <div class="reference-questions">
        <div class="reference-header">
            <div class="reference-title">你可以这样问我：</div>
        </div>
        <div class="questions-container">
            <div
                class="question-item"
                v-for="(item, index) in botConfig?.presets || []"
                :key="index"
                @click="handleSelectQuestion(item)"
            >
                <div class="question-content">
                    <div class="question-body">
                        <div class="question-text">{{ item.desc }}</div>
                    </div>
                    <div class="question-arrow">
                        <i class="pi pi-arrow-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getBotConfig } from '@/config/botConfig';

const props = defineProps({
    botId: {
        type: [String, Number],
        default: ''
    }
});

const emit = defineEmits(['sendDirectly']);

// 根据 botId 获取当前机器人配置
const botConfig = computed(() => getBotConfig(props.botId));

// 处理问题选择
const handleSelectQuestion = (item) => {
    // 直接发送消息，而不是填充到输入框
    emit('sendDirectly', item.desc);
};
</script>

<style lang="scss" scoped>
@use 'sass:color';

.reference-questions {
    width: 100%;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: $label-secondary;

    .reference-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding-left: 4px;

        .reference-title {
            font-weight: 600;
            color: $label-primary;
            font-size: 15px;
            background: linear-gradient(90deg, $system-blue, $system-teal);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 0.3px;
        }
    }

    .questions-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .question-item {
        background-color: $system-background-primary;
        border-radius: 12px;
        transition: all 0.25s cubic-bezier(0.22, 1, 0.36, 1);
        cursor: pointer;
        width: fit-content;
        box-sizing: border-box;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        will-change: transform;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-color: rgba(0, 0, 0, 0.08);
        }

        &:active {
            transform: scale(0.98);
            background-color: $system-grouped-background-tertiary;
        }

        .question-content {
            padding: 10px 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            overflow: hidden;

            .question-body {
                flex: 1;
                min-width: 0;
                display: flex;
                flex-direction: column;

                .question-text {
                    color: $label-primary;
                    font-weight: 500;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    line-height: 1.4;
                }
            }

            .question-arrow {
                color: $system-blue;
                margin-left: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: transform 0.2s ease;
                flex-shrink: 0;
                width: 16px;
                height: 16px;

                i {
                    font-size: 12px;
                }
            }
        }

        &:hover .question-arrow {
            transform: translateX(3px);
            color: color.mix($system-blue, black, $weight: 95%);
        }
    }
}
</style>
