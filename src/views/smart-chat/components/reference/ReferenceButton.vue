<template>
    <div class="reference-trigger" @click="$emit('click')">
        <img
            class="icon-placeholder"
            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-crm.png"
        />
        <span>{{ count }} 条数据来源</span>
    </div>
</template>

<script setup>
defineProps({
    count: {
        type: Number,
        required: true
    }
});

defineEmits(['click']);
</script>

<style lang="scss" scoped>
.reference-trigger {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 3px 10px;
    color: $label-secondary;
    font-size: 12px;
    border: 1px solid rgba($label-secondary, 0.5);
    font-weight: 600;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: fit-content;

    &:active {
        opacity: 0.8;
        transform: scale(0.98);
    }

    .icon-placeholder {
        width: 18px;
        height: 18px;
    }

    span {
        line-height: 1.2;
        letter-spacing: 0.2px;
        color: $label-secondary;
    }
}
</style>
