<template>
    <div class="reference-questions-home">
        <div class="welcome-message" v-if="botConfig">
            <div class="welcome-title">{{ botConfig.welcomeTitle }}</div>
            <div class="welcome-subtitle">{{ botConfig.welcomeSubtitle }}</div>
        </div>

        <!-- 使用网格布局展示问题卡片 -->
        <div class="questions-grid">
            <div
                class="question-card"
                v-for="(item, index) in botConfig?.presets || []"
                :key="index"
                @click="handleSelectQuestion(item)"
            >
                <div class="question-icon" v-if="item.iconUrl">
                    <img :src="item.iconUrl" class="question-preset-icon" />
                </div>
                <div class="question-content">
                    <div class="question-title">{{ item.desc }}</div>
                    <div class="question-description" v-if="item.description">
                        {{ item.description }}
                    </div>
                </div>
                <div class="question-extra" v-if="item.tags && item.tags.length">
                    <div class="question-tags">
                        <span
                            v-for="(tag, tagIndex) in item.tags"
                            :key="tagIndex"
                            class="question-tag"
                        >
                            {{ tag }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getBotConfig } from '@/config/botConfig';

const props = defineProps({
    botId: {
        type: [String, Number],
        default: ''
    }
});

const emit = defineEmits(['sendDirectly']);

// 根据 botId 获取当前机器人配置
const botConfig = computed(() => getBotConfig(props.botId));

// 处理问题选择
const handleSelectQuestion = (item) => {
    emit('sendDirectly', item.desc);
};
</script>

<style lang="scss" scoped>
.reference-questions-home {
    width: 100%;
    font-size: 14px;
    font-weight: 500;

    .welcome-message {
        padding: 18px 20px;
        border-radius: 16px;
        background-color: $system-background-primary;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.03);
        margin-bottom: 20px;

        .welcome-title {
            font-weight: 700;
            font-size: 17px;
            margin-bottom: 10px;
            letter-spacing: -0.3px;
            background: linear-gradient(90deg, $system-blue, $system-teal);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .welcome-subtitle {
            font-weight: 500;
            font-size: 14px;
            color: $label-secondary;
            line-height: 1.5;
        }
    }

    // 网格布局
    .questions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr); // 每行两个卡片
        gap: 12px;
        padding: 0 4px;
    }

    // 问题卡片样式
    .question-card {
        background-color: $system-background-primary;
        border-radius: 14px;
        padding: 14px;
        transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        cursor: pointer;
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid rgba(0, 0, 0, 0.03);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
        will-change: transform;
        position: relative;
        overflow: hidden;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }

        &:active {
            transform: scale(0.98);
            background-color: $system-grouped-background-tertiary;
        }

        // 问题图标
        .question-icon {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        // 问题内容
        .question-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        // 问题标题
        .question-title {
            color: $label-primary;
            font-weight: 600;
            font-size: 14px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        // 问题描述
        .question-description {
            color: $label-secondary;
            font-size: 12px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        // 图标样式
        .question-preset-icon {
            width: 22px;
            height: 22px;
        }

        // 标签样式
        .question-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;

            .question-tag {
                background-color: rgba($system-blue, 0.1);
                color: $system-blue;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 8px;
                font-weight: 500;
                white-space: nowrap;
            }
        }

        // 添加伪元素作为右下角装饰
        &::after {
            content: '';
            position: absolute;
            right: -10px;
            bottom: -10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba($system-blue, 0.1);
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        &:hover::after {
            transform: scale(1.2);
            opacity: 0.8;
        }
    }

    // 针对小屏幕设备进行响应式调整
    @media screen and (max-width: 360px) {
        .questions-grid {
            grid-template-columns: 1fr; // 在小屏幕上切换为单列
        }
    }
}
</style>
