<template>
    <div class="reference-drawer" :class="{ 'reference-drawer--active': modelValue }">
        <!-- 遮罩层 -->
        <div class="drawer-mask" @click="handleClose"></div>

        <!-- 抽屉内容 -->
        <div class="drawer-content">
            <!-- 抽屉头部 -->
            <div class="drawer-header">
                <div class="header-content">
                    <h3>{{ references.length }}条数据来源</h3>
                    <p class="header-desc">以下是AI回答的参考知识来源</p>
                </div>
                <i class="pi pi-times" @click="handleClose"></i>
            </div>

            <!-- 抽屉内容区 -->
            <div class="drawer-body">
                <div class="reference-list">
                    <div
                        v-for="(item, index) in references"
                        :key="index"
                        class="reference-item"
                        :class="{
                            'reference-item--expanded': expandedMap[index],
                        }"
                    >
                        <!-- 参考项头部 -->
                        <div class="reference-header" @click="toggleExpand(index)">
                            <div class="reference-title">
                                <span class="reference-number">{{ index + 1 }}</span>
                                <span class="title">{{ item.title }}</span>
                            </div>
                            <i
                                class="pi"
                                :class="[expandedMap[index] ? 'pi-chevron-up' : 'pi-chevron-down']"
                            ></i>
                        </div>

                        <!-- 参考项内容 -->
                        <div class="reference-content" v-show="expandedMap[index]">
                            <div
                                v-if="item.content"
                                class="markdown-body"
                                v-html="renderMarkdown(item.content)"
                            ></div>
                            <div v-else class="no-content">暂无内容</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { md } from '@/utils/chat';

// Props 定义
const props = defineProps({
    modelValue: Boolean,
    references: {
        type: Array,
        default: () => []
    }
});

// Emits 定义
const emit = defineEmits(['update:modelValue']);

// 展开状态管理
const expandedMap = ref({});

// 初始化时展开所有项
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal && props.references.length > 0) {
            // 将所有项设置为展开状态
            props.references.forEach((_, index) => {
                expandedMap.value[index] = true;
            });
        }
    },
    { immediate: true }
);

// 处理关闭
const handleClose = () => {
    emit('update:modelValue', false);
};

// 切换展开状态
const toggleExpand = (index) => {
    expandedMap.value[index] = !expandedMap.value[index];
};

// Markdown 渲染
const renderMarkdown = (content) => {
    return content ? md.render(content) : '';
};
</script>

<style lang="scss" scoped>
.reference-drawer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    visibility: hidden;

    .drawer-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        opacity: 0;
        transition: opacity 0.3s ease;
        backdrop-filter: blur(3px);
    }

    .drawer-content {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        height: 65vh;
        background: $system-background-primary;
        border-radius: 24px 24px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        display: flex;
        flex-direction: column;
        box-shadow: 0 -4px 24px rgba(0, 0, 0, 0.12);
        will-change: transform;

        .drawer-header {
            position: relative;
            padding: 18px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            background: $system-background-primary;

            .header-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            h3 {
                font-size: 17px;
                font-weight: 700;
                margin: 0;
                background: linear-gradient(90deg, $system-blue, $system-teal);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                position: relative;
            }

            .header-desc {
                margin: 8px 0 0;
                font-size: 13px;
                color: $label-secondary;
                font-weight: 500;
            }

            i {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
                font-size: 18px;
                color: $label-secondary;
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.2s ease;
                background-color: $system-background-secondary;

                &:active {
                    background-color: $system-gray5;
                    transform: translateY(-50%) scale(0.95);
                }
            }
        }

        .drawer-body {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            background-color: $system-grouped-background-primary;

            .reference-list {
                .reference-item {
                    margin-bottom: 14px;
                    background: $system-background-primary;
                    border-radius: 16px;
                    overflow: hidden;
                    transition: all 0.3s ease;
                    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05);
                    border: 1px solid rgba(0, 0, 0, 0.03);

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &--expanded {
                        background-color: $system-background-primary;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
                    }

                    .reference-header {
                        padding: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        cursor: pointer;
                        transition: all 0.2s ease;

                        &:active {
                            transform: scale(0.98);
                        }

                        .reference-title {
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            flex: 1;
                            overflow: hidden;

                            .reference-number {
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                background: linear-gradient(135deg, $system-blue, $system-teal);
                                color: white;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 14px;
                                font-weight: 600;
                                flex-shrink: 0;
                                box-shadow: 0 2px 6px rgba(10, 122, 255, 0.2);
                            }

                            .title {
                                font-size: 15px;
                                font-weight: 600;
                                color: $label-primary;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                        }

                        i {
                            font-size: 16px;
                            color: $system-blue;
                            transition: transform 0.3s ease;
                        }
                    }

                    .reference-content {
                        padding: 0 16px 16px;
                        border-top: 1px solid $separator-color-opaque;
                        margin-top: -1px;

                        .markdown-body {
                            font-size: 14px;
                            line-height: 1.6;
                            color: $label-primary;
                            word-break: break-word;
                            padding-top: 12px;
                        }

                        .no-content {
                            text-align: center;
                            padding: 16px 0;
                            color: $label-tertiary;
                            font-style: italic;
                            font-size: 14px;
                        }
                    }
                }
            }
        }
    }

    &--active {
        visibility: visible;

        .drawer-mask {
            opacity: 1;
        }

        .drawer-content {
            transform: translateY(0);
        }
    }
}
</style>
