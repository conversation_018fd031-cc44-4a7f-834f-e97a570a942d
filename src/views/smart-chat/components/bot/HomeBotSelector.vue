<template>
    <div class="home-bot-selector">
        <!-- 常用功能快捷区 -->
        <div class="quick-access-section">
            <div class="section-title">
                <i class="pi pi-star-fill title-icon"></i>
                <span>常用功能</span>
            </div>
            <div class="quick-bots">
                <div
                    v-for="bot in quickAccessBots"
                    :key="bot.id"
                    class="quick-bot-item"
                    @click="handleBotClick(bot)"
                >
                    <div class="bot-icon-wrapper">
                        <img :src="bot.avatar" alt="" class="bot-icon" />
                    </div>
                    <span class="bot-name">{{ bot.name }}</span>
                </div>
            </div>
        </div>

        <!-- 中台功能展示区 -->
        <div class="platform-section">
            <div class="section-title">
                <i class="pi pi-th-large title-icon"></i>
                <span>功能中台</span>
            </div>
            <div class="platform-bots">
                <div
                    v-for="bot in platformBots"
                    :key="bot.id"
                    class="platform-bot-item"
                    @click="handleBotClick(bot)"
                >
                    <div class="bot-avatar-wrapper">
                        <img :src="bot.avatar" alt="" class="bot-avatar" />
                    </div>
                    <div class="bot-info">
                        <span class="bot-name">{{ bot.name }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    // bot列表数据
    bots: {
        type: Array,
        default: () => []
    }
});

const emits = defineEmits(['bot-click']);

// 常用功能的botId列表
const quickAccessBotIds = [1, 4, 15];

// 计算属性：常用功能bot列表
const quickAccessBots = computed(() => {
    return props.bots.filter(bot => quickAccessBotIds.includes(bot.id));
});

// 计算属性：中台功能bot列表（排除常用功能的bot）
const platformBots = computed(() => {
    return props.bots.filter(bot => !quickAccessBotIds.includes(bot.id));
});

/**
 * 处理bot点击事件
 * @param {Object} bot - 被点击的bot对象
 */
const handleBotClick = bot => {
    emits('bot-click', bot);
};
</script>

<style lang="scss" scoped>
.home-bot-selector {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 节标题样式 */
.section-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 600;
    color: $label-primary;
    margin-bottom: 8px;
    padding: 0 2px;
    letter-spacing: -0.2px;

    .title-icon {
        font-size: 14px;
        color: $system-blue;
    }
}

/* 常用功能区特殊样式 - 使用系统色变量的蓝色渐变 */
.quick-access-section .section-title {
    background: linear-gradient(135deg, $system-blue, $system-teal);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: -0.3px;

    .title-icon {
        background: linear-gradient(135deg, $system-blue, $system-teal);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 14px;
    }
}

/* 常用功能快捷区 - 使用系统色的渐变卡片设计 */
.quick-access-section {
    background: linear-gradient(135deg, rgba($system-blue, 0.08), rgba($system-teal, 0.04));
    border-radius: 14px;
    padding: 8px;
    border: 1px solid rgba($system-blue, 0.12);
    backdrop-filter: blur(10px);

    .quick-bots {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;

        .quick-bot-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 8px 6px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
            border: 1px solid rgba($system-blue, 0.15);
            position: relative;
            overflow: hidden;
            min-height: 60px;
            box-shadow: 0 2px 8px rgba($system-blue, 0.08);
            background: rgba($system-background-primary, 0.5);

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, $system-blue, $system-teal);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(
                    135deg,
                    rgba($system-blue, 0.05),
                    rgba($system-teal, 0.02)
                );
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:active {
                transform: scale(0.95);
                transition: transform 0.1s ease;
            }

            &:hover {
                transform: translateY(-6px);
                border-color: rgba($system-blue, 0.35);
                box-shadow: 0 8px 25px rgba($system-blue, 0.15), 0 4px 12px rgba(0, 0, 0, 0.05);

                &::before {
                    opacity: 1;
                }

                &::after {
                    opacity: 1;
                }
            }

            .bot-icon-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 30px;
                border-radius: 8px;
                transition: all 0.3s ease;
                position: relative;
                z-index: 1;

                .bot-icon {
                    width: 30px;
                    height: 30px;
                    filter: drop-shadow(0 1px 4px rgba($system-blue, 0.2));
                }
            }

            .bot-name {
                font-size: 13px;
                color: $label-primary;
                font-weight: 600;
                text-align: center;
                line-height: 1.2;
                position: relative;
                z-index: 1;
            }
        }
    }
}

/* 中台功能展示区 */
.platform-section {
    padding: 6px 2px;

    .platform-bots {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .platform-bot-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 6px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            position: relative;
            background: rgba($system-background-primary, 0.6);
            backdrop-filter: blur(8px);
            border: 1px solid $system-gray3;
            box-shadow: 0 1px 4px rgba($system-blue, 0.05);
            min-height: 64px;

            &:hover {
                background: rgba($system-background-primary, 0.9);
                backdrop-filter: blur(12px);
                box-shadow: 0 4px 16px rgba($system-blue, 0.12), 0 2px 8px rgba($system-blue, 0.06);
                transform: translateY(-2px);
                border-color: rgba($system-blue, 0.2);

                .bot-info .bot-name {
                    color: $system-blue;
                }
            }

            &:active {
                transform: translateY(0) scale(0.98);
            }

            .bot-avatar-wrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 36px;
                flex-shrink: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                .bot-avatar {
                    width: 36px;
                    height: 36px;
                    transition: all 0.3s ease;
                }
            }

            .bot-info {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .bot-name {
                    font-size: 13px;
                    color: $label-primary;
                    font-weight: 600;
                    transition: color 0.3s ease;
                    line-height: 1.2;
                }
            }
        }
    }
}
</style>
