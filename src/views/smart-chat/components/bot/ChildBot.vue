<template>
    <div class="child-bot" @click.stop="selectChildBot">
        <div class="child-bot-inner">
            <div class="bot-avatar" v-if="bot.avatar">
                <img :src="bot.avatar" alt="" />
            </div>
            <span class="bot-name">{{ bot.name }}</span>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    bot: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['selectChildBot']);

// 选择子bot - 改变选中状态，并将bot名称作为消息准备发送
const selectChildBot = () => {
    emit('selectChildBot', props.bot);
};
</script>

<style lang="scss" scoped>
.child-bot {
    position: relative;
    display: flex;
    flex-shrink: 0;
    border-radius: 16px;
    background: $system-background-primary;
    box-shadow: 0 2px 8px rgba(60, 60, 67, 0.1);
    cursor: pointer;
    overflow: hidden;
    min-width: fit-content;
    animation: slideDown 0.3s ease forwards;
    opacity: 0;
    transform: translateZ(0);
    will-change: transform, opacity;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    background-image: linear-gradient($system-background-primary, $system-background-primary),
        linear-gradient(135deg, rgba($system-purple, 0.5), rgba($system-blue, 0.5));
    background-origin: border-box;
    background-clip: padding-box, border-box;

    &-inner {
        display: flex;
        align-items: center;
        padding: 6px 10px;
        gap: 6px;
    }

    .bot-avatar {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.8);
            background-color: white;
        }
    }

    .bot-name {
        color: $label-primary;
        font-size: 12px;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    &:active {
        transform: scale(0.97);
        box-shadow: 0 1px 4px rgba(60, 60, 67, 0.05);
        background-image: linear-gradient(rgba($system-indigo, 0.08), rgba($system-indigo, 0.08)),
            linear-gradient(135deg, rgba($system-indigo, 0.7), rgba($system-blue, 0.7));

        .bot-name {
            color: #fff;
        }
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
