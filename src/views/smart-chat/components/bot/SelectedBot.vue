<template>
    <div class="parent-bot-container">
        <!-- 子bot列表 - 移动到父bot上方 -->
        <div
            class="child-bots-container"
            v-if="childBots.length > 0"
            :class="{ 'mobile-scroll': !isPcPlatform, 'pc-scroll': isPcPlatform }"
        >
            <ChildBot
                v-for="childBot in childBots"
                :key="childBot.id"
                :bot="childBot"
                @select-child-bot="handleSelectChildBot"
            />
        </div>
        <div class="parent-bot" @click.stop>
            <div class="parent-bot-inner">
                <div class="bot-avatar">
                    <img :src="bot.avatar" alt="" />
                </div>
                <span class="bot-name">当前位置：{{ bot.name }}</span>
                <i class="pi pi-times-circle delete-btn" @click.stop="removeKnowledgeBase"></i>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import useChatStore from '@/stores/chat';
import ChildBot from './ChildBot.vue';
import { detectDingTalkPlatform, DINGTALK_PLATFORM } from '@/utils/index';

// 接收输入框是否获得焦点的prop
const props = defineProps({
    // 当前选中的bot信息
    bot: {
        type: Object,
        default: () => ({
            id: null,
            name: '',
            avatar: ''
        })
    }
});

// 定义发送消息的事件
const emit = defineEmits(['sendBotMessage', 'selectChildBot']);

const chatStore = useChatStore();

// 设备平台判断
const isPcPlatform = computed(() => {
    const platform = detectDingTalkPlatform();
    return platform === DINGTALK_PLATFORM.PC;
});

// 获取子bot列表
const childBots = computed(() => chatStore.childBots);

const removeKnowledgeBase = () => {
    chatStore.setSelectedElfBot(null);
    chatStore.childBots = [];
};

// 发送子bot消息的处理函数
const handleSelectChildBot = bot => {
    emit('selectChildBot', bot);
};

// 组件挂载时，如果 bot 有值，请求对应的子 bot
onMounted(async () => {
    if (props.bot && props.bot.id) {
        await chatStore.getChildBots(props.bot.id);
    }
});
</script>

<style lang="scss" scoped>
.parent-bot-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 3px;
    position: relative;
    transform: translateZ(0);
    will-change: transform, opacity;
    z-index: 2;
}

.parent-bot {
    display: flex;
    flex-shrink: 0;
    z-index: 2;
    border-radius: 18px 18px 0 0;
    background: linear-gradient(135deg, $system-blue, $system-teal);
    position: relative;
    overflow: hidden;
    width: 100%;
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);

    &-inner {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 10px 16px;
        gap: 8px;
    }

    .bot-avatar {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
    }

    .bot-name {
        color: white;
        font-size: 14px;
        font-weight: 600;
        margin-right: 8px;
    }

    .delete-btn {
        cursor: pointer;
        color: rgba(255, 255, 255, 0.9);
        font-size: 18px;
        transition: all 0.2s ease;
        margin-left: auto;

        &:active {
            transform: scale(0.9);
            color: rgba(255, 255, 255, 0.7);
        }
    }
}

.child-bots-container {
    display: flex;
    gap: 4px;
    padding: 0 4px;
    width: 100%;
    z-index: 1;
    position: relative;

    // 移动端滚动样式
    &.mobile-scroll {
        overflow-x: auto;
        overflow-y: hidden;
        // 隐藏滚动条
        &::-webkit-scrollbar {
            display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    // PC端滚动样式
    &.pc-scroll {
        overflow-x: auto;
        overflow-y: hidden;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
            height: 4px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 2px;
            margin: 0 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, $system-blue, $system-teal);
            border-radius: 2px;
            transition: all 0.2s ease;

            &:hover {
                background: linear-gradient(
                    90deg,
                    darken($system-blue, 10%),
                    darken($system-teal, 10%)
                );
            }
        }

        &::-webkit-scrollbar-corner {
            background: transparent;
        }
    }
}

// 用于子组件级联淡出效果
@for $i from 1 through 6 {
    .child-bots-container > :nth-child(#{$i}) {
        animation-delay: #{0.05 * $i}s;
    }
}
</style>
