<template>
    <div class="bot-selector">
        <!-- 添加滚动容器 -->
        <div class="scroll-container">
            <div class="bot-list">
                <div
                    v-for="bot in displayBots"
                    :key="bot.id"
                    class="bot-item"
                    @click="selectBot(bot)"
                >
                    <img :src="bot.avatar" alt="" />
                    {{ bot.name }}
                </div>
            </div>
            <!-- 固定的更多按钮，在有更多bot且不是PC平台时显示 -->
            <div v-if="hasMoreBots" class="more-btn" @click="toggleMoreBots">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/more.png"
                    alt=""
                />
            </div>
        </div>

        <!-- 弹出层 -->
        <div v-show="showMoreBots" class="more-bots-dropdown">
            <div class="more-bots-panel">
                <div v-for="bot in elfBots" :key="bot.id" class="bot-item" @click="selectBot(bot)">
                    <img :src="bot.avatar" alt="" />
                    {{ bot.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import useChatStore from '@/stores/chat';
import { detectDingTalkPlatform, DINGTALK_PLATFORM } from '@/utils/index';

const chatStore = useChatStore();
const elfBots = computed(() => chatStore.elfBots);

// 判断是否为dingding-PC平台
const isPcPlatform = computed(() => {
    const platform = detectDingTalkPlatform();
    return platform === DINGTALK_PLATFORM.PC;
});

// 根据平台类型决定显示的bot数量
const displayBots = computed(() => {
    if (isPcPlatform.value) {
        // PC平台只显示前两个bot
        return elfBots.value.slice(0, 2);
    }
    // 其他平台显示所有bot
    return elfBots.value;
});

// 判断是否有更多bot需要显示
const hasMoreBots = computed(() => {
    // 在PC平台上，如果总数超过2个，则显示更多按钮
    // 在其他平台上，始终显示更多按钮
    return !isPcPlatform.value || elfBots.value.length > 2;
});

// 更多按钮的引用
const showMoreBots = ref(false);

const toggleMoreBots = () => {
    showMoreBots.value = !showMoreBots.value;
};

// 修改 selectBot 函数，改为发送事件
const emit = defineEmits(['select-bot']);

const selectBot = (bot) => {
    showMoreBots.value = false;
    // 发送选择事件给父组件处理
    emit('select-bot', bot);
};

// 添加点击外部关闭弹窗的处理
const handleClickOutside = (event) => {
    const dropdown = document.querySelector('.more-bots-dropdown');
    const moreBtn = document.querySelector('.more-btn');
    if (
        showMoreBots.value &&
        dropdown &&
        !dropdown.contains(event.target) &&
        !moreBtn.contains(event.target)
    ) {
        showMoreBots.value = false;
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
@use 'sass:color';

.bot-selector {
    width: 100%;
    font-weight: 500;
    position: relative;
    margin-bottom: 4px;

    .scroll-container {
        position: relative;
        width: 100%;
        overflow: hidden;
        padding: 0 36px 0 0;
    }

    .bot-list {
        width: 100%;
        display: flex;
        overflow-x: auto;
        scroll-behavior: smooth;
        gap: 10px;
        align-items: center;
        /* 隐藏滚动条但保持可滚动 */
        &::-webkit-scrollbar {
            display: none;
        }
        -ms-overflow-style: none;
        scrollbar-width: none;

        .bot-item {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 14px;
            height: 40px;
            background: #fff;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            color: $label-primary;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
            border: 1px solid rgba(0, 0, 0, 0.08);
            box-sizing: border-box;
            will-change: transform;

            &:hover {
                background: linear-gradient(135deg, $system-blue, $system-teal);
                color: #fff;
                border: 1px solid transparent;

                img {
                    border-color: rgba(255, 255, 255, 0.8);
                }
            }

            img {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                border: 1px solid rgba(255, 255, 255, 0.8);
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                transition: all 0.3s ease;
            }
        }
    }

    .more-btn {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 36px;
        height: 36px;
        border: 1px solid $fill-color-tertiary;
        border-radius: 12px;
        cursor: pointer;
        z-index: 3;
        background-color: $system-background-primary;
        transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        box-shadow: 0 2px 8px rgba($system-gray, 0.1);

        &:hover {
            background-color: $system-gray7;
            box-shadow: 0 4px 12px rgba($system-gray, 0.15);
        }

        &:active {
            transform: translateY(-50%) scale(0.95);
        }

        img {
            width: 20px;
            height: 20px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.08));
        }
    }

    .more-bots-dropdown {
        position: absolute;
        bottom: 100%;
        right: 0;
        z-index: 999;
        will-change: opacity, transform;
        animation: popup-fade-in 0.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;

        @keyframes popup-fade-in {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .more-bots-panel {
            background-color: $system-background-primary;
            border-radius: 16px;
            padding: 10px 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            border: 1px solid $fill-color-tertiary;
            max-height: 300px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 200px;

            .bot-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 12px 14px;
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 14px;
                font-weight: 500;
                color: $label-primary;
                border: none;

                &:hover {
                    background: linear-gradient(135deg, $system-blue, $system-teal);
                    color: white;

                    img {
                        border-color: rgba(255, 255, 255, 0.8);
                    }
                }

                &:active {
                    background: linear-gradient(
                        135deg,
                        color.mix($system-blue, black, $weight: 95%),
                        color.mix($system-teal, black, $weight: 95%)
                    );
                }

                img {
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                    transition: all 0.3s ease;
                }
            }
        }
    }
}
</style>
