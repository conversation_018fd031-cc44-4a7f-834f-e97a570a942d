<template>
    <Teleport to="body">
        <Transition name="panel-fade">
            <div
                v-if="modelValue"
                class="bottom-panel-overlay"
                @click.self="closePanel"
            >
                <div
                    class="bottom-panel-container"
                    :class="{ fullscreen: Number(panelHeight) === 100 }"
                    :style="{ height: `${panelHeight}%` }"
                >
                    <div
                        class="panel-header"
                        :class="{
                            'full-panel-header': Number(panelHeight) === 100,
                        }"
                    >
                        <div class="header-left">
                            <button
                                v-if="showBackButton"
                                class="back-button"
                                @click.stop="closePanel"
                            >
                                <i class="pi pi-chevron-down"></i>
                            </button>
                        </div>

                        <div class="header-center">
                            <div
                                class="drag-handle"
                                @mousedown="startDrag"
                                @touchstart="startDrag"
                            ></div>
                        </div>

                        <div class="header-right">
                            <slot name="header-actions"></slot>
                        </div>
                    </div>
                    <div class="panel-content">
                        <slot></slot>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup>
import {
    ref,
    onUnmounted,
    watch
} from 'vue';

const props = defineProps({
    /**
     * 控制面板是否显示
     */
    modelValue: {
        type: Boolean,
        required: true
    },
    /**
     * 面板高度百分比，范围0-1
     */
    heightPercent: {
        type: Number,
        default: 1
    },
    /**
     * 是否显示返回按钮
     */
    showBackButton: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['update:modelValue', 'update:heightPercent', 'back']);

// 最小高度百分比
const MIN_HEIGHT_PERCENT = 50;
const isDragging = ref(false);
const startY = ref(0);
const startHeight = ref(0);
const panelHeight = ref(props.heightPercent * 100);

// 监听 heightPercent 的变化 并更新 panelHeight
watch(
    () => props.heightPercent,
    (newValue) => {
        panelHeight.value = newValue * 100;
    }
);

/**
 * 开始拖拽
 * @param {MouseEvent|TouchEvent} event
 */
function startDrag(event) {
    event.preventDefault();
    event.stopPropagation();
    isDragging.value = true;
    startY.value =
        event.type === 'mousedown' ? event.clientY : event.touches[0].clientY;
    startHeight.value = panelHeight.value;

    // 添加事件监听
    if (event.type === 'mousedown') {
        window.addEventListener('mousemove', handleDrag, { passive: false });
        window.addEventListener('mouseup', stopDrag);
    } else {
        window.addEventListener('touchmove', handleDrag, { passive: false });
        window.addEventListener('touchend', stopDrag);
    }
}

/**
 * 处理拖拽过程
 * @param {MouseEvent|TouchEvent} event
 */
function handleDrag(event) {
    if (!isDragging.value) {return;}

    event.preventDefault();
    event.stopPropagation();

    const currentY =
        event.type === 'mousemove' ? event.clientY : event.touches[0].clientY;
    const deltaY = currentY - startY.value;

    // 计算新的高度百分比
    const windowHeight = window.innerHeight;
    const deltaPercent = (deltaY / windowHeight) * 100;
    let newHeight = startHeight.value - deltaPercent;

    // 限制最小和最大高度
    newHeight = Math.max(MIN_HEIGHT_PERCENT, Math.min(100, newHeight));

    panelHeight.value = newHeight;
    emit('update:heightPercent', newHeight / 100);
}

/**
 * 停止拖拽
 */
function stopDrag() {
    isDragging.value = false;
    window.removeEventListener('mousemove', handleDrag);
    window.removeEventListener('mouseup', stopDrag);
    window.removeEventListener('touchmove', handleDrag);
    window.removeEventListener('touchend', stopDrag);
}

/**
 * 关闭面板
 */
function closePanel() {
    emit('update:modelValue', false);
    emit('back');
}

// 组件卸载时清理事件监听
onUnmounted(() => {
    if (isDragging.value) {
        stopDrag();
    }
});
</script>

<style lang="scss" scoped>
.bottom-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.bottom-panel-container {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: auto;
    max-height: 100vh;
    background-color: #fff;

    &.fullscreen {
        border-radius: 0;
    }
}

.panel-header {
    height: 42px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    background: linear-gradient(135deg, #1890ff, #ffffff);
    flex-shrink: 0;

    &.full-panel-header {
        border-radius: 0;
    }

    .header-left,
    .header-right {
        flex: 1;
        display: flex;
        align-items: center;
    }

    .header-left {
        justify-content: flex-start;
    }

    .header-right {
        justify-content: flex-end;
    }

    .header-center {
        flex: 2;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.back-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 8px;
    cursor: pointer;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    &:hover {
        background-color: rgba(255, 255, 255, 0.3);
    }

    i {
        font-size: 12px;
    }
}

.drag-handle {
    width: 36px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: ns-resize;
    touch-action: none;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    position: relative;
    z-index: 10;

    &:hover {
        background-color: white;
        width: 44px;
    }

    &::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
    }
}

.panel-content {
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

// 过渡动画
.panel-fade-enter-active,
.panel-fade-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.panel-fade-enter-from,
.panel-fade-leave-to {
    opacity: 0;
}

.panel-fade-enter-from .bottom-panel-container {
    transform: translateY(100%);
}

.panel-fade-leave-to .bottom-panel-container {
    transform: translateY(100%);
}
</style>
