<template>
    <div class="iframe-container">
        <div class="iframe-content">
            <div class="loading-animation" v-show="!iframeLoaded">
                <ShimmerBorder effectType="circular" borderRadius="0 0 12px 12px" />
            </div>
            <iframe
                title="iframe"
                v-if="finalSrc"
                :src="finalSrc"
                class="external-frame"
                ref="iframeRef"
                @load="onFrameLoad"
                @error="handleIframeError"
                :class="{ 'iframe-loaded': iframeLoaded }"
                v-bind="iframeProps"
            ></iframe>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useAppStore } from '@/stores/app';
import { useIframeService } from '@/services';
import ShimmerBorder from '@/components/common/ShimmerBorder.vue';

const props = defineProps({
    /**
     * 外部链接地址
     */
    src: {
        type: String,
        required: true
    },
    /**
     * 是否需要添加ticket参数
     */
    needTicket: {
        type: Boolean,
        default: true
    },
    /**
     * 传递给iframe的其他属性
     */
    iframeProps: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['load', 'loaded']);

const appStore = useAppStore();
const finalSrc = ref('');
const iframeLoaded = ref(false);
const iframeRef = ref(null);

// 引入iframe通信服务
const iframeService = useIframeService();
const { setupMessageListener } = iframeService;

// 清理函数
let cleanupMessageListener = null;

/**
 * iframe加载完成后的处理
 */
const onFrameLoad = () => {
    if (iframeRef.value?.contentWindow) {
        // 设置消息监听器
        cleanupMessageListener = setupMessageListener(iframeRef.value.contentWindow);
    }

    // 立即设置加载状态，使用CSS过渡处理平滑过渡
    iframeLoaded.value = true;
    emit('load');

    // 通知父组件iframe已加载完成，可以调整高度
    emit('loaded', true);

    // 添加延迟，确保动画完成后再通知
    setTimeout(() => {
        emit('loaded', true);
    }, 300);
};

/**
 * 处理iframe加载错误
 */
function handleIframeError(error) {
    console.error('iframe加载失败:', error);
    iframeLoaded.value = false;
    emit('loaded', false);
}

/**
 * 获取带有ticket的URL
 */
const init = async () => {
    try {
        // 如果不需要ticket，直接使用原URL
        if (!props.needTicket) {
            finalSrc.value = props.src;
            return;
        }

        const data = await appStore.getTicket();
        const separator = props.src.includes('?') ? '&' : '?';
        finalSrc.value =
            data?.returncode === 0 && data?.result
                ? `${props.src}${separator}ticket=${data.result}`
                : props.src;
    } catch (error) {
        console.error('获取ticket异常:', error);
        finalSrc.value = props.src;
    }
};

onMounted(() => {
    // 初始化获取ticket
    init();
});

onUnmounted(() => {
    // 清理消息监听
    if (cleanupMessageListener) {
        cleanupMessageListener();
    }
});
</script>

<style lang="scss" scoped>
.iframe-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.iframe-content {
    width: 100%;
    height: 100%;
    position: relative;
}

.loading-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $system-background-secondary;
    z-index: 2;
    will-change: opacity;
}

.external-frame {
    width: 100%;
    height: 100%;
    border: none;
    opacity: 0;
    will-change: opacity, transform;

    &.iframe-loaded {
        opacity: 1;
        animation: frame-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;

        & + .loading-animation {
            animation: loader-disappear 0.2s ease forwards;
        }
    }
}

@keyframes frame-appear {
    from {
        opacity: 0;
        transform: scale(0.98);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes loader-disappear {
    to {
        opacity: 0;
        visibility: hidden;
    }
}
</style>
