<template>
    <div class="material-panel-router">
        <component
            :is="currentComponent"
            v-if="currentComponent"
            v-bind="componentProps"
            @route-change="handleRouteChange"
        />
    </div>
</template>

<script setup>
import { ref, shallowRef, onMounted, watch, provide } from 'vue';
import materialRoutes from '@/router/panelRoutes';

const props = defineProps({
    /**
     * 初始路径
     */
    initialPath: {
        type: String,
        default: 'home'
    }
});

const emit = defineEmits(['close']);

// 使用shallowRef优化性能
const currentComponent = shallowRef(null);
const componentProps = ref({});

// 内部历史记录栈
const history = ref([]);
// 当前路径
const currentPath = ref(props.initialPath);

/**
 * 根据路径加载对应组件
 * @param {string} path - 组件路径
 * @param {Object} params - 传递给组件的参数
 */
const navigateTo = async (path, params = {}) => {
    try {
        // 将当前路径添加到历史记录
        if (currentPath.value && currentPath.value !== path) {
            history.value.push({
                path: currentPath.value,
                props: { ...componentProps.value }
            });
        }

        // 更新当前路径
        currentPath.value = path;

        // 从路由映射中获取组件
        let componentModule = null;
        if (materialRoutes[path]) {
            componentModule = await materialRoutes[path]();
        } else {
            // 找不到路径时，加载默认组件
            console.warn(`未找到路径 "${path}" 对应的组件, 将加载默认组件`);
            componentModule = await materialRoutes.default();
        }

        // 更新当前组件和属性
        currentComponent.value = componentModule.default;
        componentProps.value = params;

        // 通知父组件路由已变化
        emit('route-change', {
            path,
            isBack: false,
            hasHistory: history.value.length > 0
        });
    } catch (error) {
        console.error('加载组件失败:', error);
    }
};

/**
 * 返回上一级
 */
const goBack = () => {
    if (history.value.length > 0) {
        const previous = history.value.pop();
        currentPath.value = previous.path;
        currentComponent.value = null; // 先清空再加载

        // 异步加载上一个组件
        setTimeout(async () => {
            let componentModule = null;
            if (materialRoutes[previous.path]) {
                componentModule = await materialRoutes[previous.path]();
            } else {
                componentModule = await materialRoutes.default();
            }

            currentComponent.value = componentModule.default;
            componentProps.value = previous.props || {};
        }, 0);
    } else {
        // 没有历史记录了，关闭面板
        emit('close');
    }
};

/**
 * 处理子组件的路由变化请求
 */
const handleRouteChange = (routeInfo) => {
    if (routeInfo.path === 'goBack') {
        // 特殊处理：组件请求返回上一级
        goBack();
    } else if (routeInfo.path) {
        navigateTo(routeInfo.path, routeInfo.params || {});
    }
};

// 监听initialPath变化
watch(
    () => props.initialPath,
    (newPath) => {
        if (newPath !== currentPath.value) {
            // 重置历史并导航到新路径
            history.value = [];
            navigateTo(newPath);
        }
    },
    { immediate: false }
);

// 在组件挂载时加载初始路径
onMounted(() => {
    navigateTo(props.initialPath);
});

// 暴露给父组件的方法
defineExpose({
    goBack,
    navigateTo
});

// 提供面板路由上下文
provide('materialPanelContext', {
    isInMaterialPanel: true,
    navigateTo,
    goBack
});
</script>

<style lang="scss" scoped>
.material-panel-router {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}
</style>
