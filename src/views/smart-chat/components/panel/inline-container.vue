<template>
    <div class="inline-container" :class="{ collapsed: !expanded }">
        <!-- 精致的header设计，融合整体风格 -->
        <div class="container-header" v-if="src">
            <div class="header-left">
                <div class="header-icon">
                    <i class="pi pi-chart-bar"></i>
                </div>
                <span class="header-title">
                    <span class="question-content">{{ questionContent }}</span>
                    <span class="report-suffix">{{ reportSuffix }}</span>
                </span>
            </div>
            <div class="header-right">
                <div
                    v-if="src && expanded"
                    class="fullscreen-button"
                    @click="openFullscreen"
                    title="全屏查看"
                >
                    <img
                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-chat/fullscreen-btn.png"
                        alt=""
                        srcset=""
                    />
                </div>
                <slot name="header-actions"></slot>
            </div>
        </div>

        <!-- 折叠状态的占位符 -->
        <div v-if="!expanded" class="collapsed-placeholder" @click="handleExpand">
            <div class="placeholder-content">
                <i class="pi pi-external-link placeholder-icon"></i>
                <span class="placeholder-text">点击查看完整内容</span>
                <i class="pi pi-arrow-right expand-arrow"></i>
            </div>
        </div>

        <!-- 展开状态的内容（预览模式，直接显示可滚动iframe） -->
        <div v-else class="container-content" ref="containerContentRef">
            <div class="content-wrapper">
                <slot></slot>
            </div>
            <!-- 底部tip区域 - 只在非全屏且展开时显示 -->
            <div v-if="!isFullscreen && expanded" class="view-more-tip" @click="openFullscreen">
                <div class="tip-content">
                    <img
                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-chat/fullscreen-btn.png"
                        alt=""
                        srcset=""
                    />
                    <span class="tip-text">全屏查看</span>
                </div>
            </div>
        </div>

        <!-- 全屏展开效果 -->
        <teleport to="body" v-if="isFullscreen">
            <div class="fullscreen-overlay" @click="closeFullscreen">
                <!-- 轻柔的背景遮罩 -->
                <div class="overlay-backdrop"></div>

                <!-- 从原位置展开的内容容器 -->
                <div
                    class="fullscreen-container"
                    :style="fullscreenStyle"
                    @click.stop
                    ref="fullscreenContainerRef"
                >
                    <!-- 简洁的顶部工具栏 -->
                    <div class="fullscreen-toolbar">
                        <div class="toolbar-left">
                            <div class="toolbar-icon">
                                <i class="pi pi-chart-bar"></i>
                            </div>
                            <span class="toolbar-title">
                                <span class="question-content">{{ questionContent }}</span>
                                <span class="report-suffix">{{ reportSuffix }}</span>
                            </span>
                        </div>
                        <div class="toolbar-close" @click="closeFullscreen" title="关闭 (ESC)">
                            <i class="pi pi-times"></i>
                        </div>
                    </div>

                    <!-- iframe内容区域 -->
                    <div class="fullscreen-iframe-container" ref="fullscreenIframeContainerRef">
                        <IframeView
                            v-if="fullscreenSrc"
                            :src="fullscreenSrc"
                            :need-ticket="needTicket"
                        />
                    </div>
                </div>
            </div>
        </teleport>
    </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue';
import IframeView from './iframe.vue';

const props = defineProps({
    /**
     * 头部标题（兼容性保留）
     */
    title: {
        type: String,
        default: '请看返回内容'
    },
    /**
     * 用户问题内容
     */
    questionContent: {
        type: String,
        default: ''
    },
    /**
     * 报告后缀
     */
    reportSuffix: {
        type: String,
        default: ' - 分析报告'
    },
    /**
     * 是否展开状态
     */
    expanded: {
        type: Boolean,
        default: true
    },
    /**
     * iframe源地址（用于全屏显示）
     */
    src: {
        type: String,
        default: ''
    },
    /**
     * 是否需要ticket参数
     */
    needTicket: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['close', 'expand', 'fullscreen']);

// 状态管理
const isFullscreen = ref(false);
const containerContentRef = ref(null);
const fullscreenContainerRef = ref(null);
const fullscreenIframeContainerRef = ref(null);

// 计算属性
const fullscreenSrc = computed(() => props.src);

// 处理兼容性：如果没有传入 questionContent，则从 title 中分离
const questionContent = computed(() => {
    if (props.questionContent) {
        return props.questionContent;
    }
    // 兼容旧的 title 格式 "问题内容 - 分析报告"
    if (props.title && props.title.includes(' - ')) {
        return props.title.split(' - ')[0];
    }
    return props.title || '请看返回内容';
});

const reportSuffix = computed(() => {
    if (props.reportSuffix) {
        return props.reportSuffix;
    }
    // 兼容旧的 title 格式
    if (props.title && props.title.includes(' - ')) {
        return ' - ' + props.title.split(' - ').slice(1).join(' - ');
    }
    return ' - 分析报告';
});

// 计算全屏容器样式（展开动画）
const fullscreenStyle = computed(() => {
    return {
        borderRadius: '16px',
        overflow: 'hidden'
    };
});

/**
 * 处理展开事件
 */
function handleExpand() {
    emit('expand');
}

/**
 * 打开全屏模式
 */
async function openFullscreen() {
    isFullscreen.value = true;
    emit('fullscreen', true);

    // 防止body滚动
    document.body.style.overflow = 'hidden';

    // 等待DOM更新
    await nextTick();

    // 添加展开动画
    if (fullscreenContainerRef.value) {
        fullscreenContainerRef.value.classList.add('expanded');
    }
}

/**
 * 关闭全屏模式
 */
function closeFullscreen() {
    if (fullscreenContainerRef.value) {
        fullscreenContainerRef.value.classList.add('collapsing');

        // 等待动画完成后关闭
        setTimeout(() => {
            isFullscreen.value = false;
            emit('fullscreen', false);
            // 恢复body滚动
            document.body.style.overflow = '';
        }, 0);
    } else {
        // 直接关闭
        isFullscreen.value = false;
        emit('fullscreen', false);
        document.body.style.overflow = '';
    }
}

/**
 * ESC键关闭全屏
 */
function handleKeydown(event) {
    if (event.key === 'Escape' && isFullscreen.value) {
        closeFullscreen();
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
    // 确保页面离开时恢复滚动
    document.body.style.overflow = '';
});
</script>

<style lang="scss" scoped>
.inline-container {
    background: $system-background-primary;
    border: 1px solid $fill-color-quaternary;
    box-shadow: 0 2px 8px $fill-color-quaternary;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* 精致的header，融合整体风格 */
    .container-header {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 0; /* 允许文本截断 */

            .header-icon {
                width: 24px;
                height: 24px;
                border-radius: 6px;
                background: linear-gradient(135deg, $system-blue, $system-teal);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 1px 3px rgba($system-blue, 0.3);

                i {
                    color: white;
                    font-size: 11px;
                }
            }

            .header-title {
                font-size: 13px;
                font-weight: 500;
                letter-spacing: -0.01em;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: calc(100vw - 180px); /* 响应式宽度，预留按钮空间 */

                .question-content {
                    color: $system-blue; /* 用户问题内容使用蓝色 */
                    font-weight: 600;
                }

                .report-suffix {
                    color: $label-primary; /* 报告后缀使用正常颜色 */
                    font-weight: 500;
                }
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;

            .fullscreen-button {
                display: flex;
                align-items: center;
                gap: 4px;
                background: linear-gradient(135deg, $system-orange, $system-red);
                border: none;
                padding: 4px 8px;
                cursor: pointer;
                color: white;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                box-shadow: 0 1px 3px rgba($system-orange, 0.3);
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

                img {
                    width: 20px;
                    height: 20px;
                }

                &:hover {
                    background: linear-gradient(
                        135deg,
                        darken($system-orange, 5%),
                        darken($system-red, 5%)
                    );
                    transform: translateY(-0.5px);
                    box-shadow: 0 2px 6px rgba($system-orange, 0.4);
                }

                &:active {
                    transform: translateY(0);
                }
            }

            .close-button {
                background: rgba($system-gray, 0.1);
                border: none;
                padding: 6px;
                cursor: pointer;
                color: $label-secondary;
                border-radius: 6px;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;

                &:hover {
                    background-color: rgba($system-red, 0.1);
                    color: $system-red;
                    transform: scale(1.05);
                }

                &:active {
                    transform: scale(0.95);
                }

                i {
                    font-size: 10px;
                }
            }
        }
    }

    .container-content {
        overflow: hidden;
        position: relative;
        height: 500px;
        display: flex;
        flex-direction: column;

        .content-wrapper {
            flex: 1;
            overflow: auto;
            position: relative;
        }

        .view-more-tip {
            position: relative;
            height: 36px;
            cursor: pointer;
            background: linear-gradient(135deg, rgba($system-orange, 0.9), rgba($system-red, 0.9));
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;

            &:hover {
                background: linear-gradient(135deg, $system-orange, $system-red);
            }

            &:active {
                transform: translateY(0);
            }

            .tip-content {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #fff;
                font-weight: 500;
                font-size: 12px;
                transition: color 0.2s ease;

                img {
                    width: 18px;
                    height: 18px;
                }

                .tip-text {
                    font-weight: 500;
                    user-select: none;
                }
            }
        }
    }

    // 折叠状态样式
    &.collapsed {
        cursor: pointer;

        &:hover {
            box-shadow: 0 4px 12px $fill-color-quaternary;
            transform: translateY(-1px);
        }

        &:active {
            transform: scale(0.98);
        }
    }
}

.collapsed-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba($system-blue, 0.02);
    border: 1px dashed rgba($system-blue, 0.2);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    height: 100px;

    &:hover {
        background: rgba($system-blue, 0.04);
        border-color: rgba($system-blue, 0.4);
        box-shadow: 0 2px 8px rgba($system-blue, 0.1);
        transform: translateY(-1px);
    }

    &:active {
        transform: scale(0.98);
    }

    .placeholder-content {
        display: flex;
        align-items: center;
        gap: 6px;
        color: $system-blue;
        font-weight: 500;
        padding: 10px 14px;
        border-radius: 8px;
        background: rgba($system-background-primary, 0.8);
        box-shadow: 0 1px 4px rgba($system-blue, 0.1);
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);

        .placeholder-icon {
            font-size: 12px;
            color: $system-blue;
        }

        .placeholder-text {
            font-size: 12px;
            letter-spacing: 0.2px;
            font-weight: 500;
        }

        .expand-arrow {
            font-size: 10px;
            margin-left: 2px;
            color: $system-blue;
            opacity: 0.7;
        }
    }

    &:hover .placeholder-content {
        background-color: rgba($system-background-primary, 0.95);
        box-shadow: 0 2px 8px rgba($system-blue, 0.15);

        .expand-arrow {
            transform: translateX(2px);
            opacity: 1;
        }
    }
}

/* 自然展开的全屏效果 */
.fullscreen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;

    .overlay-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba($system-background-secondary, 0.8);
        backdrop-filter: blur(12px) saturate(120%);
        -webkit-backdrop-filter: blur(12px) saturate(120%);
        animation: backdropFadeIn 0.3s ease-out forwards;
    }

    .fullscreen-container {
        width: 100%;
        height: 100%;
        max-width: 100%;
        max-height: 100%;
        background: $system-background-primary;
        box-shadow: 0 8px 32px rgba($system-gray, 0.2), 0 2px 8px rgba($system-gray, 0.1);
        border: 1px solid $fill-color-quaternary;
        display: flex;
        flex-direction: column;
        position: relative;
        z-index: 2;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);

        &.expanded {
            opacity: 1;
        }

        &.collapsing {
            opacity: 0;
        }

        /* 简洁顶部工具栏 */
        .fullscreen-toolbar {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            background: $system-background-secondary;
            flex-shrink: 0;

            .toolbar-left {
                display: flex;
                align-items: center;
                gap: 12px;
                min-width: 0; /* 允许文本截断 */
                flex: 1;

                .toolbar-icon {
                    width: 24px;
                    height: 24px;
                    border-radius: 8px;
                    background: linear-gradient(135deg, $system-blue, $system-teal);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2px 4px rgba($system-blue, 0.2);

                    i {
                        color: white;
                        font-size: 12px;
                    }
                }

                .toolbar-title {
                    font-size: 13px;
                    font-weight: 600;
                    letter-spacing: -0.01em;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: calc(100vw - 120px); /* 全屏模式响应式宽度 */

                    .question-content {
                        color: $system-blue; /* 用户问题内容使用蓝色 */
                        font-weight: 700;
                    }

                    .report-suffix {
                        color: $label-primary; /* 报告后缀使用正常颜色 */
                        font-weight: 600;
                    }
                }
            }

            .toolbar-close {
                background: rgba($system-gray, 0.1);
                border: none;
                padding: 8px;
                cursor: pointer;
                color: $label-secondary;
                border-radius: 8px;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;

                &:hover {
                    background-color: rgba($system-red, 0.1);
                    color: $system-red;
                    transform: scale(1.05);
                }

                &:active {
                    transform: scale(0.95);
                }

                i {
                    font-size: 12px;
                }
            }
        }

        /* iframe容器 */
        .fullscreen-iframe-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
    }
}

@keyframes backdropFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
