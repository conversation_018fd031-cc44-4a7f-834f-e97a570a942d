<template>
    <div class="session-drawer-overlay" v-show="modelValue" @click="handleClose"></div>
    <div class="session-drawer" :class="{ 'session-drawer--active': modelValue }">
        <!-- 抽屉头部 -->
        <div class="drawer-header">
            <div class="header-title">
                <img
                    class="history-icon"
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/history2.png"
                    alt="历史"
                />
                <span>历史会话</span>
            </div>
            <div class="header-close" @click="handleClose">
                <i class="pi pi-times"></i>
            </div>
        </div>

        <!-- 抽屉内容区 -->
        <div class="drawer-body">
            <!-- 加载中状态 -->
            <div class="loading-container" v-if="isLoading && sessions.length === 0">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/loading.png"
                    alt="loading"
                />
                <span>加载中...</span>
            </div>

            <!-- 空状态 -->
            <div class="empty-container" v-else-if="sessions.length === 0">
                <!-- 新增创建新会话按钮 -->
                <Button icon="pi pi-plus" rounded label="新会话" @click="$emit('newChat')" />
                <div class="empty-text">暂无历史会话</div>
            </div>

            <!-- 会话列表 -->
            <div class="session-list" v-else>
                <div
                    v-for="item in sessions"
                    :key="item.id"
                    class="session-item"
                    @click="selectSession(item)"
                >
                    <div class="session-icon">
                        <i class="pi pi-comments"></i>
                    </div>
                    <div class="session-info">
                        <div class="session-name">{{ item.sessionName }}</div>
                    </div>
                    <div class="session-arrow">
                        <i class="pi pi-angle-right"></i>
                    </div>
                </div>

                <!-- 加载更多按钮 -->
                <div class="drawer-actions-bottom" v-if="sessions.length > 0">
                    <button
                        class="load-more-btn"
                        @click="loadMoreSessions"
                        v-if="!loadedAllSessions"
                        :disabled="isLoading"
                    >
                        <div class="loading-indicator" v-if="isLoading">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/loading.png"
                                alt="loading"
                            />
                        </div>
                        <span>{{ isLoading ? '加载中...' : '查看更多' }}</span>
                    </button>
                    <div class="no-more-text" v-else>没有更多会话了</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import useChatStore from '@/stores/chat';
import { useRouter } from 'vue-router';

// Props 定义
const props = defineProps({
    modelValue: Boolean
});

// Emits 定义
const emit = defineEmits(['update:modelValue', 'newChat', 'historySelect']);

// 使用 store 和 router
const chatStore = useChatStore();

// 会话列表状态
const sessions = ref([]);
const isLoading = ref(false);
const loadedAllSessions = ref(false);
const currentPage = ref(1);
const pageSize = ref(5);

// 监听抽屉显示状态
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            // 抽屉打开时，重置状态并加载第一页数据
            resetState();
            loadSessions();
            // 打开抽屉时阻止页面滚动
            document.body.style.overflow = 'hidden';
        } else {
            // 关闭抽屉时恢复页面滚动
            document.body.style.overflow = '';
        }
    },
    { immediate: true }
);

// 重置状态
const resetState = () => {
    sessions.value = [];
    currentPage.value = 1;
    loadedAllSessions.value = false;
};

// 处理关闭
const handleClose = () => {
    emit('update:modelValue', false);
};

// 加载会话列表
const loadSessions = async () => {
    if (isLoading.value || loadedAllSessions.value) {
        return;
    }

    isLoading.value = true;

    try {
        const result = await chatStore.getSessions({
            pageIndex: currentPage.value,
            pageSize: pageSize.value
        });

        if (result && result.list && result.list.length > 0) {
            // 添加新会话到列表
            sessions.value = [...sessions.value, ...result.list];

            // 检查是否已加载所有会话
            if (result.list.length < pageSize.value || result.pageindex >= result.pagecount) {
                loadedAllSessions.value = true;
            }
        } else if (currentPage.value === 1) {
            // 第一页就没有数据
            loadedAllSessions.value = true;
        }
    } catch (error) {
        console.error('获取会话列表失败:', error.message);
    } finally {
        isLoading.value = false;
    }
};

// 加载更多会话
const loadMoreSessions = () => {
    if (!isLoading.value && !loadedAllSessions.value) {
        currentPage.value++;
        loadSessions();
    }
};

// 选择会话
const selectSession = (session) => {
    // 关闭会话抽屉
    handleClose();

    // 设置当前会话ID
    chatStore.setSessionId(session.id);

    // 发送会话变更事件
    emit('historySelect', session.id);
};
</script>

<style lang="scss" scoped>
.session-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $label-quaternary;
    z-index: 999;
}

.session-drawer {
    position: fixed;
    top: 0;
    left: 0;
    width: 85%;
    max-width: 350px;
    height: 100%;
    z-index: 1000;
    background-color: $system-background-primary;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.22, 1, 0.36, 1);
    display: flex;
    flex-direction: column;
    will-change: transform;

    &--active {
        transform: translateX(0);
    }

    .drawer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid $fill-color-quaternary;
        background-color: $system-background-primary;

        .header-title {
            display: flex;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            color: $label-primary;
            gap: 8px;

            .history-icon {
                width: 20px;
                height: 20px;
                filter: drop-shadow(0 1px 2px $fill-color-quaternary);
            }
        }

        .header-close {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: $label-secondary;
            transition: all 0.2s ease;
            background-color: $system-grouped-background-primary;

            &:active {
                transform: scale(0.95);
                background-color: $system-grouped-background-tertiary;
            }

            i {
                font-size: 20px;
                filter: drop-shadow(0 2px 4px $fill-color-quaternary);
            }
        }
    }

    .drawer-body {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        background-color: $system-grouped-background-primary;

        .loading-container,
        .empty-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            gap: 16px;
            color: $label-secondary;

            img {
                width: 32px;
                height: 32px;
                animation: spin 1.5s linear infinite;
            }

            .empty-text {
                font-size: 14px;
                color: $label-secondary;
                margin-top: 8px;
            }

            button {
                color: $system-blue;
                background-color: rgba($system-blue, 0.08);
                box-shadow: 0 4px 12px rgba($system-blue, 0.2);
                border: none;
                font-weight: 600;

                &:hover {
                    background-color: rgba($system-blue, 0.12);
                    box-shadow: 0 6px 16px rgba($system-blue, 0.3);
                }

                &:active {
                    transform: scale(0.95);
                    box-shadow: 0 2px 8px rgba($system-blue, 0.2);
                }
            }
        }

        .session-list {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .session-item {
                display: flex;
                align-items: center;
                padding: 12px;
                border-radius: 16px;
                background-color: $system-background-primary;
                cursor: pointer;
                transition: all 0.2s ease;
                box-shadow: 0 3px 12px $fill-color-quaternary;
                border: 1px solid $fill-color-quaternary;

                &:active {
                    transform: scale(0.98);
                    background-color: $system-background-secondary;
                }

                .session-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, $system-blue, $system-teal);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;
                    box-shadow: 0 3px 8px rgba($system-blue, 0.2);

                    i {
                        color: white;
                        font-size: 18px;
                    }
                }

                .session-info {
                    flex: 1;
                    overflow: hidden;

                    .session-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: $label-primary;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .session-arrow {
                    color: $label-tertiary;
                    width: 20px;
                    display: flex;
                    justify-content: center;

                    i {
                        font-size: 18px;
                    }
                }
            }
        }

        .drawer-actions-bottom {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            padding-bottom: 20px;

            .load-more-btn {
                padding: 8px 20px;
                border-radius: 20px;
                background-color: $system-background-primary;
                border: none;
                color: $label-secondary;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px $fill-color-quaternary;

                &:active:not(:disabled) {
                    transform: scale(0.95);
                }

                &:disabled {
                    opacity: 0.7;
                    cursor: not-allowed;
                }

                .loading-indicator {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 16px;
                        height: 16px;
                        animation: spin 1.5s linear infinite;
                    }
                }
            }

            .no-more-text {
                font-size: 14px;
                color: $label-tertiary;
            }
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
