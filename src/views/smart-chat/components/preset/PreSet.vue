<template>
    <div class="preset-container">
        <div class="welcome-header">
            <div class="avatar-container">
                <img :src="botConfig.avatarUrl" :alt="botConfig.title" class="avatar" />
            </div>
            <h1 class="welcome-title">{{ botConfig.title }}</h1>
            <p class="welcome-subtitle">{{ botConfig.subtitle }}</p>
        </div>

        <div class="preset-cards">
            <div
                v-for="(item, index) in botConfig?.presets || []"
                :key="index"
                class="preset-card"
                @click="handleApplyPreset(item)"
            >
                <div class="card-content">
                    <div class="card-icon">
                        <i class="pi pi-comments"></i>
                    </div>
                    <div class="card-text">
                        <h3 class="card-title">{{ item.title }}</h3>
                        <p class="card-desc">{{ item.desc }}</p>
                    </div>
                    <div class="card-arrow">
                        <i class="pi pi-angle-right"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="start-hint">
            <p>点击问题开始对话，或直接输入您的问题</p>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getBotConfig } from '@/config/botConfig';

const props = defineProps({
    botId: {
        type: [String, Number],
        default: ''
    }
});

const emit = defineEmits(['sendDirectly']);

// 根据 botId 获取当前配置
const botConfig = computed(() => getBotConfig(props.botId));

const handleApplyPreset = (item) => {
    // 直接发送消息，而不是填充到输入框
    emit('sendDirectly', item.desc);
};
</script>

<style lang="scss">
.preset-container {
    padding: 80px 20px;
    flex: 1;
    overflow-y: auto;
    font-weight: 500;

    .welcome-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 16px;
        animation: fadeInDown 0.4s ease-out;

        .avatar-container {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: $system-background-primary;
            display: flex;
            justify-content: center;
            align-items: center;
            transition:
                transform 0.3s ease,
                box-shadow 0.3s ease;

            &:active {
                transform: scale(0.95);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            }

            .avatar {
                width: 30px;
                height: 30px;
            }
        }

        .welcome-title {
            font-size: 18px;
            font-weight: 600;
            color: $label-primary;
            margin-bottom: 4px;
            margin-top: 8px;
        }

        .welcome-subtitle {
            font-size: 13px;
            color: $label-secondary;
            font-weight: 500;
        }
    }

    .preset-cards {
        max-width: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
        animation: fadeInUp 0.4s ease-out;

        .preset-card {
            background-color: $system-background-primary;
            border-radius: 14px;
            overflow: hidden;
            transition: all 0.2s cubic-bezier(0.22, 1, 0.36, 1);
            box-shadow: 0 2px 8px $fill-color-quaternary;
            border: 1px solid $fill-color-quaternary;

            &:active {
                transform: scale(0.97);
                background-color: $system-background-secondary;
            }

            .card-content {
                display: flex;
                align-items: center;
                padding: 12px 14px;
                gap: 12px;

                .card-icon {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, $system-blue, $system-teal);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    flex-shrink: 0;
                    box-shadow: 0 2px 6px rgba($system-blue, 0.2);

                    i {
                        color: white;
                        font-size: 16px;
                    }
                }

                .card-text {
                    flex: 1;
                    min-width: 0;
                    padding: 0 2px;

                    .card-title {
                        font-size: 14px;
                        font-weight: 600;
                        color: $label-primary;
                        margin-bottom: 4px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .card-desc {
                        font-size: 12px;
                        color: $label-secondary;
                        line-height: 1.3;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }
                }

                .card-arrow {
                    color: $label-tertiary;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    width: 18px;
                    transition: transform 0.2s ease;

                    i {
                        font-size: 16px;
                    }
                }
            }

            &:active .card-arrow {
                transform: translateX(3px);
            }
        }
    }

    .start-hint {
        text-align: center;
        padding: 6px 0;
        animation: fadeIn 0.4s ease-out;

        p {
            font-size: 12px;
            color: $label-secondary;
            font-weight: 500;
            letter-spacing: 0.1px;
        }
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>
