<template>
    <div class="home-page">
        <!-- 使用新的头部组件并传递selectedBot -->
        <HeaderActions
            @openHistory="openSessionDrawer"
            @newChat="handleNewChat"
            :selectedBot="selectedBot"
        />

        <div class="home-content">
            <!-- 加载完成后才显示内容 -->
            <template v-if="isLoaded">
                <!-- 欢迎问候语 -->
                <FadeInSection :delay="50">
                    <div class="greeting-text">
                        <div>
                            <span v-if="!selectedBot || selectedBot.elfType !== 1">{{
                                typedGreeting
                            }}</span>
                            <span
                                :class="[
                                    {
                                        'gradient-text': !selectedBot || selectedBot.elfType !== 1,
                                        'normal-text': selectedBot && selectedBot.elfType === 1
                                    }
                                ]"
                                >{{ typedText }}</span
                            >
                            <span
                                class="cursor-container"
                                v-if="!selectedBot || selectedBot.elfType !== 1"
                            >
                                <span class="cursor" v-if="isTyping"></span>
                            </span>
                        </div>
                    </div>
                </FadeInSection>

                <!-- 聊天输入区域 -->
                <FadeInSection :delay="50">
                    <div class="chat-input-area">
                        <!-- 已删除手动切换按钮，改为根据 botId 自动判断 -->

                        <div class="user-input">
                            <div class="user-input-chat">
                                <section class="main">
                                    <div
                                        :class="[
                                            'input-area',
                                            {
                                                'input-area--with-bot':
                                                    selectedBot && selectedBot.elfType === 1,
                                                'input-area--send-only': shouldHideDeepThinking
                                            }
                                        ]"
                                        @click.prevent="focusMessage"
                                    >
                                        <!-- 选中的bot信息和切换按钮 -->
                                        <div
                                            v-if="selectedBot && selectedBot.elfType === 1"
                                            class="bot-header-row"
                                        >
                                            <div
                                                class="selected-bot-tag"
                                                @click.stop="clearSelectedBot"
                                            >
                                                <div class="change-bot">
                                                    <img
                                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/left-back.png"
                                                        alt=""
                                                    />
                                                </div>
                                                <span class="bot-name">{{ selectedBot.name }}</span>
                                            </div>

                                            <!-- 表单模式切换按钮 -->
                                            <div
                                                v-if="shouldShowDynamicForm"
                                                class="form-mode-toggle"
                                                @click.stop="toggleFormMode"
                                                :title="
                                                    useDynamicForm
                                                        ? '切换到传统输入'
                                                        : '切换到智能表单'
                                                "
                                            >
                                                <img
                                                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/toogle.png"
                                                    alt=""
                                                    srcset=""
                                                />
                                                <span>切换</span>
                                            </div>
                                        </div>

                                        <!-- 文本输入区域和发送按钮行 -->
                                        <div
                                            class="input-content-row"
                                            :class="{ 'input-content-row--column': useDynamicForm }"
                                        >
                                            <!-- 传统文本输入区域 -->
                                            <div v-if="!useDynamicForm" class="textarea-container">
                                                <textarea
                                                    ref="chatInput"
                                                    id="chat_input"
                                                    name="message"
                                                    :placeholder="
                                                        selectedBot && selectedBot.elfType === 1
                                                            ? getBotPlaceholder(selectedBot.id)
                                                            : '发消息、输入 @ 选择技能'
                                                    "
                                                    v-model="message"
                                                    @input.prevent="handleInput"
                                                    @keydown.enter.prevent="handleEnterKey"
                                                    @keydown="handleKeyDown"
                                                    @compositionstart="handleCompositionStart"
                                                    @compositionend="handleCompositionEnd"
                                                ></textarea>
                                            </div>

                                            <!-- 动态表单输入区域 -->
                                            <div v-else class="dynamic-form-container">
                                                <DynamicForm
                                                    ref="dynamicFormRef"
                                                    :config="dynamicFormConfig"
                                                    @value-change="handleDynamicFormChange"
                                                    @content-change="handleDynamicFormContentChange"
                                                    @send-message="handleDynamicFormSendMessage"
                                                />
                                            </div>

                                            <!-- 只有发送按钮的情况下，直接显示在输入框旁边 -->
                                            <div
                                                v-if="shouldHideDeepThinking"
                                                class="send-btn-container"
                                            >
                                                <div
                                                    class="sending"
                                                    :class="{
                                                        'sending--disabled': !canSendMessage
                                                    }"
                                                    @click="startChat"
                                                >
                                                    <img
                                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/send.svg"
                                                        alt=""
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 底部功能区：深度思考按钮和发送按钮 -->
                                        <div
                                            v-if="!shouldHideDeepThinking"
                                            class="input-bottom-area"
                                        >
                                            <DeepThinkingButton />
                                            <div class="send-btn-container">
                                                <div
                                                    class="sending"
                                                    :class="{
                                                        'sending--disabled': !canSendMessage
                                                    }"
                                                    @click="startChat"
                                                >
                                                    <img
                                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/send.svg"
                                                        alt=""
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>

                        <!-- 添加推荐问题组件 -->
                        <ReferenceQuestionsHome
                            v-if="selectedBot && selectedBot.elfType === 1"
                            :botId="selectedBot.id"
                            @sendDirectly="handleQuestionSelect"
                        />
                    </div>
                </FadeInSection>

                <!-- Bot选择器 -->
                <FadeInSection :delay="50" v-if="!selectedBot || selectedBot.elfType !== 1">
                    <HomeBotSelector :bots="homeDisplayBots" @bot-click="handleBotClick" />
                </FadeInSection>

                <!-- Bot选择弹窗 - 移到与FadeInSection平级的位置 -->
                <div v-if="showBotSelector" class="bot-selector-popup" @click="preventBubbling">
                    <div class="bot-search">
                        <input
                            ref="botSearchInput"
                            type="text"
                            v-model="botSearchQuery"
                            placeholder="搜索助手"
                            @input="filterBots"
                            @blur="handleBotSelectorBlur"
                        />
                    </div>
                    <div class="bot-popup-list">
                        <div
                            v-for="bot in filteredBots"
                            :key="bot.id"
                            class="bot-popup-item"
                            @click="selectBotFromPopup(bot)"
                        >
                            <img :src="bot.avatar" alt="" class="bot-icon" />
                            <span>{{ bot.name }}</span>
                        </div>
                        <div v-if="filteredBots.length === 0" class="empty-result">
                            <div class="empty-icon">
                                <i class="pi pi-search"></i>
                            </div>
                            <div class="empty-text">未找到相关助手</div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- 底部弹出面板 -->
        <PanelContainer
            v-model="showBottomPanel"
            :heightPercent="panelHeightPercent"
            :showBackButton="true"
            @back="resetPanel"
        >
            <RouterView v-if="showRouterView" :initialPath="routerPath" @close="closeRouterView" />

            <IframeView
                v-if="showIframeView"
                :src="iframeConfig.src"
                :iframeProps="iframeConfig.props"
            />
        </PanelContainer>

        <!-- 添加会话列表抽屉组件 -->
        <SessionDrawer
            v-model="showSessionDrawer"
            @newChat="handleNewChat"
            @historySelect="handleHistorySelect"
        />
    </div>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi';
import { ref, onMounted, computed, nextTick, watch, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import useChatStore from '@/stores/chat';
import { useBotSelector } from '@/composables/useBotSelector';
import ReferenceQuestionsHome from '@/views/smart-chat/components/reference/ReferenceQuestionsHome.vue';
import PanelContainer from '@/views/smart-chat/components/panel/container.vue';
import IframeView from '@/views/smart-chat/components/panel/iframe.vue';
import RouterView from '@/views/smart-chat/components/panel/router.vue';
import DeepThinkingButton from '@/views/smart-chat/components/deepseek/DeepThinkingButton.vue';
import FadeInSection from '@/components/common/FadeInSection.vue';
import HeaderActions from '@/views/smart-chat/components/header/HeaderActions.vue';
import SessionDrawer from '@/views/smart-chat/components/history/SessionDrawer.vue';
import DynamicForm from '@/components/DynamicForm/index.vue';
import HomeBotSelector from '@/views/smart-chat/components/bot/HomeBotSelector.vue';
import testMockData from '@/views/smart-chat/json/test-mock.json';

import { isDingTalk, isDingTalkPC } from '@/utils/index';

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '家家精灵'
    });
}

const router = useRouter();
const route = useRoute();
const chatStore = useChatStore();
const message = ref('');
const chatInput = ref(null);

// DynamicForm 相关状态
const dynamicFormRef = ref(null);
const dynamicFormConfig = ref(testMockData);
const dynamicFormValues = ref({});

// 当前是否正在使用动态表单模式（响应式状态）
const useDynamicForm = ref(false);

// 根据 botId 判断是否应该显示动态表单（计算属性）
const shouldShowDynamicForm = computed(() => {
    // 检查是否为特定的botId
    const isTargetBot = selectedBot.value && Number(selectedBot.value.id) === 10;

    if (!isTargetBot) {
        return false;
    }

    // 钉钉环境中：只有PC端展示
    if (isDingTalk()) {
        return isDingTalkPC();
    }

    // 脱离钉钉环境：都展示
    return true;
});

// 使用组合式函数获取bot选择相关的状态和方法
const {
    elfBots,
    selectedBot,
    showBottomPanel,
    panelHeightPercent,
    showRouterView,
    showIframeView,
    routerPath,
    iframeConfig,
    selectBot,
    resetPanel,
    closeRouterView,
    clearSelectedBot,
    setBotFromUrl
} = useBotSelector({
    onSuccess: () => {
        typeGreeting();
    },
    onClose: () => {
        if (isDingTalk()) {
            dd.setNavigationTitle({
                title: '家家精灵'
            });
        }
    }
});

// 计算属性：过滤只在首页显示的bot
const homeDisplayBots = computed(() => {
    return elfBots.value.filter(bot => bot.displayInHome === true);
});

// 会话抽屉和历史抽屉控制
const showSessionDrawer = ref(false);
const selectedSessionId = ref(null);

// 打开会话抽屉
const openSessionDrawer = () => {
    showSessionDrawer.value = true;
};

// 打字机效果相关变量
const typedGreeting = ref(''); // 用于时间问候语
const typedText = ref(''); // 用于gradientText的打字效果
const fullGreeting = ref(''); // 根据时间问候语
const typingSpeed = ref(20); // 打字速度(ms)
const isTyping = ref(false); // 控制光标显示

// 添加输入法状态标记
const isComposing = ref(false);

// 添加一个状态变量，防止打字效果重复执行
const typingInProgress = ref(false);

// 渐变文字内容
const gradientText = computed(() => {
    return selectedBot.value ? getBotGreeting(selectedBot.value.id) : '小精灵';
});

/**
 * bot相关的文案配置
 * @type {Object}
 */
const botConfig = {
    1: {
        greeting: '找人问事零等待，取物签约秒搞定',
        placeholder: '输入要问的问题'
    },
    2: {
        greeting: '活动创建我助力，常见问题我先行',
        placeholder: '输入要问的问题'
    },
    10: {
        greeting: '解读车商数据，问数只是开始',
        placeholder: '城厂销量数据、单店运营情况'
    },
    11: {
        greeting: '车商问题随手查，专业解答效率佳',
        placeholder: '输入要问的问题'
    },
    12: {
        greeting: '研发问题我解答，文档地址一键查',
        placeholder: '输入要问的问题'
    },
    13: {
        greeting: '展厅全链指南书，规则秒懂不绕途',
        placeholder: '输入要问的问题'
    },
    16: {
        greeting: '营销活动AI助手',
        placeholder: '活动创建、活动查询、活动任务、活动场地'
    }
};

/**
 * 根据bot ID获取对应的问候语
 * @param {number|string} botId
 * @returns {string} 返回对应bot的问候语
 */
const getBotGreeting = botId => {
    return botConfig[botId]?.greeting || `${selectedBot.value?.name || '智能助手'}`;
};

/**
 * 根据bot ID获取对应的placeholder
 * @param {number|string} botId - bot的ID
 * @returns {string} 返回对应bot的placeholder
 */
const getBotPlaceholder = botId => {
    return botConfig[botId]?.placeholder || '发消息';
};

const getGreetingByTime = () => {
    const hour = new Date().getHours();
    let greeting = '';

    if (hour >= 5 && hour < 12) {
        greeting = '上午好，';
    } else if (hour >= 12 && hour < 18) {
        greeting = '下午好，';
    } else {
        greeting = '晚上好，';
    }

    return greeting;
};

// 打字机效果函数
const typeGreeting = () => {
    // 如果已在执行打字效果，则直接返回
    if (typingInProgress.value) {
        return;
    }

    typingInProgress.value = true;

    // 根据当前时间设置问候语
    fullGreeting.value = getGreetingByTime();

    // 清空当前文本
    typedGreeting.value = '';
    typedText.value = '';
    isTyping.value = false;

    // 先完成问候语的打字效果
    let greetingCurrentIndex = 0;
    const greetingLength = fullGreeting.value.length;

    const greetingTypingInterval = setInterval(() => {
        if (greetingCurrentIndex < greetingLength) {
            typedGreeting.value += fullGreeting.value.charAt(greetingCurrentIndex);
            greetingCurrentIndex++;
        } else {
            // 问候语打字完成，开始gradientText的打字效果
            clearInterval(greetingTypingInterval);

            // 获取要显示的文本
            const textToType = gradientText.value;
            let textCurrentIndex = 0;
            const textLength = textToType.length;

            const textTypingInterval = setInterval(() => {
                if (textCurrentIndex < textLength) {
                    typedText.value += textToType.charAt(textCurrentIndex);
                    textCurrentIndex++;
                } else {
                    // 打字完成，清除定时器
                    clearInterval(textTypingInterval);
                    isTyping.value = true;
                    typingInProgress.value = false; // 重置状态
                }
            }, typingSpeed.value);
        }
    }, typingSpeed.value);
};

// Bot选择弹窗相关状态
const showBotSelector = ref(false);
const botSearchQuery = ref('');
const filteredBots = ref([]);
const botSearchInput = ref(null); // 新增：Bot搜索框引用

// 添加加载状态变量
const isLoaded = ref(false);

// 添加计算属性：判断是否应该隐藏深度思考按钮
const shouldHideDeepThinking = computed(() => {
    // 当选中的botId不是1或2时，隐藏深度思考按钮
    return selectedBot.value && ![1, 2].includes(Number(selectedBot.value.id));
});

/**
 * 获取当前输入的消息内容
 * @returns {string} 当前输入的消息内容
 */
const getCurrentMessage = () => {
    if (useDynamicForm.value) {
        // 动态表单模式：使用 generatePrompt 获取提示词
        return dynamicFormRef.value?.generatePrompt?.() || '';
    } else {
        // 传统模式：使用文本输入框的内容
        return message.value;
    }
};

/**
 * 清空当前输入内容
 */
const clearCurrentInput = () => {
    message.value = '';
};

// 计算属性：是否可以发送消息
const canSendMessage = computed(() => {
    const currentMessage = getCurrentMessage();
    return currentMessage && currentMessage.trim().length > 0;
});

const handleBotClick = bot => {
    selectBot(bot);
};

/**
 * 处理动态表单值变化
 * @param {Object} data - 表单变化数据
 */
const handleDynamicFormChange = data => {
    dynamicFormValues.value = { ...data.allValues };
    console.log('动态表单值变化:', data);
};

/**
 * 重置动态表单状态
 */
const resetDynamicFormState = () => {
    // 根据当前bot是否支持动态表单来设置初始状态
    useDynamicForm.value = shouldShowDynamicForm.value;
    console.log('重置动态表单状态:', useDynamicForm.value ? '启用' : '禁用');
};

/**
 * 处理动态表单内容变化
 * @param {Object} data - 内容变化数据
 */
const handleDynamicFormContentChange = data => {
    // 检查动态表单内容是否为空
    const content = data.content?.trim() || '';

    console.log('动态表单内容变化:', {
        content: content,
        currentMode: useDynamicForm.value ? '动态表单' : '传统输入',
        shouldShow: shouldShowDynamicForm.value
    });

    // 如果内容为空且当前在动态表单模式，切换到传统输入模式
    if (!content && useDynamicForm.value) {
        console.log('🔄 动态表单内容为空，切换到传统输入模式');
        useDynamicForm.value = false;
    }
    // 如果有内容且当前在传统模式但应该显示动态表单，切换回动态表单模式
    else if (content && !useDynamicForm.value && shouldShowDynamicForm.value) {
        console.log('🔄 动态表单有内容，切换回动态表单模式');
        useDynamicForm.value = true;
    }
};

/**
 * 处理动态表单发送消息事件
 * @param {string} messageContent - 要发送的消息内容
 */
const handleDynamicFormSendMessage = messageContent => {
    // 直接发送消息
    sendMessage(messageContent);
};

/**
 * 切换表单模式（动态表单 <-> 传统输入）
 */
const toggleFormMode = () => {
    console.log(
        '🔄 用户手动切换表单模式:',
        useDynamicForm.value ? '动态表单 -> 传统输入' : '传统输入 -> 动态表单'
    );
    useDynamicForm.value = !useDynamicForm.value;

    // 切换后聚焦到相应的输入框
    nextTick(() => {
        if (!useDynamicForm.value) {
            // 切换到传统输入模式时，聚焦到文本框
            focusMessage();
        }
    });
};

onMounted(async () => {
    const botId = route.query.botId;
    // 获取bot列表
    await chatStore.getBots();

    // 标记为已加载
    isLoaded.value = true;

    chatStore.setSessionId(null);

    // 处理URL中的botId
    setBotFromUrl(botId);

    // 初始化动态表单状态
    resetDynamicFormState();

    // 打字机效果
    typeGreeting();

    // 初始化存储
    await chatStore.initializeStorage();

    // 添加全局点击事件监听
    document.addEventListener('click', handlePageClick);
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
    document.removeEventListener('click', handlePageClick);
});

// 监听选中的bot变化，重置动态表单状态
watch(
    selectedBot,
    (newBot, oldBot) => {
        // 当bot发生变化时，重置动态表单状态
        if (newBot !== oldBot) {
            resetDynamicFormState();
        }
    },
    { immediate: true }
);

/**
 * 检测是否为@ 符号的输入
 * @param {KeyboardEvent} event - 键盘事件对象
 * @returns {boolean} - 是否为@ 符号输入
 */
function isAtSymbolInput(event) {
    // 直接输入@符号
    if (event.key === '@') {
        return true;
    }

    // Windows/Linux: Shift + 2
    if (event.key === '2' && event.shiftKey) {
        return true;
    }

    // macOS: Option + 2
    if (event.key === '2' && event.altKey) {
        return true;
    }

    return false;
}

/**
 * 处理键盘按下事件
 * @param {KeyboardEvent} event - 键盘事件对象
 */
function handleKeyDown(event) {
    const textarea = chatInput.value;
    if (!textarea) {
        return;
    }

    const cursorPosition = textarea.selectionStart;
    const textBeforeCursor = message.value.substring(0, cursorPosition);

    // 检查是否为@符号输入，并且用户未选择elfType为1的bot
    if (isAtSymbolInput(event) && (!selectedBot.value || selectedBot.value.elfType !== 1)) {
        // 只在以下情况触发@选择器：
        // 1. 输入框为空
        // 2. 光标在开始位置
        // 3. 光标前是空格或换行符
        if (
            message.value.length === 0 ||
            cursorPosition === 0 ||
            /[\s\n]$/.test(textBeforeCursor)
        ) {
            event.preventDefault(); // 阻止@符号输入
            showBotSelector.value = true;
            filteredBots.value = [...homeDisplayBots.value];

            // 获取输入框底部位置并设置CSS变量
            nextTick(() => {
                if (botSearchInput.value) {
                    botSearchInput.value.focus();
                }

                // 获取输入框元素
                const inputArea = document.querySelector('.input-area');
                if (inputArea) {
                    const rect = inputArea.getBoundingClientRect();
                    // 设置CSS变量以便用于弹窗位置计算
                    document.documentElement.style.setProperty(
                        '--input-box-bottom',
                        `${rect.bottom}px`
                    );
                }
            });
        }
    }
}

/**
 * 处理输入事件，用于移动端输入法输入@的情况
 * @param {Event} event - 输入事件对象
 */
function handleInput() {
    const textarea = chatInput.value;
    if (!textarea) {
        return;
    }

    // 自动调整输入框高度
    autoHeightChatForm();

    const cursorPosition = textarea.selectionStart;
    const textBeforeCursor = message.value.substring(0, cursorPosition);

    // 检查是否刚输入了@符号（移动端输入法），并且用户未选择elfType为1的bot
    if (textBeforeCursor.endsWith('@') && (!selectedBot.value || selectedBot.value.elfType !== 1)) {
        // 只在以下情况触发@选择器：
        // 1. @是第一个字符
        // 2. @前面是空格或换行符
        if (textBeforeCursor.length === 1 || /[\s\n]@$/.test(textBeforeCursor)) {
            // 删除输入的@符号
            message.value =
                message.value.substring(0, cursorPosition - 1) +
                message.value.substring(cursorPosition);

            // 显示选择器
            showBotSelector.value = true;
            filteredBots.value = [...homeDisplayBots.value];

            // 获取输入框底部位置并设置CSS变量
            nextTick(() => {
                if (botSearchInput.value) {
                    botSearchInput.value.focus();
                }

                // 获取输入框元素
                const inputArea = document.querySelector('.input-area');
                if (inputArea) {
                    const rect = inputArea.getBoundingClientRect();
                    // 设置CSS变量以便用于弹窗位置计算
                    document.documentElement.style.setProperty(
                        '--input-box-bottom',
                        `${rect.bottom}px`
                    );
                }
            });
        }
    }
}

// 过滤Bot列表
const filterBots = () => {
    if (!botSearchQuery.value) {
        filteredBots.value = [...homeDisplayBots.value];
        return;
    }

    filteredBots.value = homeDisplayBots.value.filter(bot =>
        bot.name.toLowerCase().includes(botSearchQuery.value.toLowerCase())
    );
};

const handleBotSelectorBlur = () => {
    // 不要在失焦时立即关闭选择器
    // 而是设置一个标志，表示用户可能正在查看选择器
    // 只有当用户点击其他区域或选择了bot时才关闭
};

// 添加新的点击事件，用于判断是否关闭选择器
const handlePageClick = event => {
    // 如果选择器已显示，且点击的不是选择器或输入框
    if (
        showBotSelector.value &&
        !event.target.closest('.bot-selector-popup') &&
        !event.target.closest('.input-area')
    ) {
        showBotSelector.value = false;
        botSearchQuery.value = '';
    }
};

// 监听搜索框内容变化
watch(botSearchQuery, () => {
    filterBots();
});

// 从弹窗中选择Bot
const selectBotFromPopup = bot => {
    // 关闭弹窗
    showBotSelector.value = false;
    botSearchQuery.value = '';

    // 选择Bot
    selectBot(bot);
};

/**
 * 聚焦输入框
 */
const focusMessage = () => {
    chatInput.value?.focus();
};

// 自动调整输入框高度
const autoHeightChatForm = () => {
    nextTick(() => {
        if (chatInput.value) {
            chatInput.value.style.height = '28px';
            chatInput.value.style.height = Math.min(chatInput.value.scrollHeight, 130) + 'px';
        }
    });
};

// 处理回车键, 换行或发送消息, Shift + Enter 换行, Enter 发送消息
const handleEnterKey = event => {
    // 用户正在使用输入法输入，不做任何处理
    if (event.isComposing || isComposing.value || event.keyCode === 229) {
        return;
    }

    if (event.shiftKey) {
        // 如果按下了 Shift + Enter，则插入换行
        const textarea = chatInput.value;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        message.value = message.value.substring(0, start) + '\n' + message.value.substring(end);
        nextTick(() => {
            // 移动光标到新行
            textarea.selectionStart = textarea.selectionEnd = start + 1;
            // 自动调整输入框高度
            autoHeightChatForm();
            // 确保光标在可视区域内
            textarea.scrollTop = textarea.scrollHeight;
        });
    } else {
        // 如果只是 Enter，则发送消息
        if (homeDisplayBots.value.length > 0) {
            // 如果消息为空，不执行任何操作
            const currentMessage = getCurrentMessage();
            if (!currentMessage.trim()) {
                return;
            }

            // 开始聊天
            startChat();
        }
    }
};

/**
 * 发送消息的通用方法
 * @param {string} messageContent - 要发送的消息内容
 */
const sendMessage = messageContent => {
    if (!messageContent || !messageContent.trim()) {
        return;
    }

    const trimmedMessage = messageContent.trim();

    // 将临时消息存储到store中
    chatStore.setTempMessage(trimmedMessage);

    // 清空当前输入内容
    clearCurrentInput();

    // 跳转到对应的chat-bot页面
    router.push({
        path: '/smart-chat/chat-bot',
        query: {
            botId: selectedBot.value?.id || 1
        }
    });
};

/**
 * 开始聊天，根据当前输入模式获取消息内容并发送
 */
const startChat = () => {
    // 如果已经有选中的bot，并且elfType为1，直接处理消息
    if (!selectedBot.value || (selectedBot.value && selectedBot.value.elfType === 1)) {
        const messageContent = getCurrentMessage();
        sendMessage(messageContent);
    }
};

// 处理输入法开始输入
const handleCompositionStart = () => {
    isComposing.value = true;
};

// 处理输入法输入结束
const handleCompositionEnd = () => {
    isComposing.value = false;
};

/**
 * 处理推荐问题的选择
 * @param {string} question - 选中的问题文本
 */
const handleQuestionSelect = question => {
    // 推荐问题直接发送，不受当前输入模式影响
    sendMessage(question);
};

const handleNewChat = () => {
    // 只有当选中了bot时才执行操作
    if (selectedBot.value) {
        // 清空选中的 bot
        chatStore.setSelectedElfBot(null);

        // 清空当前会话ID
        chatStore.clearCurrentSessionId();

        selectedSessionId.value = null;

        // 重置动态表单状态
        resetDynamicFormState();

        // 清空当前输入内容
        clearCurrentInput();

        // 清空问候语
        typeGreeting();
    }
};

const handleHistorySelect = () => {
    // 跳转到对应的chat-bot页面
    router.push({
        path: '/smart-chat/chat-bot'
    });
};
</script>

<style lang="scss" scoped>
/* 基础布局 */
.home-page {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    font-weight: 500;
    position: relative;
    background: linear-gradient(160deg, rgba(255, 255, 255, 0.95) 0%, rgb(242, 242, 247) 100%);
}

.home-content {
    padding: 120px 16px 20px; /* 调整顶部padding，给header留出空间 */
    width: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    overflow-y: auto;
}

/* 问候语区域 */
.greeting-text {
    font-size: 22px;
    color: $label-primary;
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    div {
        display: inline-block;
        position: relative;
    }

    .gradient-text {
        background: linear-gradient(90deg, $system-blue, $system-teal);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
    }

    .normal-text {
        color: $label-primary;
        font-size: 20px;
    }

    .cursor-container {
        display: inline-block;
        width: 14px;
        position: relative;
    }

    .cursor {
        position: absolute;
        left: 0;
        bottom: -2px;
        display: inline-block;
        width: 14px;
        height: 3px;
        margin-left: 4px;
        background: linear-gradient(90deg, $system-blue, $system-teal);
        animation: cursor-blink 0.8s infinite;
    }
}

/* 动画定义 */
@keyframes cursor-blink {
    0%,
    100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

/* 聊天输入区域 */
.chat-input-area {
    margin-bottom: 14px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.user-input {
    width: 100%;
    padding: 0;
    position: relative;

    .user-input-chat {
        width: 100%;

        .main {
            width: 100%;

            .input-area {
                width: 100%;
                padding: 12px 16px 12px;
                border: 1px solid transparent;
                border-radius: 16px;
                display: flex;
                flex-direction: column;
                gap: 12px;
                background: linear-gradient($system-background-primary, $system-background-primary)
                        padding-box,
                    linear-gradient(135deg, $system-blue, $system-teal) border-box;
                transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
                will-change: transform;

                /* Bot头部行样式 */
                .bot-header-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    gap: 12px;
                }

                /* 表单模式切换按钮样式 */
                .form-mode-toggle {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 32px;
                    cursor: pointer;
                    flex-shrink: 0;
                    font-size: 12px;
                    color: $label-secondary;
                    gap: 4px;

                    img {
                        width: 18px;
                        height: 18px;
                    }
                }

                /* 选中的bot标签样式 */
                .selected-bot-tag {
                    display: flex;
                    align-items: center;
                    border-radius: 16px;
                    padding: 4px 8px;
                    flex-shrink: 0;
                    font-size: 14px;
                    background: linear-gradient(
                        135deg,
                        rgba(255, 107, 53, 0.12),
                        rgba(255, 132, 102, 0.08)
                    );
                    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
                    width: fit-content;
                    cursor: pointer;
                    border: 1px solid rgba(255, 107, 53, 0.15);
                    box-shadow: 0 1px 4px rgba(255, 107, 53, 0.1);
                    // max-width: 120px; /* 限制最大宽度 */

                    &:hover {
                        background: linear-gradient(
                            135deg,
                            rgba(255, 107, 53, 0.18),
                            rgba(255, 132, 102, 0.12)
                        );
                        border-color: rgba(255, 107, 53, 0.25);
                        box-shadow: 0 3px 12px rgba(255, 107, 53, 0.15);
                        transform: translateY(-1px);
                    }

                    .bot-name {
                        color: #ff4500;
                        font-weight: 600;
                        margin-right: 4px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .change-bot {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-right: 6px;

                        img {
                            width: 18px;
                            height: 18px;
                            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
                        }
                    }
                }

                .textarea-container {
                    width: 100%;
                }

                .dynamic-form-container {
                    width: 100%;
                    flex: 1;

                    :deep(.dynamic-form) {
                        background-color: transparent;
                        border: none;
                        padding: 0;
                    }
                }

                textarea {
                    display: block;
                    width: 100%;
                    color: $label-primary;
                    min-height: 28px;
                    height: 28px;
                    line-height: 28px;
                    max-height: 130px;
                    padding: 0;
                    border: none;
                    outline: none;
                    resize: none;
                    background-color: transparent;
                    font-size: 14px;
                    padding-right: 5px;
                    caret-color: $system-blue;

                    &::placeholder {
                        color: $label-secondary;
                        font-size: 14px !important;
                    }
                }

                /* 底部功能区 */
                .input-bottom-area {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    padding-top: 4px;
                    margin-top: 4px;
                }
            }
        }
    }
}

/* Bot选择弹窗样式 */
.bot-selector-popup {
    position: fixed;
    left: 20px;
    right: 20px;
    max-width: 800px;
    background-color: $system-background-primary;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    margin: 0 auto;
    z-index: 9999;
    overflow: hidden;
    top: calc(var(--input-box-bottom, 50%) + 10px);
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.03);
    will-change: transform, opacity;

    /* 添加显隐动画 */
    animation: popupFadeIn 0.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;

    /* 添加其他相关样式 */
    @keyframes popupFadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .bot-search {
        padding: 16px 16px 12px;

        input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e5e5ea;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) inset;

            &:focus {
                border-color: $system-blue;
                background-color: #ffffff;
                box-shadow: 0 0 0 3px rgba(10, 122, 255, 0.1);
            }

            &::placeholder {
                color: $label-tertiary;
                font-size: 14px;
            }
        }
    }

    .bot-popup-list {
        max-height: 280px;
        overflow-y: auto;
        padding-bottom: 16px;

        .bot-popup-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0 8px;
            border-radius: 12px;

            &:hover {
                background-color: $system-grouped-background-tertiary;
            }

            .bot-icon {
                width: 24px;
                height: 24px;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
            }

            span {
                font-size: 14px;
                color: $label-primary;
                font-weight: 500;
            }
        }

        .empty-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 24px 0;

            .empty-icon {
                color: $system-gray;
                font-size: 24px;
                width: 48px;
                height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: $system-gray6;
                border-radius: 50%;
                margin-bottom: 8px;
            }

            .empty-text {
                font-size: 14px;
                color: $label-secondary;
                text-align: center;
            }
        }
    }
}

/* 新增输入内容行样式 */
.input-content-row {
    display: flex;
    align-items: flex-end;
    width: 100%;

    &--column {
        flex-direction: column;
        gap: 12px;
    }

    .textarea-container {
        flex: 1;
    }
}

/* 深度思考按钮容器 */
.deep-thinking-container {
    display: flex;
    margin-top: 10px;
    padding: 0 2px;
}

// 修改 send-btn-container，移除不需要的样式
.send-btn-container {
    display: flex;
    align-items: center;

    .sending {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(135deg, $system-blue, $system-teal);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        cursor: pointer;
        box-shadow: 0 4px 8px rgba(10, 122, 255, 0.2);
        will-change: transform;

        img {
            width: 14px;
            height: 14px;
        }

        &--disabled {
            background: $system-gray5;
            opacity: 0.7;
            cursor: not-allowed;
            box-shadow: none;

            &:active {
                transform: none;
            }
        }
    }
}

/* 添加加载中状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    gap: 16px;

    img {
        width: 36px;
        height: 36px;
        animation: spin 1.5s linear infinite;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    span {
        font-size: 16px;
        color: $label-secondary;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
