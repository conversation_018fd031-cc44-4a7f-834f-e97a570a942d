<template>
    <section class="chat-content">
        <div class="chat-content-inner">
            <!-- 新增 header 部分 -->
            <header class="chat-header">
                <div class="new-session-btn" @click="handleNewSession">
                    <img :src="PLUS_ICON" alt="新建会话" />
                    <span>新会话</span>
                </div>
                <!-- 添加历史消息按钮 -->
                <div class="history-button" @click="showSessionDrawer = true">
                    <img :src="PLUS_GRAY_ICON" alt="查看历史会话" />
                    <span>查看历史会话</span>
                </div>
            </header>
            <PreSet
                v-if="chatForm.showPreset"
                @sendDirectly="message => sendMessage(message)"
                :bot-id="selectedElfBot ? selectedElfBot.id : chatStore.defaultBot?.id || 1"
            />
            <div v-else class="conversation" @scroll="handleScroll">
                <!-- 修改加载更多提示 -->
                <div
                    class="loading-more"
                    :class="{ 'loading-more--visible': chatForm.loadingMore }"
                >
                    <img :src="LOADING_ICON" alt="loading" />
                    <span>加载更多消息...</span>
                </div>
                <!-- 修改没有更多消息提示 -->
                <div
                    v-if="chatForm.loadedAllMessages && messages.length > 0"
                    class="no-more-messages"
                >
                    没有更多消息了
                </div>
                <template v-if="!chatForm.loading">
                    <section
                        class="item"
                        :class="{ 'item--with-iframe': message.innerPageUrl && !message.sending }"
                        v-for="message in messages"
                        :key="message.id"
                    >
                        <div class="item-right">
                            <div class="user-query">
                                <div class="content">
                                    <div>{{ message.questionContent }}</div>
                                </div>
                            </div>
                            <div class="response">
                                <div class="model-response">
                                    <div class="content">
                                        <ThinkingProcess
                                            v-if="message.thinkContent"
                                            :think-content="message.thinkContent"
                                            :is-finished="!message.thinking"
                                            :reference-count="message.referenceList.length"
                                            @showReference="showReference(message)"
                                        />
                                        <div
                                            v-if="message.newborn"
                                            class="markdown-body"
                                            :id="message.id"
                                        ></div>
                                        <div
                                            v-else-if="
                                                !message.sending &&
                                                !message.newborn &&
                                                (!message.thinking || message.answerContent)
                                            "
                                            class="markdown-body"
                                            :id="message.id"
                                            v-html="
                                                renderMarkdown(
                                                    message.answerContent ||
                                                        getEmptyAnswerContent(message)
                                                )
                                            "
                                        ></div>

                                        <!-- 添加内联iframe显示 -->
                                        <InlineContainer
                                            v-if="message.innerPageUrl && !message.sending"
                                            :questionContent="message.questionContent"
                                            :reportSuffix="' - 分析报告'"
                                            :expanded="isIframeExpanded(message.id)"
                                            :src="message.innerPageUrl"
                                            :needTicket="true"
                                            @expand="handleIframeExpand(message.id)"
                                            @fullscreen="
                                                isFullscreen =>
                                                    handleFullscreen(message.id, isFullscreen)
                                            "
                                            class="inline-iframe-container"
                                        >
                                            <IframeView
                                                v-if="isIframeExpanded(message.id)"
                                                :src="message.innerPageUrl"
                                                :need-ticket="true"
                                                @loaded="
                                                    loaded => handleIframeLoaded(message.id, loaded)
                                                "
                                            />
                                        </InlineContainer>
                                    </div>
                                    <div class="tools-container">
                                        <div
                                            class="message-tools"
                                            v-if="
                                                !message.sending &&
                                                message.answerContent &&
                                                !message.isStopped &&
                                                !message.isError
                                            "
                                        >
                                            <div
                                                class="tool-item"
                                                @click="copy($event, message.answerContent)"
                                            >
                                                <img :src="COPY_ICON" alt="copy" />
                                            </div>
                                        </div>
                                        <ReferenceButton
                                            v-if="
                                                !message.sending && message.referenceList.length > 0
                                            "
                                            :count="message.referenceList.length"
                                            @click="showReference(message)"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </template>
                <!-- 添加参考问题组件，只在最后一个消息后显示 -->
                <ReferenceQuestions
                    class="reference-questions-container"
                    v-if="showReferenceQuestions && messages.length > 0"
                    :bot-id="selectedElfBot ? selectedElfBot.id : chatStore.defaultBot?.id || 1"
                    @sendDirectly="message => sendMessage(message)"
                />
            </div>
            <!-- user-input -->
            <div class="user-input">
                <!-- 知识库选择区域 -->
                <div
                    class="knowledge-base-area"
                    :class="{ 'knowledge-base-area--hidden': !showKnowledgeBaseArea }"
                >
                    <BotSelector
                        v-if="elfBots.length > 0 && !selectedElfBot"
                        @select-bot="selectBot"
                    />
                    <SelectedBot
                        v-if="selectedElfBot"
                        :bot="selectedElfBot"
                        @select-child-bot="selectBot"
                    />
                </div>
                <!-- 聊天部分 -->
                <div class="user-input-chat">
                    <section class="main">
                        <div
                            :class="[
                                'chat-input-container ',
                                {
                                    'input-area--focused': inputFocused,
                                    'with-selected-bot': selectedElfBot && showKnowledgeBaseArea
                                }
                            ]"
                            @click.prevent="focusMessage"
                        >
                            <!-- 文本输入区域和发送按钮行 -->
                            <div class="input-content-row">
                                <!-- 文本输入区域 -->
                                <div class="textarea-container">
                                    <textarea
                                        ref="inputRef"
                                        name="message"
                                        v-model="chatForm.data.message"
                                        @input.prevent="autoHeightChatForm"
                                        @keydown.enter.prevent="handleEnterKey"
                                        @focus="handleFocus"
                                        @blur="handleBlur"
                                        @compositionstart="handleCompositionStart"
                                        @compositionend="handleCompositionEnd"
                                        :disabled="chatForm.loading"
                                        autocomplete="off"
                                        autocapitalize="off"
                                        autocorrect="off"
                                        spellcheck="false"
                                        placeholder="请输入问题..."
                                    ></textarea>
                                </div>

                                <!-- 只有发送按钮的情况下，直接显示在输入框旁边 -->
                                <div v-if="shouldHideDeepThinking" class="send-btn-container">
                                    <div
                                        v-if="chatForm.stop.visibility"
                                        class="stop-sending"
                                        @click="stopSending"
                                    >
                                        <img
                                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/sending-stop.png"
                                            alt=""
                                        />
                                    </div>
                                    <div
                                        v-else
                                        class="sending"
                                        :class="{ 'sending--disabled': isSendDisabled }"
                                        @click="
                                            e => {
                                                e.stopPropagation();
                                                removeFocus();
                                                sendMessage(chatForm.data.message);
                                            }
                                        "
                                    >
                                        <img
                                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/send.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- 底部功能区：深度思考按钮和发送按钮 -->
                            <div v-if="!shouldHideDeepThinking" class="input-bottom-area">
                                <DeepThinkingButton />
                                <div class="send-btn-container">
                                    <div
                                        v-if="chatForm.stop.visibility"
                                        class="stop-sending"
                                        @click="stopSending"
                                    >
                                        <img
                                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/sending-stop.png"
                                            alt=""
                                        />
                                    </div>
                                    <div
                                        v-else
                                        class="sending"
                                        :class="{ 'sending--disabled': isSendDisabled }"
                                        @click="
                                            e => {
                                                e.stopPropagation();
                                                removeFocus();
                                                sendMessage(chatForm.data.message);
                                            }
                                        "
                                    >
                                        <img
                                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/send.svg"
                                            alt=""
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- 添加蒙层，当接口加载中时显示，移到整个聊天界面 -->
            <div class="input-overlay" v-if="chatForm.loading">
                <div class="loading-indicator">
                    <img :src="LOADING_ICON" alt="loading" />
                </div>
            </div>

            <PanelContainer
                v-model="showBottomPanel"
                :heightPercent="panelHeightPercent"
                :showBackButton="true"
                @back="resetPanel"
            >
                <RouterView
                    v-if="showRouterView"
                    :initialPath="routerPath"
                    @close="closeRouterView"
                />

                <IframeView
                    v-if="showIframeView"
                    :src="iframeConfig.src"
                    :iframeProps="iframeConfig.props"
                />
            </PanelContainer>

            <!-- 添加参考资料抽屉组件 -->
            <ReferenceDrawer v-model="showReferenceDrawer" :references="currentReferences" />

            <!-- 添加会话抽屉组件 -->
            <SessionDrawer
                v-model="showSessionDrawer"
                @newChat="handleNewSession"
                @historySelect="handleHistorySelect"
            />
        </div>
    </section>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi';
import { ref, nextTick, onMounted, onUnmounted, computed, onBeforeUnmount, watchEffect } from 'vue';
import { useToast } from 'primevue/usetoast';
import useChatStore from '@/stores/chat';
import { DiffDOM } from 'diff-dom';
import { isDingTalk, isDingTalkMobile, userAgentIncludes } from '@/utils/index';

import { useRoute, useRouter } from 'vue-router';
import { useBotSelector } from '@/composables/useBotSelector';
import { useIframeStore } from '@/stores/iframe';

import PreSet from '@/views/smart-chat/components/preset/PreSet.vue';
import BotSelector from '@/views/smart-chat/components/bot/BotSelector.vue';
import SelectedBot from '@/views/smart-chat/components/bot/SelectedBot.vue';
import ReferenceQuestions from '@/views/smart-chat/components/reference/ReferenceQuestions.vue';
import PanelContainer from '@/views/smart-chat/components/panel/container.vue';
import RouterView from '@/views/smart-chat/components/panel/router.vue';
import IframeView from '@/views/smart-chat/components/panel/iframe.vue';
import InlineContainer from '@/views/smart-chat/components/panel/inline-container.vue';
import ThinkingProcess from '@/views/smart-chat/components/deepseek/ThinkingProcess.vue';
import ReferenceDrawer from '@/views/smart-chat/components/reference/ReferenceDrawer.vue';
import DeepThinkingButton from '@/views/smart-chat/components/deepseek/DeepThinkingButton.vue';
import ReferenceButton from '@/views/smart-chat/components/reference/ReferenceButton.vue';
import SessionDrawer from '@/views/smart-chat/components/history/SessionDrawer.vue';

import { scrollToBottom, copyToClipboard, md, fixMarkdownCodeBlocks } from '@/utils/chat';

// 常量定义
const COPY_ICON =
    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/copy3-000.png';
const COPY_SUCCESS_ICON =
    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/copy-success2.png';
const LOADING_ICON =
    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/loading.png';
const PLUS_ICON = 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/plus.png';
const PLUS_GRAY_ICON =
    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/history2.png';
const SEND_ICON = 'https://z.autoimg.cn/dealer_microfe_aidev/assets/aiwenpingan-chat-vue/send.svg';

// 创建实例和钩子
const route = useRoute();
const router = useRouter();
const toast = useToast();
const chatStore = useChatStore();
const diffDom = new DiffDOM();

// 状态和引用
const inputRef = ref(null);
const isComposing = ref(false);
const showScrollBottom = ref(false);
const showSessionDrawer = ref(false);
const showReferenceQuestions = ref(false);
const showReferenceDrawer = ref(false);
const currentReferences = ref([]);
const scroll = ref({ autoScroll: false });
const expandedIframeIds = ref([]); // 存储所有展开的iframe ID

// 计算属性
const messages = computed(() => chatStore.messages);
const chatForm = computed(() => chatStore.chatForm);
const elfBots = computed(() => chatStore.elfBots);
const selectedElfBot = computed(() => chatStore.selectedElfBot);
// const sessionId = computed(() => route.query.sessionId);
const isSendDisabled = computed(() => {
    // 禁用发送按钮的条件：
    // 1. 输入框为空
    // 2. 正在加载中
    return !chatForm.value.data.message.trim().length || chatForm.value.loading;
});

// 使用组合式函数
const {
    selectedElfBot: selectedBot,
    showBottomPanel,
    panelHeightPercent,
    showRouterView,
    showIframeView,
    routerPath,
    iframeConfig,
    selectBot,
    resetPanel,
    closeRouterView,
    setBotFromUrl
} = useBotSelector({
    onClose: () => {
        if (isDingTalk()) {
            dd.setNavigationTitle({
                title: '家家精灵'
            });
        }
    }
});

// 获取 iframe store
const iframeStore = useIframeStore();

// 监听 iframe 消息变化
watchEffect(() => {
    // 只处理未处理的消息
    if (!iframeStore.isMessageProcessed && iframeStore.latestTextMessage) {
        const message = iframeStore.latestTextMessage;

        // 自动调整输入框高度
        nextTick(() => {
            autoHeightChatForm();

            sendMessage(message);

            // 标记消息已处理
            iframeStore.markMessageAsProcessed();

            showBottomPanel.value = false;
            showIframeView.value = false;
        });
    }
});

// 辅助函数
/**
 * 预加载图片
 */
function preloadImages() {
    [COPY_ICON, COPY_SUCCESS_ICON, LOADING_ICON, PLUS_ICON, PLUS_GRAY_ICON, SEND_ICON].forEach(
        src => {
            const img = new Image();
            img.src = src;
        }
    );
}

/**
 * 渲染Markdown内容
 * @param {string} markdownContent - markdown内容
 * @returns {string} 渲染后的HTML
 */
function renderMarkdown(markdownContent) {
    let renderedContent = md.render(fixMarkdownCodeBlocks(markdownContent));

    // 使用正则表达式将表格包裹在具有滚动功能的div中
    renderedContent = renderedContent
        .replace(/<table>/g, '<div class="table-wrapper"><table>')
        .replace(/<\/table>/g, '</table></div>');

    return renderedContent;
}

/**
 * 获取空回答时的默认内容
 * @param {Object} message - 消息对象
 * @returns {string} 默认内容
 */
function getEmptyAnswerContent(message) {
    // 如果正在思考中，不显示空回答提示
    if (message.thinking) {
        return '';
    }

    // 根据消息状态返回不同的提示文案
    if (message.isError) {
        return '<div class="empty-answer-content error">🚫 抱歉，回答生成失败，请重试一下~</div>';
    } else if (message.isStopped) {
        return '<div class="empty-answer-content stopped">⏹️ 回答生成已停止</div>';
    } else {
        return '<div class="empty-answer-content default">💭 抱歉，暂时无法为您提供相关回答，请换个问题试试~</div>';
    }
}

/**
 * 设置会话滚动到底部
 */
function setConversationToBottom() {
    scrollToBottom('.chat-content-inner .conversation');
}

/**
 * 复制文本到剪贴板
 * @param {Event} event - 点击事件
 * @param {string} input - 要复制的文本
 */
async function copy(event, input) {
    const toolsElement = event.currentTarget;
    const imgElement = toolsElement.querySelector('img');

    try {
        await copyToClipboard(input);
        // 更改图标
        imgElement.src = COPY_SUCCESS_ICON;

        toast.add({
            severity: 'success',
            summary: '复制成功',
            detail: '已复制到剪贴板',
            life: 1000
        });

        // 1.5秒后恢复原图标
        setTimeout(() => {
            imgElement.src = COPY_ICON;
        }, 1500);
    } catch (err) {
        toast.add({
            severity: 'error',
            summary: '复制失败',
            detail: err,
            life: 1000
        });
    }
}

/**
 * 更新消息状态
 * @param {string} messageId - 消息ID
 * @param {string} status - 消息状态
 */
function updateMessageStatus(messageId, status) {
    const statusTemplates = {
        thinking: `<div class="message-status thinking">
                        <img class="status-img" src="${LOADING_ICON}" />
                        <div class="status-text">正在思考中...</div>
                      </div>`,
        generating: `<div class="message-status generating">
                          <img class="status-img" src="${LOADING_ICON}" />
                          <div class="status-text">正在生成回答...</div>
                        </div>`,
        error: `<div class="message-status error">
                     <i class="pi pi-exclamation-circle status-icon"></i>
                     <div class="status-text">回答超时啦，请重试一下~</div>
                   </div>`,
        stopped: `<div class="message-status stopped">
                       <i class="pi pi-ban status-icon"></i>
                       <div class="status-text">已停止生成回答</div>
                     </div>`
    };

    document.getElementById(messageId).innerHTML = statusTemplates[status];
}

/**
 * 更新大模型回复内容 - answercontent 字段内容
 * @param {string} messageId - 消息ID
 * @param {string} content - 更新的内容
 */
function renderAnswerContent(messageId, content) {
    const oldDom = document.getElementById(messageId);
    const newDom = document.createElement('div');
    Array.from(oldDom.attributes).forEach(attr => {
        newDom.setAttribute(attr.name, attr.value);
    });

    // 如果content为空，使用默认的空回答提示
    let renderContent = content;
    if (!content && content !== null && content !== undefined) {
        // 从消息列表中找到对应的消息对象，以获取其状态
        const message = messages.value.find(msg => msg.id === messageId);
        renderContent = getEmptyAnswerContent(message || {});
    }

    newDom.innerHTML = renderMarkdown(
        renderContent || '<div class="message-status message-status--placeholder">略</div>'
    );
    const diff = diffDom.diff(oldDom, newDom);
    diffDom.apply(oldDom, diff);
}

/**
 * 统一更新消息内容
 * @param {Object} message - 消息对象
 * @param {Object} data - 更新数据
 */
function updateMessage(message, data) {
    // 更新回答内容（包括null值）
    if (data.answerContent !== undefined) {
        message.answerContent = data.answerContent;
    }

    // 更新思考状态
    if (data.thinkContent) {
        message.thinking = true;
        message.thinkContent = data.thinkContent;
    }

    // 停止思考状态 - 当开始有answerContent时或者明确结束时
    if (data.answerContent) {
        message.thinking = false;
    }

    // 更新引用列表
    if (data.referenceList !== undefined) {
        message.referenceList = data.referenceList || [];
    }

    // 更新弹窗页面URL
    if (data.popupPageUrl !== undefined) {
        message.popupPageUrl = data.popupPageUrl;
    }

    // 更新内联页面URL
    if (data.innerPageUrl !== undefined) {
        message.innerPageUrl = data.innerPageUrl;
        message.iframeLoaded = false;

        // 自动展开当前的的innerPageUrl
        if (!expandedIframeIds.value.includes(message.id)) {
            expandedIframeIds.value.push(message.id);
        }
    }

    // 更新发送状态
    if (data.sending !== undefined) {
        message.sending = data.sending;
    }

    // 更新错误状态
    if (data.isError !== undefined) {
        message.isError = data.isError;
    }

    // 更新停止状态
    if (data.isStopped !== undefined) {
        message.isStopped = data.isStopped;
    }
}

/**
 * 根据需要自动滚动
 * @param {Object} message - 消息对象
 */
function autoScrollIfNeeded(message) {
    if (!scroll.value.autoScroll && message.answerContent?.length % 10 === 0) {
        setConversationToBottom();
    }
}

/**
 * 创建并显示消息
 * @param {string} messageContent - 消息内容
 * @returns {Object} 创建的消息对象
 */
function createAndShowMessage(messageContent) {
    const tempMessage = {
        id: `temp-${Date.now()}`,
        questionContent: messageContent,
        answerContent: '',
        sending: true,
        newborn: true,
        referenceList: [],
        iframeLoaded: false
    };

    messages.value.push(tempMessage);
    nextTick(() => {
        updateMessageStatus(tempMessage.id, 'thinking');
        setConversationToBottom();
    });

    return tempMessage;
}

/**
 * 重置输入状态
 */
function resetInputState() {
    chatForm.value.data.message = '';
    autoHeightChatForm();
}

// 输入框相关功能
/**
 * 聚焦消息输入框
 */
function focusMessage() {
    autoHeightChatForm();
    inputRef.value.focus();
}

/**
 * 自动调整输入框高度
 */
function autoHeightChatForm() {
    nextTick(() => {
        inputRef.value.style.height = '28px';
        inputRef.value.style.height = inputRef.value.scrollHeight + 'px';
    });
}

const inputFocused = ref(false);
/**
 * 输入框聚焦处理
 */
function handleFocus() {
    inputFocused.value = true;
}

/**
 * 输入框失焦处理
 */
function handleBlur() {
    // 添加微小延迟，让过渡更平滑
    setTimeout(() => {
        inputFocused.value = false;
    }, 50);
}

/**
 * 处理输入法开始输入
 */
function handleCompositionStart() {
    isComposing.value = true;
}

/**
 * 处理输入法输入结束
 */
function handleCompositionEnd() {
    isComposing.value = false;
}

/**
 * 处理Enter键按下
 * @param {KeyboardEvent} event - 键盘事件
 */
function handleEnterKey(event) {
    // 如果正在加载中，不处理任何操作
    if (chatForm.value.loading) {
        return;
    }

    //  用户正在使用输入法输入，不做任何处理
    if (event.isComposing || isComposing.value || event.keyCode === 229) {
        return;
    }

    if (event.shiftKey) {
        // 如果按下了 Shift + Enter，则插入换行
        const textarea = inputRef.value;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        chatForm.value.data.message =
            chatForm.value.data.message.substring(0, start) +
            '\n' +
            chatForm.value.data.message.substring(end);
        nextTick(() => {
            // 移动光标到新行
            textarea.selectionStart = textarea.selectionEnd = start + 1;
            // 自动调整输入框高度
            autoHeightChatForm();
            // 确保光标在可视区域内
            textarea.scrollTop = textarea.scrollHeight;
        });
    } else {
        // Enter 发送消息逻辑
        sendMessage(chatForm.value.data.message);
    }
}

// 滚动处理
/**
 * 处理会话区域滚动
 * @param {Event} e - 滚动事件
 */
function handleScroll(e) {
    const scrollTop = e.target.scrollTop;
    const scrollHeight = e.target.scrollHeight;
    const clientHeight = e.target.clientHeight;

    // 当向上滚动超过一屏高度时显示置底按钮
    showScrollBottom.value = scrollHeight - scrollTop - clientHeight > 200;

    // 如果消息数量小于分页大小，禁用滚动加载功能
    if (messages.value.length < chatForm.value.data.messages.pageSize) {
        return;
    }

    // 检查是否已经加载完所有消息
    if (chatForm.value.loadedAllMessages) {
        return;
    }

    // 检查是否正在加载更多消息
    if (chatForm.value.loadingMore) {
        return;
    }

    // 检查是否滚动到顶部（考虑一个小的阈值）
    const scrollThreshold = 5;

    if (scrollTop < scrollThreshold) {
        loadMoreHistoryMessages();
    }
}

// API交互相关
/**
 * 发送消息到服务器并更新状态
 * @param {Object} tempMessage - 临时消息对象
 * @param {string} messageContent - 消息内容
 * @returns {Promise<Object>} 服务器返回的消息对象
 */
async function sendMessageToServer(tempMessage, messageContent) {
    const requestData = { questionContent: messageContent };
    const response = await chatStore.createMessage(requestData);
    const serverMessage = response.result;

    // 更新会话ID
    if (serverMessage.sessionId) {
        chatStore.setSessionId(serverMessage.sessionId);
    }

    // 替换临时消息
    const messageIndex = messages.value.findIndex(msg => msg.id === tempMessage.id);
    if (messageIndex !== -1) {
        messages.value[messageIndex] = {
            ...serverMessage,
            sending: true,
            newborn: true,
            iframeLoaded: false
        };
    }

    return serverMessage;
}

/**
 * 统一的错误处理
 * @param {Error} err - 错误对象
 * @param {Object} options - 选项
 */
function handleError(err, options = {}) {
    console.error('处理错误:', err);

    // 关闭SSE连接
    if (chatForm.value.eventSource) {
        chatForm.value.eventSource.close();
        chatForm.value.eventSource = null;
    }

    chatForm.value.stop.visibility = false;

    if (options.messageIndex !== undefined) {
        // 更新消息错误状态
        const message = messages.value[options.messageIndex];
        if (message) {
            updateMessage(message, { sending: false, isError: true });
            nextTick(() => {
                updateMessageStatus(message.id, 'error');
                setConversationToBottom();
            });
        }
    } else {
        // 移除临时消息
        messages.value.pop();
    }

    // 显示错误提示
    toast.add({
        severity: 'error',
        summary: '错误',
        detail: err.message || '操作失败，请重试',
        life: 3000
    });
}
/**
 * 处理消息流
 * @param {Object} message - 消息对象
 * @param {number} messageIndex - 消息索引
 */
function handleMessageStream(message, messageIndex) {
    chatForm.value.stop.visibility = true;

    // 构建SSE URL
    const useDeepThink =
        selectedElfBot.value?.id === 10 ||
        selectedElfBot.value?.id === 11 ||
        selectedElfBot.value?.id === 12 ||
        selectedElfBot.value?.id === 13
            ? false
            : chatStore.deepThinking || false;
    const sseUrl = `/api/chatMessage/send?messageId=${message.id}&deepThink=${useDeepThink}`;

    // 创建SSE连接
    const source = new EventSource(sseUrl);
    source.retry = 24 * 60 * 60 * 1000;
    chatForm.value.eventSource = source;

    // 消息更新处理
    source.onmessage = e => {
        const data = JSON.parse(e.data);
        const currentMessage = messages.value[messageIndex];

        updateMessage(currentMessage, data);
        renderAnswerContent(currentMessage.id, data.answerContent);

        nextTick(() => {
            fixTableScroll(currentMessage.id);
        });

        autoScrollIfNeeded(currentMessage);
    };

    // 生成结束处理
    source.addEventListener('end', e => {
        source.close();
        const data = JSON.parse(e.data);
        const currentMessage = messages.value[messageIndex];

        updateMessage(currentMessage, {
            ...data,
            sending: false
        });

        // 自动打开弹窗页面
        if (data.popupPageUrl) {
            handleLinkClick(data.popupPageUrl);
        }

        chatForm.value.stop.visibility = false;

        nextTick(() => {
            fixTableScroll(currentMessage.id);
            setConversationToBottom();
        });
    });

    // 错误处理
    source.onerror = err => {
        handleError(err, { messageIndex });
    };
}

// 消息发送与停止
/**
 * 移除当前焦点，用于解决iOS键盘不收起问题
 */
function removeFocus() {
    // 清除焦点的2种方式，提高不同iOS设备上的兼容性
    if (inputRef.value) {
        inputRef.value.blur();
    }
    // 兼容 ios
    if (document.activeElement) {
        // @ts-ignore
        document.activeElement.blur();
    }
}

/**
 * 主发送消息函数
 * @param {string} directMessage - 直接传入的消息内容
 */
async function sendMessage(directMessage) {
    // 验证和预处理
    if (chatForm.value.loading || !directMessage?.trim()) {
        return;
    }

    const messageContent = directMessage.trim();

    // 手动触发输入框失焦，解决iOS上键盘不收起的问题
    removeFocus();

    // 隐藏参考问题和预设问题
    showReferenceQuestions.value = false;
    chatForm.value.showPreset = false;

    // 重置输入状态
    resetInputState();

    // 创建并显示临时消息
    const tempMessage = createAndShowMessage(messageContent);

    try {
        // 发送消息到服务器
        const serverMessage = await sendMessageToServer(tempMessage, messageContent);

        // 显示生成中状态
        nextTick(() => {
            updateMessageStatus(serverMessage.id, 'generating');
            setConversationToBottom();
        });

        // 开始消息流处理
        const messageIndex = messages.value.findIndex(msg => msg.id === serverMessage.id);
        handleMessageStream(serverMessage, messageIndex);
    } catch (err) {
        handleError(err);
    }
}

/**
 * 停止生成回答
 * @param {Event} e - 点击事件
 */
function stopSending(e) {
    e.stopPropagation(); // 阻止事件冒泡 防止触发父元素的点击事件

    // 手动触发输入框失焦，解决iOS上键盘不收起的问题
    removeFocus();

    if (chatForm.value.eventSource) {
        // 关闭连接
        chatForm.value.eventSource.close();
        chatForm.value.eventSource = null;

        // 更新状态
        chatForm.value.stop.visibility = false;

        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage) {
            updateMessage(lastMessage, { sending: false, isStopped: true });
            updateMessageStatus(lastMessage.id, 'stopped');
        }
    }
}

// 历史消息相关
/**
 * 加载更多历史消息
 * 用于有会话ID时，加载该会话的更多历史记录
 */
async function loadMoreHistoryMessages() {
    // 如果正在加载，直接返回
    if (chatForm.value.loadingMore) {
        return;
    }

    // 设置加载状态
    chatForm.value.loadingMore = true;

    try {
        // 获取当前会话容器元素和第一条消息，用于保持滚动位置
        const conversationElement = document.querySelector('.conversation');
        const oldMessages = conversationElement.querySelectorAll('.item');
        const firstMessage = oldMessages[0];
        const firstMessageRect = firstMessage?.getBoundingClientRect();

        // 计算下一页的页码
        const nextPageIndex = chatForm.value.data.messages.pageIndex + 1;

        // 请求下一页数据，始终带上会话ID
        const result = await chatStore.getMessages({
            pageIndex: nextPageIndex,
            pageSize: chatForm.value.data.messages.pageSize,
            fromHistory: false // 指定不是来自历史页面
        });

        // 更新页码
        chatForm.value.data.messages.pageIndex = nextPageIndex;

        // 检查是否已加载所有消息
        if (
            !result ||
            result.list.length === 0 ||
            result.list.length < chatForm.value.data.messages.pageSize ||
            result.pageindex >= result.pagecount
        ) {
            chatForm.value.loadedAllMessages = true;
        }

        // 等待 DOM 更新完成
        await nextTick();

        // 保持滚动位置
        requestAnimationFrame(() => {
            if (firstMessage && firstMessageRect) {
                const newRect = firstMessage.getBoundingClientRect();
                const scrollAdjustment = newRect.top - firstMessageRect.top;

                conversationElement.scrollBy({
                    top: scrollAdjustment - 30,
                    behavior: 'instant'
                });
            }
        });
    } catch (err) {
        console.error('加载历史消息失败:', err.message);
        toast.add({
            severity: 'error',
            summary: '错误',
            detail: '加载更多消息失败',
            life: 3000
        });
    } finally {
        // 延迟关闭加载状态
        setTimeout(() => {
            chatForm.value.loadingMore = false;
            showReferenceQuestions.value = true;
        }, 500);
    }
}

/**
 * 获取第一屏的历史消息
 */
async function handleGetFirstScreenMessages() {
    chatForm.value.loadedAllMessages = false;
    chatForm.value.loading = true;
    chatForm.value.data.messages.pageIndex = 1;
    chatForm.value.loadingMore = false;

    try {
        // 构建请求参数
        const params = {
            pageIndex: 1,
            pageSize: chatForm.value.data.messages.pageSize,
            fromHistory: false
        };

        const result = await chatStore.getMessages(params);

        if (!result || result.list.length === 0) {
            chatForm.value.showPreset = true;
            showReferenceQuestions.value = false; // 如果显示预设问答，则不显示参考问题
        }

        // 检查是否只有一页数据
        if (
            result &&
            (result.list.length < chatForm.value.data.messages.pageSize ||
                result.pageindex >= result.pagecount)
        ) {
            chatForm.value.loadedAllMessages = true;
        }
    } catch (err) {
        console.error('获取首屏消息失败:', err.message);
    } finally {
        setConversationToBottom();
        chatForm.value.loading = false;
        showReferenceQuestions.value = true;

        // 自动展开最新的iframe（如果存在）
        nextTick(() => {
            const messagesWithIframe = messages.value.filter(m => m.innerPageUrl && !m.sending);
            if (messagesWithIframe.length > 0) {
                const latestMessage = messagesWithIframe[messagesWithIframe.length - 1];
                // 避免重复添加
                if (!expandedIframeIds.value.includes(latestMessage.id)) {
                    expandedIframeIds.value.push(latestMessage.id);
                }
            }
        });
    }
}

// 链接与图片处理
/**
 * 打开内部WebView
 * @param {string} url - 链接地址
 * @param {string} title - 标题
 */
function openInternalWebView(url, title = '外部链接') {
    // 这里可以调用钉钉的API或自定义方法来打开内部WebView
    // 例如使用钉钉的API：
    if (isDingTalk()) {
        dd.biz.util.openLink({
            url: url,
            title: title,
            onSuccess: function () {
                console.log('打开链接成功');
            },
            onFail: function (err) {
                console.error('打开链接失败', err);
                // 如果钉钉API调用失败，可以使用备用方案
            }
        });
    } else {
        // 浏览器端
        openInWindow(url);
    }
}

/**
 * 浏览器端打开链接
 * @param {string} url - 链接地址
 */
function openInWindow(url) {
    window.open(url, '_blank');
}

/**
 * 处理图片点击
 * @param {Event} event - 点击事件
 */
function handleImageClick(event) {
    const target = event.target;

    // 确保target是图片元素
    if (!target || target.tagName.toLowerCase() !== 'img') {
        return;
    }

    // 获取当前会话中所有图片
    const conversation = document.querySelector('.conversation');
    const allImages = Array.from(conversation.querySelectorAll('.markdown-body img'));

    // 获取所有图片URL
    const urls = allImages.map(img => img.src);

    // 获取当前点击图片的索引
    const current = urls.indexOf(target.src);

    if (isDingTalk()) {
        // 调用钉钉预览图片API
        dd.previewImage({
            urls: urls, //图片地址列表
            current: current, // 当前显示的图片索引
            onSuccess: function () {
                console.log('图片预览成功');
            },
            onFail: function (err) {
                console.error('图片预览失败', err);
            }
        });
    } else {
        // 非钉钉环境下使用新窗口打开图片
        window.open(target.src, '_blank');
    }
}

/**
 * 处理链接点击事件
 * @param {Event} event - 点击事件
 */
function handleLinkClick(href) {
    console.log('👋handleLinkClick', href);

    try {
        const urlParts = href.split('?');
        let queryParams = {};
        if (urlParts.length > 1) {
            // 手动解析查询参数
            const searchParams = new URLSearchParams('?' + urlParts[1]);
            searchParams.forEach((value, key) => {
                queryParams[key] = value;
            });
        }

        const elfType = queryParams.elfType || 1;
        const panelHeight = Number(queryParams.panelHeightPercent) || 1;

        // 根据elfType进行不同处理
        switch (Number(elfType)) {
            case 1:
                // 普通链接，直接在新窗口打开
                openInternalWebView(href);
                break;

            case 2:
                // 外部链接 - 通过iframe展示
                showBottomPanel.value = true;
                showIframeView.value = true;
                panelHeightPercent.value = panelHeight;
                iframeConfig.value = {
                    src: href,
                    props: queryParams
                };
                break;

            case 3:
                // 内部路由 - 通过RouterView组件展示
                routerPath.value = urlParts[0];
                console.log('routerPath.value', routerPath.value);

                showBottomPanel.value = true;
                showRouterView.value = true;
                panelHeightPercent.value = panelHeight;

                break;

            case 4:
                // 内部路由push跳转，添加来源标识
                router.push({
                    path: urlParts[0],
                    query: {
                        ...queryParams,
                        fromGenie: '1'
                    }
                });
                break;

            default:
                // 未知elfType，使用默认处理方式
                console.warn(`未知的elfType: ${elfType}，使用默认处理方式`);
                openInternalWebView(href);
        }
    } catch (error) {
        console.warn('链接解析失败，使用默认打开方式', error);
        openInternalWebView(href);
    }
}

/**
 * 处理会话点击事件
 * @param {Event} event - 点击事件
 */
function handleConversationClick(event) {
    const target = event.target;

    // 检查点击是否发生在 markdown-body 内部
    const isInMarkdownBody = target.closest('.markdown-body');
    if (!isInMarkdownBody) {
        return; // 如果不在 markdown-body 内，直接返回不处理
    }

    // 处理图片点击 - 增强图片查找逻辑
    if (target.tagName.toLowerCase() === 'img' || target.querySelector('img')) {
        event.preventDefault();
        // 获取实际的图片元素，可能是target本身或其子元素
        const imgElement =
            target.tagName.toLowerCase() === 'img' ? target : target.querySelector('img');

        if (imgElement) {
            handleImageClick({ ...event, target: imgElement });
            return;
        }
    }

    // 处理链接点击
    const linkTarget = target.closest('a');
    if (linkTarget && linkTarget.href) {
        const originalHref = linkTarget.getAttribute('href');
        console.log('linkTarget.href', linkTarget.href);
        console.log('originalHref', originalHref);

        event.preventDefault();
        handleLinkClick(originalHref);
        return;
    }
}

/**
 * 添加链接点击事件监听
 */
function addLinkListeners() {
    const conversation = document.querySelector('.conversation');
    if (conversation) {
        conversation.addEventListener('click', handleConversationClick);
    }
}

/**
 * 移除链接点击事件监听
 */
function removeLinkListeners() {
    const conversation = document.querySelector('.conversation');
    if (conversation) {
        conversation.removeEventListener('click', handleConversationClick);
    }
}

/**
 * 显示参考资料
 * @param {Object} message - 消息对象
 */
function showReference(message) {
    currentReferences.value = message.referenceList;
    showReferenceDrawer.value = true;
}

/**
 * 处理新会话点击事件
 */
function handleNewSession() {
    chatStore.clearCurrentSessionId();
    chatStore.clearMessages();
    chatStore.setSelectedElfBot(null);
    expandedIframeIds.value = []; // 重置展开状态
    router.push('/smart-chat/home');
}

/**
 * 修复表格横向滚动问题
 * @param {string} messageId - 消息ID
 */
function fixTableScroll(messageId) {
    const messageElement = document.getElementById(messageId);
    if (!messageElement) {
        return;
    }

    // 获取所有表格容器
    const tableWrappers = messageElement.querySelectorAll('.table-wrapper');
    if (tableWrappers.length === 0) {
        return;
    }

    tableWrappers.forEach(wrapper => {
        const table = wrapper.querySelector('table');
        if (!table) {
            return;
        }

        // 检查表格是否需要滚动
        if (table.scrollWidth > wrapper.clientWidth) {
            // 强制触发重新计算布局
            wrapper.style.display = 'none';

            // 使用 requestAnimationFrame 确保样式变更已应用
            requestAnimationFrame(() => {
                wrapper.style.display = 'block';
                // 确保容器的 overflow-x 属性设置正确
                wrapper.style.overflowX = 'auto';
                wrapper.style.display = 'block';
                wrapper.style.width = '100%';

                // 在iOS上可能需要额外触发重绘
                if (
                    userAgentIncludes('iphone') ||
                    userAgentIncludes('ipad') ||
                    userAgentIncludes('ipod')
                ) {
                    wrapper.style.webkitTransform = 'translate3d(0,0,0)';
                    setTimeout(() => {
                        wrapper.style.webkitTransform = '';
                    }, 0);
                }
            });
        }
    });
}

/**
 * 初始化
 */
function init() {
    if (chatStore.tempMessage) {
        sendMessage(chatStore.tempMessage);
        chatStore.clearTempMessage();
    } else {
        // 没有临时消息，正常加载历史消息
        handleGetFirstScreenMessages();
    }
}

// 生命周期钩子
onMounted(async () => {
    await chatStore.getBots();

    // 处理URL中的botId
    setBotFromUrl(route.query.botId);

    // 初始化存储
    await chatStore.initializeStorage();

    init();

    addLinkListeners();

    preloadImages();
});

onUnmounted(() => {
    // 重置聊天状态，确保下次进入页面时是全新的状态
    chatStore.resetState();

    // 重置展开状态
    expandedIframeIds.value = [];

    // 移除链接点击事件监听
    removeLinkListeners();
});

// 在离开页面时重置状态
onBeforeUnmount(() => {
    // 清理可能存在的 EventSource 连接
    if (chatForm.value.eventSource) {
        chatForm.value.eventSource.close();
    }
});

// 设置导航标题
if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '家家精灵'
    });
}

// 添加计算属性：判断是否应该隐藏深度思考按钮
const shouldHideDeepThinking = computed(() => {
    // 当选中的botId不是1或2时，隐藏深度思考按钮
    return selectedElfBot.value && ![1, 2].includes(Number(selectedElfBot.value.id));
});

// 新增计算属性用于控制知识库区域的显示
const isMobile = ref(isDingTalkMobile());
const showKnowledgeBaseArea = computed(() => {
    // 如果不是移动端，始终显示
    if (!isMobile.value) {
        return true;
    }

    // 如果是移动端，则根据输入框焦点状态决定是否显示
    return !inputFocused.value;
});

// 添加处理会话变更的方法
/**
 * 处理历史记录选择事件
 * @param {number} sessionId - 变更的会话ID
 */
async function handleHistorySelect() {
    // 重置聊天状态
    chatStore.resetState();

    // 重置展开状态
    expandedIframeIds.value = [];

    // 重新加载会话消息
    await handleGetFirstScreenMessages();
}

/**
 * 处理iframe展开事件
 * @param {string} messageId - 消息ID
 */
function handleIframeExpand(messageId) {
    // 避免重复添加
    if (!expandedIframeIds.value.includes(messageId)) {
        expandedIframeIds.value.push(messageId);
    }
}

/**
 * 处理iframe加载完成事件
 * @param {string} messageId - 消息ID
 * @param {boolean} loaded - 是否加载成功
 */
function handleIframeLoaded(messageId, loaded) {
    // 找到对应的消息
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
        // 设置iframe加载状态
        messages.value[messageIndex].iframeLoaded = loaded;
    }
}

/**
 * 判断iframe是否展开
 * @param {string} messageId - 消息ID
 * @returns {boolean} 是否展开
 */
function isIframeExpanded(messageId) {
    return expandedIframeIds.value.includes(messageId);
}

/**
 * 处理全屏状态变化
 * @param {string} messageId - 消息ID
 * @param {boolean} isFullscreen - 是否全屏
 */
function handleFullscreen(messageId, isFullscreen) {
    console.log(`消息 ${messageId} 全屏状态: ${isFullscreen}`);
    // 可以在这里添加其他全屏相关的逻辑，比如统计、埋点等
}
</script>

<style lang="scss" scoped>
.chat-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    position: relative;
    background-color: $system-grouped-background-primary;

    .chat-content-inner {
        display: flex;
        flex-direction: column;
        height: 100%;

        .chat-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 997;
            padding: 6px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: $system-grouped-background-primary;
            height: 48px;

            .new-session-btn,
            .history-button {
                padding: 6px 10px;
                border-radius: 16px;
                display: flex;
                align-items: center;
                font-size: 13px;
                font-weight: 600;
                color: $label-primary;
                background-color: $system-grouped-background-primary;
                transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
                will-change: transform;
                cursor: pointer;

                &:active {
                    transform: scale(0.95);
                    background-color: $system-grouped-background-tertiary;
                }

                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 4px;
                    filter: drop-shadow(0 1px 2px $fill-color-quaternary);
                }
            }

            .new-session-btn {
                color: $system-blue;
            }
        }

        .conversation {
            height: 100%;
            flex: 1;
            padding: 48px 16px 60px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            background-color: $system-grouped-background-primary;
            position: relative;
            display: flex;
            flex-direction: column;

            .loading-more {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 12px 0;
                opacity: 0;
                transition: opacity 0.3s ease;
                height: 0;
                overflow: hidden;

                &--visible {
                    opacity: 1;
                    height: auto;
                }

                img {
                    width: 24px;
                    height: 24px;
                    margin-right: 8px;
                    animation: spin 1.5s linear infinite;
                    filter: drop-shadow(0 1px 2px $fill-color-quaternary);
                }

                span {
                    font-size: 14px;
                    color: $label-secondary;
                }
            }

            .no-more-messages {
                text-align: center;
                padding: 16px;
                font-size: 14px;
                color: $system-gray;
                margin-bottom: 8px;
                background: $system-gray6;
                border-radius: 24px;
                width: fit-content;
                align-self: center;
                padding: 8px 16px;
            }

            .item {
                margin-bottom: 24px;
                width: 100%;

                &:last-child {
                    margin-bottom: 0;
                }

                .item-right {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    font-weight: 500;

                    .user-query {
                        align-self: flex-end;
                        max-width: 85%;
                        margin-bottom: 16px;

                        .content {
                            background: linear-gradient(135deg, $system-blue, $system-teal);
                            padding: 12px 16px;
                            border-radius: 18px 0 18px 18px;
                            font-size: 14px;
                            line-height: 1.5;
                            color: white;
                            word-break: break-word;
                            box-shadow: 0 4px 12px rgba($system-blue, 0.15);
                        }
                    }

                    .response {
                        align-self: flex-start;
                        max-width: 100%;

                        .model-response {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            position: relative;

                            .content {
                                max-width: 100%;
                                position: relative;
                                background-color: $system-background-primary;
                                padding: 14px 16px;
                                border-radius: 0 18px 18px 18px;
                                font-size: 14px;
                                line-height: 1.5;
                                color: $label-primary;
                                word-break: break-word;
                                box-shadow: 0 4px 16px $fill-color-quaternary;
                                border: 1px solid $fill-color-quaternary;
                            }

                            .tools-container {
                                display: flex;
                                justify-content: flex-start;
                                align-items: center;
                                width: 100%;
                                padding: 8px 0 0;
                                gap: 12px;
                            }

                            .message-tools {
                                display: flex;
                                align-items: center;
                                gap: 12px;

                                .tool-item {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    cursor: pointer;
                                    transition: all 0.2s ease;
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 16px;
                                    background-color: $system-background-primary;
                                    box-shadow: 0 2px 8px $fill-color-quaternary;

                                    &:active {
                                        transform: scale(0.92);
                                        box-shadow: 0 1px 4px $fill-color-quaternary;
                                    }

                                    img {
                                        width: 16px;
                                        height: 16px;
                                    }
                                }
                            }

                            /* 添加图片加载占位符样式 */
                            .img-placeholder {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 12px 16px;
                                margin: 12px 0;
                                background-color: rgba($system-grouped-background-tertiary, 0.5);
                                border-radius: 10px;
                                border: 1px dashed rgba($system-blue, 0.3);
                                animation: pulse 1.5s infinite ease-in-out;
                                gap: 10px;

                                img {
                                    width: 24px;
                                    height: 24px;
                                    margin: 0;
                                    animation: spin 1.5s linear infinite;
                                    filter: drop-shadow(0 2px 4px $fill-color-quaternary);
                                    box-shadow: none;
                                }

                                .placeholder-text {
                                    font-size: 14px;
                                    color: $label-secondary;
                                    font-weight: 500;
                                }
                            }

                            @keyframes pulse {
                                0% {
                                    background-color: rgba(
                                        $system-grouped-background-tertiary,
                                        0.3
                                    );
                                }
                                50% {
                                    background-color: rgba(
                                        $system-grouped-background-tertiary,
                                        0.5
                                    );
                                }
                                100% {
                                    background-color: rgba(
                                        $system-grouped-background-tertiary,
                                        0.3
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }

        .user-input {
            width: 100%;
            padding: 10px 10px 16px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);

            .knowledge-base-area {
                width: 100%;
                position: relative;
                transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
                display: flex;
                align-items: flex-start;
                max-height: 140px;
                opacity: 1;
                transform: translateY(0);
                z-index: 10;

                &--hidden {
                    max-height: 0;
                    opacity: 0;
                    margin: 0;
                    transform: translateY(-5px);
                    pointer-events: none;
                }
            }

            .user-input-chat {
                width: 100%;

                .main {
                    width: 100%;
                    .chat-input-container {
                        width: 100%;
                        padding: 12px 16px;
                        border: 1px solid $system-gray5;
                        border-radius: 16px;
                        display: flex;
                        flex-direction: column;
                        gap: 12px;
                        align-items: center;
                        justify-content: space-between;
                        background-color: $system-background-primary;
                        box-shadow: 0 4px 16px $fill-color-quaternary;
                        will-change: transform;

                        &.with-selected-bot {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-top: 1px solid transparent;
                        }

                        .input-area--focused {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-top: 1px solid transparent;
                            margin-top: -1px;
                        }

                        .input-content-row {
                            display: flex;
                            align-items: flex-end;
                            width: 100%;
                        }

                        .textarea-container {
                            flex: 1;
                        }

                        textarea {
                            display: block;
                            width: 100%;
                            color: $label-primary;
                            min-height: 28px;
                            height: 28px;
                            line-height: 28px;
                            max-height: 130px;
                            padding: 0;
                            border: none;
                            outline: none;
                            resize: none;
                            font-size: 14px;
                            background-color: transparent;

                            &::placeholder {
                                color: $label-tertiary;
                                font-size: 14px;
                            }
                        }

                        .input-bottom-area {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-end;
                            width: 100%;
                            padding-top: 4px;
                            margin-top: 4px;
                        }

                        .send-btn-container {
                            display: flex;
                            align-items: center;
                            margin-left: 10px;

                            .sending,
                            .stop-sending {
                                width: 28px;
                                height: 28px;
                                border-radius: 50%;
                                background: linear-gradient(135deg, $system-blue, $system-teal);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
                                cursor: pointer;
                                box-shadow: 0 4px 8px rgba(10, 122, 255, 0.2);
                                will-change: transform;

                                &:active {
                                    transform: scale(0.9);
                                    box-shadow: 0 2px 4px rgba(10, 122, 255, 0.15);
                                }

                                img {
                                    width: 16px;
                                    height: 16px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

.input-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($system-background-primary, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    padding: 16px 0;
    height: 200px;

    .loading-indicator {
        display: flex;
        align-items: center;
        gap: 10px;

        img {
            width: 28px;
            height: 28px;
            animation: spin 1.5s linear infinite;
            filter: drop-shadow(0 2px 4px $fill-color-quaternary);
        }
    }
}

.selected-knowledge-wrapper {
    display: none;
}

textarea:disabled {
    background-color: $system-background-secondary;
    cursor: not-allowed;
}

.reference-questions-container {
    margin-bottom: 30px;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 优化内嵌页面链接按钮样式 */
.inner-page-link {
    margin-top: 12px;
    width: 100%;
    display: flex;
    justify-content: center;

    .inner-page-button {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        background: linear-gradient(135deg, $system-blue, $system-teal);
        border-radius: 16px;
        box-shadow: 0 3px 10px rgba($system-blue, 0.25);
        transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        width: 100%;
        max-width: 220px;
        gap: 4px;
        cursor: pointer;

        &:active {
            transform: scale(0.96);
            box-shadow: 0 2px 6px rgba($system-blue, 0.2);
        }

        i {
            color: #fff;
            font-size: 12px;
        }

        span {
            font-size: 13px;
            font-weight: 500;
            color: #fff;
            letter-spacing: 0.5px;
        }
    }
}

// 增加一个全局的动画类，确保所有元素平滑过渡
.user-input-chat,
.textarea-container,
.chat-input-container,
.input-content-row,
.input-bottom-area {
    transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
    transform: translateZ(0);
}

/* 为 iframe 相关的内容添加样式 */
.model-response .content {
    /* 确保 iframe 在内容区域中正确显示 */
    overflow: visible !important;
}

.conversation .item .item-right .response .model-response {
    /* 增加一些外边距，以便 iframe 有更好的视觉效果 */
    margin-bottom: 12px;
}

/* 内联iframe容器的动画效果 */
.inline-iframe-container {
    animation: fadeInUp 0.4s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 包含iframe的消息项样式调整 */
.item--with-iframe {
    .item-right {
        .response {
            width: 100% !important;

            .model-response {
                .content {
                    padding: 0 !important;
                    width: 100% !important;
                    max-width: 100% !important;
                    border-radius: 0 !important;
                    border: none !important;
                    box-shadow: none !important;
                    background-color: transparent !important;
                }
            }
        }
    }

    /* 优化iframe容器样式 */
    .inline-iframe-container {
        margin: 0 !important;
        border-radius: 8px !important;
        overflow: hidden;
        border: none !important;
        background-color: $system-grouped-background-primary !important;
        box-shadow: none !important;
    }
}
</style>

<style lang="scss">
/* 自定义 GitHub Markdown CSS 样式 */
.markdown-body {
    .message-status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        text-align: center;
        margin: 8px 0;

        .status-img {
            width: 28px;
            height: 28px;
            box-shadow: none;
            animation: spin 1.5s linear infinite;
            filter: drop-shadow(0 2px 4px $fill-color-quaternary);
            margin: 0 !important;
        }

        .status-text {
            color: $label-secondary;
            font-size: 14px;
            font-weight: 500;
        }

        &.error .status-icon {
            color: $system-red;
            font-size: 18px;
        }

        &.stopped .status-icon {
            color: $system-orange;
            font-size: 18px;
        }
    }
    .message-status--placeholder {
        opacity: 0;
    }

    /* 空回答内容样式 */
    .empty-answer-content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 16px 20px;
        margin: 8px 0;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;

        &.default {
            background-color: rgba($system-blue, 0.08);
            color: $system-blue;
            border: 1px solid rgba($system-blue, 0.2);
        }

        &.error {
            background-color: rgba($system-red, 0.08);
            color: $system-red;
            border: 1px solid rgba($system-red, 0.2);
        }

        &.stopped {
            background-color: rgba($system-orange, 0.08);
            color: $system-orange;
            border: 1px solid rgba($system-orange, 0.2);
        }
    }
}

/* 自定义选中文本的样式 */
::selection {
    background-color: rgba($system-blue, 0.2) !important;
}

/* 为 iframe 相关的内容添加样式 */
.model-response .content {
    /* 确保 iframe 在内容区域中正确显示 */
    overflow: visible !important;
}

.conversation .item .item-right .response .model-response {
    /* 增加一些外边距，以便 iframe 有更好的视觉效果 */
    margin-bottom: 12px;
}
</style>
