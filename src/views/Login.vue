<template>
    <div class="login-overlay">
        <div class="login-content">
            <div class="loading-indicator"></div>
            <!-- <p class="loading-text">登录中...</p> -->
        </div>
    </div>
</template>
<script setup>
import { onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAppStore } from '@/stores/app';
import { isDingTalk } from '@/utils/index';
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useDingTalkBridge } from '@/services/dingTalkBridgeService';

const appStore = useAppStore();
const router = useRouter();
const route = useRoute();
const dingTalkBridge = useDingTalkBridge();

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '家家精灵'
    });
}

// 钉钉登录方法
const init = async () => {
    if (isDingTalk()) {
        try {
            // 使用封装的方法获取授权码
            const code = await dingTalkBridge.getAuthCode();

            const data = await appStore.login(code);

            // 登录成功后，跳转到刚才未登录的页面
            if (data.returncode === 0) {
                const redirectPath = route.query.redirect || '/';
                console.log('钉钉APP内🚗重定向', redirectPath);
                await router.replace(redirectPath);
            }
        } catch (err) {
            await router.replace({
                path: '/error',
                query: { errorMessage: '钉钉服务请求失败，请重试~' }
            });
        }
    } else {
        await nonDingTalkLogin();
    }
};

// 非钉钉环境登录流程
const nonDingTalkLogin = async () => {
    try {
        const ticketRes = await appStore.getTicketTest();

        if (ticketRes?.returncode !== 0 || !ticketRes?.result) {
            throw new Error('请在钉钉APP内操作~');
        }

        const loginRes = await appStore.ticketLogin(ticketRes.result);

        if (loginRes?.returncode === 0) {
            const redirectPath = route.query.redirect || '/';
            console.log('浏览器环境🚗重定向', redirectPath);
            await router.replace(redirectPath);
        } else {
            throw new Error('请在钉钉APP内操作~');
        }
    } catch (error) {
        await router.replace({
            path: '/error',
            query: { errorMessage: '请在钉钉APP内操作~' }
        });
    }
};

onMounted(() => {
    init();
});
</script>

<style scoped>
.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.login-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 200px;
}

.loading-indicator {
    width: 30px;
    height: 30px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    font-size: 14px;
    color: #666;
    margin: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
