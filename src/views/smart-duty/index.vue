<template>
    <div class="duty-list">
        <!-- 添加通用Header -->
        <AppHeader v-if="false" :showBack="true" :showExit="true" />

        <template v-if="!dutyLoading && !holidayPlan">
            <EmptyState @refresh="handleRefresh" />
        </template>
        <template v-else>
            <DateSelector
                class="date-selector"
                :holiday-plan="holidayPlan"
                @date-select="handleDateSelect"
                :loading="dateLoading"
            />
            <DutyGroups class="duty-groups" :holiday-plan="holidayPlan" :loading="dutyLoading" />
        </template>
    </div>
</template>

<script setup>
import DateSelector from './components/DateSelector.vue';
import DutyGroups from './components/DutyGroups.vue';
import { useDutyListStore } from '@/stores/dutyList';
import { onMounted, ref, onUnmounted } from 'vue';
import * as dd from 'dingtalk-jsapi';
import AppHeader from '@/components/common/AppHeader.vue';
import EmptyState from './components/EmptyState.vue';
import { isDingTalk } from '@/utils/index';

const dutyListStore = useDutyListStore();

const holidayPlan = ref(null);
const dateLoading = ref(false);
const dutyLoading = ref(false);

// 设置导航标题
const setNavTitle = () => {
    if (isDingTalk()) {
        const title = holidayPlan.value?.name || '暂无值班表';
        dd.biz.navigation.setTitle({
            title
        });
    }
};

// 验证日期是否在可选日期列表中
const isDateInSchedule = date => {
    return holidayPlan.value.scheduleDates.includes(date);
};

// 获取有效的目标日期
const getValidTargetDate = date => {
    if (date) {
        return date;
    }

    // 如果当前日期有效，使用当前日期
    if (isDateInSchedule(holidayPlan.value.currentScheduleDate)) {
        return holidayPlan.value.currentScheduleDate;
    }

    // 否则使用第一个可用日期
    return holidayPlan.value.scheduleDates[0];
};

// 更新当前选中日期
const updateCurrentScheduleDate = date => {
    if (holidayPlan.value && date !== holidayPlan.value.currentScheduleDate) {
        holidayPlan.value.currentScheduleDate = date;
    }
};

// 获取值班列表项
const fetchHolidayPlanItems = async date => {
    try {
        dutyLoading.value = true;

        // 获取有效的目标日期
        const targetDate = getValidTargetDate(date);

        // 更新当前选中日期
        updateCurrentScheduleDate(targetDate);

        // 获取值班列表数据
        const itemsRes = await dutyListStore.getHolidayPlanItems({
            planId: holidayPlan.value.id,
            scheduleDate: targetDate
        });
        holidayPlan.value.items = itemsRes.result;
    } catch (error) {
        console.error('获取值班列表失败:', error);
    } finally {
        dutyLoading.value = false;
    }
};

// 处理日期变更
const handleDateSelect = async newDate => {
    if (holidayPlan.value) {
        dateLoading.value = true;
        holidayPlan.value.currentScheduleDate = newDate;
        await fetchHolidayPlanItems(newDate);
        dateLoading.value = false;
    }
};

// 获取假期计划数据
const fetchHolidayPlan = async () => {
    try {
        const res = await dutyListStore.getHolidayPlan();
        if (res?.result) {
            holidayPlan.value = res.result;
            await fetchHolidayPlanItems();
        }
        setNavTitle();
    } catch (error) {
        console.error('获取假期计划失败:', error);
    }
};

// 初始化数据
const initData = async () => {
    try {
        dutyLoading.value = true;
        await fetchHolidayPlan();
    } catch (error) {
        console.error('初始化数据失败:', error);
    } finally {
        dutyLoading.value = false;
    }
};

// 新增：页面可见性变化监听
const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
        setNavTitle();
    }
};

// 添加刷新处理函数
const handleRefresh = async () => {
    await initData();
};

onMounted(() => {
    initData();
    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 新增：组件卸载时移除监听器
onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
});
</script>

<style lang="scss" scoped>
.duty-list {
    height: 100%;
    background-color: #f9fafb;
    display: flex;
    flex-direction: column;

    .duty-groups {
        overflow-y: auto;
        padding: 0.75rem 0.75rem;
    }
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }
}
</style>
