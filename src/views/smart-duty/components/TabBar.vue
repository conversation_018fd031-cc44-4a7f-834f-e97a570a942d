<template>
    <div class="tab-bar">
        <div
            v-for="(tab, index) in tabs"
            :key="index"
            class="tab-item"
            :class="{ active: selectedTab === index }"
            @click="handleTabClick(index)"
        >
            <i :class="['fas', tab.icon]"></i>
            <span class="tab-name">{{ tab.name }}</span>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const tabs = [
    { name: '首页', icon: 'fa-home' },
    { name: '日程', icon: 'fa-calendar' },
    { name: '消息', icon: 'fa-comment' },
    { name: '我的', icon: 'fa-user' }
];

const selectedTab = ref(0);
const emit = defineEmits(['tabChange']);

const handleTabClick = (index) => {
    selectedTab.value = index;
    emit('tabChange', index);
};
</script>

<style lang="scss" scoped>
.tab-bar {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 3.5rem;
    background-color: #fff;
    border-top: 1px solid #e5e7eb;
    display: grid;
    grid-template-columns: repeat(4, 1fr);

    .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #9ca3af;

        &.active {
            color: #2563eb;
        }

        .tab-name {
            margin-top: 0.25rem;
            font-size: 0.75rem;
        }
    }
}
</style>
