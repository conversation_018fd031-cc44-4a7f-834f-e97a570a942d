<template>
    <div class="date-selector">
        <div
            class="date-list"
            ref="scrollContainer"
            :class="{ 'loading-blur': loading }"
        >
            <ProgressSpinner
                v-if="loading"
                class="loading-spinner"
                strokeWidth="4"
                animationDuration=".5s"
                aria-label="加载中"
                style="width: 20px; height: 20px"
            />
            <div
                v-for="(date, index) in scheduleDates"
                :key="index"
                class="date-item"
                :class="{
                    active: selectedIndex === index,
                }"
                @click="handleDateSelect(index)"
                ref="dateItems"
            >
                <div class="date-content">
                    <div class="today-badge" v-if="isToday(date)">今</div>
                    <div class="weekday">
                        {{ getWeekDay(date) }}
                    </div>
                    <div class="date">
                        {{ formatDate(date) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue';

const props = defineProps({
    holidayPlan: {
        type: Object,
        default: () => ({
            scheduleDates: [],
            currentScheduleDate: ''
        })
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const selectedIndex = ref(0);
const scrollContainer = ref(null);
const dateItems = ref([]);
const emit = defineEmits(['dateSelect', 'date-change']);

const weekDayMap = {
    0: '周日',
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六'
};

const scheduleDates = computed(() => props.holidayPlan?.scheduleDates || []);

const getWeekDay = (dateStr) => {
    const date = new Date(dateStr);
    return weekDayMap[date.getDay()];
};

const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return `${String(date.getMonth() + 1).padStart(2, '0')}/${String(
        date.getDate()
    ).padStart(2, '0')}`;
};

const handleDateSelect = (index) => {
    selectedIndex.value = index;
    emit('dateSelect', scheduleDates.value[index]);
};

const scrollToCenter = async () => {
    await nextTick();
    if (scrollContainer.value && dateItems.value[selectedIndex.value]) {
        const item = dateItems.value[selectedIndex.value];
        const container = scrollContainer.value;
        const scrollLeft =
            item.offsetLeft - (container.clientWidth - item.offsetWidth) / 2;
        container.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
        });
    }
};

const initSelectedIndex = () => {
    if (
        !props.holidayPlan?.currentScheduleDate ||
        !props.holidayPlan?.scheduleDates?.length
    )
    {return;}

    const index = props.holidayPlan.scheduleDates.findIndex(
        (date) => date === props.holidayPlan.currentScheduleDate
    );

    selectedIndex.value = index !== -1 ? index : 0;
    nextTick(() => {
        scrollToCenter();
    });
};

const isToday = (dateStr) => {
    const today = new Date();
    const date = new Date(dateStr);
    return (
        date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear()
    );
};

watch(
    () => props.holidayPlan,
    () => {
        initSelectedIndex();
    },
    { deep: true }
);

onMounted(() => {
    initSelectedIndex();
});
</script>

<style lang="scss" scoped>
.date-selector {
    padding: 0.75rem 1rem;
    background-color: #fff;
    position: relative;
    overflow: hidden;

    .date-list {
        position: relative;
        display: flex;
        overflow-x: auto;
        will-change: transform;
        transform: translateZ(0);

        &.loading-blur {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        &::-webkit-scrollbar {
            display: none;
        }

        .date-item {
            flex: 0 0 auto;
            width: 4.5rem;
            margin: 0 0.5rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            background-color: #f3f4f6;
            color: #1f2937;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 0;
                background-color: #2563eb;
                transition: height 0.3s ease;
            }

            .date-content {
                text-align: center;
                position: relative;

                .today-badge {
                    position: absolute;
                    top: -11px;
                    right: -11px;
                    font-size: 0.75rem;
                    padding: 0 4px;
                    border-radius: 0.5rem;
                    background-color: #2563eb;
                    color: #f3f4f6;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
                    transform: scale(1);
                    border: 1px solid #fff;
                    font-weight: 600;

                    &:after {
                        content: '';
                        position: absolute;
                        inset: -1px;
                        background: linear-gradient(
                            45deg,
                            rgba(255, 255, 255, 0.1),
                            rgba(255, 255, 255, 0.3)
                        );
                        border-radius: inherit;
                        z-index: -1;
                    }
                }

                .weekday {
                    font-size: 0.875rem;
                    transition: color 0.3s ease;
                }

                .date {
                    margin-top: 0.25rem;
                    font-size: 1rem;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }
            }

            &.active {
                background-color: #2563eb;
                color: #fff;

                .date-content {
                    .today-badge {
                        background-color: #fff;
                        color: #2563eb;
                        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
                        border: 1px solid #2563eb;

                        &:after {
                            background: linear-gradient(
                                45deg,
                                rgba(37, 99, 235, 0.1),
                                rgba(37, 99, 235, 0.2)
                            );
                        }
                    }

                    .weekday {
                        color: rgba(255, 255, 255, 0.9);
                    }

                    .date {
                        color: #fff;
                        font-weight: 600;
                    }
                }
            }

            &:hover:not(.active) {
                background-color: #e5e7eb;
            }
        }
    }
}
</style>
