<template>
    <div class="empty-state">
        <div class="empty-content">
            <!-- 主图区域 -->
            <div class="image-wrapper">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/dutyList-notfound.png"
                    alt="暂无值班表"
                    class="empty-image"
                />
            </div>

            <!-- 文案区域 -->
            <div class="text-content">
                <h2 class="main-title">当前是正常工作时间</h2>
                <p class="sub-title">值班表将在周末/法定节假日期间显示</p>
                <div class="info-box">
                    <i class="pi pi-clock"></i>
                    <span>下一个值班时间段：{{ nextDutyTime }}</span>
                </div>
            </div>

            <!-- 刷新按钮 -->
            <Button
                class="refresh-btn"
                icon="pi pi-refresh"
                label="刷新状态"
                @click="$emit('refresh')"
                severity="secondary"
                text
            />
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
const nextDutyTime = ref('本周五 19:00 开始');
defineEmits(['refresh']);
</script>

<style lang="scss" scoped>
.empty-state {
    flex: 1;
    display: flex;
    padding-top: 100px;
    justify-content: center;
    background-color: #f9fafb;

    .empty-content {
        max-width: 480px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .image-wrapper {
        .empty-image {
            width: 240px;
            height: 240px;
            object-fit: contain;
            animation: float 6s ease-in-out infinite;
        }
    }

    .text-content {
        text-align: center;
        margin-bottom: 24px;

        .main-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .sub-title {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .info-box {
            display: inline-flex;
            align-items: center;
            padding: 12px 20px;
            background-color: #f3f4f6;
            border-radius: 8px;

            .pi {
                color: #9ca3af;
                margin-right: 8px;
                font-size: 16px;
            }

            span {
                color: #4b5563;
                font-size: 14px;
            }
        }
    }

    .refresh-btn {
        margin-top: 16px;

        &:hover {
            background-color: #f3f4f6;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}
</style>
