<template>
    <div class="duty-groups">
        <Toast />
        <ProgressSpinner
            v-if="loading"
            class="loading-spinner"
            strokeWidth="4"
            animationDuration=".5s"
            aria-label="加载中"
            style="width: 30px; height: 30px"
        />
        <div
            v-for="item in holidayPlan?.items"
            :key="item.id"
            class="duty-group"
            :class="{
                'loading-blur': loading,
                'is-expanded': expandedGroups.includes(item.id),
            }"
        >
            <div class="duty-header" @click="toggleGroup(item.id)">
                <div class="duty-name">
                    {{ item.name }}
                </div>
                <div class="duty-info">
                    <div
                        class="avatar-group"
                        v-if="hasValidEmployees(item.employees)"
                    >
                        <img
                            v-for="(employee, index) in previewEmployees(
                                item.employees
                            )"
                            :key="employee?.hrcode || index"
                            :src="getEmployeeAvatar(employee)"
                            :alt="employee?.name || '未知用户'"
                            class="avatar"
                            @error="handleAvatarError"
                        />
                        <div
                            class="avatar-more"
                            v-if="(item.employees || []).length > 3"
                        >
                            +{{ item.employees.length - 3 }}
                        </div>
                    </div>
                    <div class="empty-text" v-else>暂无值班人员</div>
                    <i
                        v-if="hasValidEmployees(item.employees)"
                        class="expand-icon pi"
                        :class="
                            expandedGroups.includes(item.id)
                                ? 'pi-chevron-up'
                                : 'pi-chevron-down'
                        "
                    ></i>
                </div>
            </div>
            <div v-show="expandedGroups.includes(item.id)" class="duty-content">
                <div class="employee-list-wrapper">
                    <div class="employee-list">
                        <div
                            v-for="employee in item.employees"
                            :key="employee?.hrcode || Math.random()"
                            class="employee-item"
                            :class="{
                                highlighted:
                                    highlightedEmployee === employee?.hrcode &&
                                    showChatTip,
                                dimmed:
                                    highlightedEmployee &&
                                    highlightedEmployee !== employee?.hrcode &&
                                    showChatTip,
                            }"
                            @click="handleEmployeeClick(employee)"
                        >
                            <img
                                :src="getEmployeeAvatar(employee)"
                                :alt="employee?.name || '未知用户'"
                                class="employee-avatar"
                                @error="handleAvatarError"
                            />
                            <div class="employee-info">
                                <div class="employee-name">
                                    {{ employee?.name || '未知用户' }}
                                    <span
                                        class="employee-hrcode"
                                        v-if="employee?.hrcode"
                                    >
                                        ({{ employee.hrcode }})
                                    </span>
                                </div>
                                <div
                                    v-if="employee?.remark"
                                    class="employee-remark"
                                >
                                    - {{ employee.remark }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <Teleport to="body">
            <div v-if="showChatTip" class="modal-overlay">
                <div class="modal-content">
                    <Message severity="info">
                        <div class="chat-tip-content">
                            <span>点击用户头像可直接聊天</span>
                        </div>
                    </Message>
                    <button class="confirm-button" @click="handleCloseTip">
                        <img
                            src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/iknow.png"
                            alt=""
                        />
                        我知道啦
                    </button>
                </div>
            </div>
        </Teleport>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { useToast } from 'primevue/usetoast';
import ProgressSpinner from 'primevue/progressspinner';
import Message from 'primevue/message';

const DEFAULT_AVATAR =
    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png'; // 替换为你的默认头像地址

const toast = useToast();
const props = defineProps({
    holidayPlan: {
        type: Object,
        default: () => ({
            items: []
        })
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const expandedGroups = ref([]);

// 添加提示框控制变量
const showChatTip = ref(false);

const CHAT_TIP_KEY = 'duty_chat_tip_shown';
const CHAT_TIP_EXPIRY_DAYS = 7;

// 添加高亮员工的状态控制
const highlightedEmployee = ref(null);

// 检查提示是否已显示过
const checkIfTipShown = () => {
    const tipData = localStorage.getItem(CHAT_TIP_KEY);
    if (!tipData) {return false;}

    try {
        const { timestamp } = JSON.parse(tipData);
        const now = new Date().getTime();
        const expiryTime =
            timestamp + CHAT_TIP_EXPIRY_DAYS * 24 * 60 * 60 * 1000;

        return now < expiryTime;
    } catch (e) {
        return false;
    }
};

// 记录提示已显示
const markTipAsShown = () => {
    const tipData = {
        timestamp: new Date().getTime()
    };
    localStorage.setItem(CHAT_TIP_KEY, JSON.stringify(tipData));
};

// 检查是否有有效的员工数据
const hasValidEmployees = (employees) => {
    return (
        Array.isArray(employees) &&
        employees.length > 0 &&
        employees.some((emp) => emp && emp.hrcode)
    );
};

// 获取员工头像，带默认值
const getEmployeeAvatar = (employee) => {
    return employee?.avatar || DEFAULT_AVATAR;
};

// 处理头像加载失败
const handleAvatarError = (event) => {
    event.target.src = DEFAULT_AVATAR;
};

const toggleGroup = (groupId) => {
    const group = props.holidayPlan?.items?.find(
        (item) => item?.id === groupId
    );
    if (!hasValidEmployees(group?.employees)) {
        return;
    }

    const index = expandedGroups.value.indexOf(groupId);
    if (index === -1) {
        expandedGroups.value.push(groupId);
        // 只有当提示未显示过时才显示提示并高亮第一个员工
        if (!checkIfTipShown()) {
            showChatTip.value = true;
            // 设置第一个有效员工为高亮
            const firstValidEmployee = group.employees.find(
                (emp) => emp && emp.hrcode
            );
            if (firstValidEmployee) {
                highlightedEmployee.value = firstValidEmployee.hrcode;
            }
        }
    } else {
        expandedGroups.value.splice(index, 1);
    }
};

// 预览员工列表，确保返回有效数据
const previewEmployees = (employees) => {
    if (!Array.isArray(employees)) {return [];}
    return employees.slice(0, 3).filter(Boolean);
};

// 处理员工点击事件
const handleEmployeeClick = (employee) => {
    if (employee?.dingLink) {
        // 如果提示框正在显示，先处理关闭逻辑
        if (showChatTip.value) {
            handleCloseTip();
        }
        window.location.href = employee.dingLink;
    }
};

// 修改关闭提示的处理方法
const handleCloseTip = () => {
    showChatTip.value = false;
    highlightedEmployee.value = null; // 清除高亮状态
    markTipAsShown(); // 记录提示已显示
};
</script>

<style lang="scss" scoped>
.duty-groups {
    position: relative;
    flex: 1;
    padding: 1rem;
    background: linear-gradient(to bottom, #f8faff, #fff);

    .loading-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
    }

    .loading-blur {
        opacity: 0.6;
        pointer-events: none;
        transition: opacity 0.3s ease;
    }

    .duty-group {
        background: #ffffff;
        border-radius: 0.75rem;
        margin-bottom: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(231, 238, 255, 0.8);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;

        &.is-expanded {
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
        }

        .duty-header {
            padding: 1rem 1.25rem;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .duty-name {
                font-size: 1.125rem;
                font-weight: 600;
                color: #333;
                position: relative;
                flex: 1;
            }

            .duty-info {
                margin-left: 1rem;
                display: flex;
                align-items: center;
                gap: 1.3rem;

                .avatar-group {
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;

                    .avatar {
                        width: 1.75rem;
                        height: 1.75rem;
                        border-radius: 50%;
                        border: 2px solid #fff;
                        margin-left: -0.5rem;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        transition: transform 0.2s ease;

                        &:first-child {
                            margin-left: 0;
                        }
                    }

                    .avatar-more {
                        background: #e0f2fe;
                        color: #0369a1;
                        padding: 0.125rem 0.375rem;
                        border-radius: 1rem;
                        font-size: 0.75rem;
                        font-weight: 500;
                    }
                }

                .expand-icon {
                    font-size: 10px;
                    color: #64748b;
                    transition: transform 0.3s ease;
                    cursor: pointer;
                }

                .empty-text {
                    color: #64748b;
                    font-size: 12px;
                }
            }
        }

        .duty-content {
            .employee-list {
                position: relative;
                padding: 0.75rem 1.25rem;
                padding-top: 0;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 0.75rem;

                .employee-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    cursor: pointer;

                    &:hover {
                        border-radius: 10px;
                        background: linear-gradient(to right, #f0f9ff, #fff);
                        border-color: #93c5fd;
                    }

                    .employee-avatar {
                        width: 2.5rem;
                        height: 2.5rem;
                        border-radius: 50%;
                        border: 2px solid #fff;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                        flex-shrink: 0;
                    }

                    .employee-info {
                        flex: 1;
                        min-width: 0;
                        display: flex;
                        align-items: center;
                        gap: 0.2rem;

                        .employee-name {
                            font-size: 0.875rem;
                            color: #333;
                            white-space: nowrap;
                            font-weight: bold;

                            .employee-hrcode {
                                font-size: 0.75rem;
                                color: #64748b;
                                font-weight: normal;
                                margin-left: 0.25rem;
                            }
                        }

                        .employee-remark {
                            font-weight: bold;
                            color: #333;
                            font-size: 0.75rem;
                        }
                    }

                    // 高亮状态
                    &.highlighted {
                        position: relative;
                        z-index: 1001;
                        transform: translateY(-2px);
                        background: #fff;
                        border-radius: 10px;
                        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.15);
                        opacity: 1 !important;

                        // 添加呼吸灯效果
                        animation: breathe 2s infinite;
                    }

                    // 置灰状态
                    &.dimmed {
                        opacity: 0.3;
                        filter: grayscale(0.8);
                        transform: scale(0.98);
                    }
                }
            }
        }
    }
}

@keyframes expandLine {
    0% {
        width: 2rem;
        opacity: 0.5;
    }
    100% {
        width: 3rem;
        opacity: 1;
    }
}

// 优化加载动画
.loading-spinner {
    :deep(.p-progress-spinner-circle) {
        stroke: #fb923c !important;
        transition: stroke 0.3s ease;
    }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.35);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

:deep(.p-message) {
    position: relative;
    margin: 0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(99, 102, 241, 0.1);
    background-color: #f8fafc;
    max-width: 90%;
    width: auto;
    animation: slideIn 0.3s ease;

    .p-message-wrapper {
        padding: 1rem 1.5rem;
    }

    .p-message-icon {
        font-size: 1.25rem;
        color: #6366f1;
    }

    .p-message-close {
        display: none;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    animation: slideIn 0.3s ease;
}

.confirm-button {
    background: #6366f1;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);

    img {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.5rem;
    }
}

// 优化动画效果
@keyframes slideIn {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.chat-tip-content {
    font-size: 1rem;
    color: #1f2937;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
}

// 添加呼吸灯动画
@keyframes breathe {
    0% {
        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.15);
    }
    50% {
        box-shadow: 0 8px 28px rgba(99, 102, 241, 0.25);
    }
    100% {
        box-shadow: 0 8px 24px rgba(99, 102, 241, 0.15);
    }
}
</style>
