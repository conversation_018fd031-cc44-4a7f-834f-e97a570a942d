<template>
    <div class="iframe-container">
        <Loading :config="loadingConfig" />
        <iframe
            v-if="iframeUrl"
            :src="iframeUrl"
            width="100%"
            height="100%"
            allow="true"
            frameborder="0"
            @load="handleIframeLoad"
        ></iframe>
    </div>
</template>

<script setup>
import { useAppStore } from '@/stores/app';
import { ref, onMounted, onUnmounted } from 'vue';
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useRouter } from 'vue-router';
import Loading from '@/components/Loading.vue';
import { getBusinessUrl, BUSINESS_TYPES } from '@/utils/urls';

const appStore = useAppStore();
const router = useRouter();

const onMessage = (e) => {
    const { eventName } = e.data;
    if (eventName === 'login') {
        router.replace({ path: '/login', query: { redirect: '/smart-data' } });
    }
};

const iframeUrl = ref('');

const loadingConfig = ref({
    visible: true,
    status: 'loading',
    message: '加载中...'
});

const updateSendDialogConfig = (status, message, visible = true) => {
    loadingConfig.value.status = status;
    loadingConfig.value.message = message;
    loadingConfig.value.visible = visible;
};

const handleIframeLoad = () => {
    updateSendDialogConfig('loaded', '加载完成', false);
};

const init = async () => {
    const data = await appStore.getTicket();
    if (data?.returncode === 0 && data?.result) {
        iframeUrl.value = getBusinessUrl(BUSINESS_TYPES.SMART_DATA, {
            ticket: data.result
        });
    } else {
        updateSendDialogConfig('failed', '获取签名失败，请稍后再试', false);
    }
};

onMounted(() => {
    const title = import.meta.env.VITE_SMART_DATA_TITLE || '智能问数';
    dd.biz.navigation.setTitle({
        title: title
    });
    updateSendDialogConfig('loading', '加载中...');
    init();
    window.addEventListener('message', onMessage, false);
});

onUnmounted(() => {
    window.removeEventListener('message', onMessage, false);
});
</script>

<style lang="scss" scoped>
.iframe-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
