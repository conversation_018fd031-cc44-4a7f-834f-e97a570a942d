<template>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">动态表单组件演示</h1>
            <p class="demo-description">
                根据 JSON
                配置动态生成表单，支持文本、内联选择器、侧边栏选择和模态框选择等多种组件类型
            </p>
        </div>

        <div class="demo-content">
            <!-- 动态表单组件 -->
            <div class="form-wrapper">
                <h2 class="section-title">表单示例</h2>

                <DynamicForm
                    ref="dynamicFormRef"
                    :config="formConfig"
                    :show-actions="true"
                    submit-text="查询"
                    :auto-submit="false"
                    @submit="handleFormSubmit"
                    @reset="handleFormReset"
                    @value-change="handleValueChange"
                    @action-triggered="handleActionTriggered"
                />
            </div>

            <!-- 表单结果展示 -->
            <div class="result-wrapper">
                <h2 class="section-title">表单数据</h2>
                <div class="result-content">
                    <div class="result-item">
                        <label class="result-label">当前值:</label>
                        <pre class="result-value">{{ JSON.stringify(currentValues, null, 2) }}</pre>
                    </div>

                    <div v-if="lastSubmitResult" class="result-item">
                        <label class="result-label">提交结果:</label>
                        <pre class="result-value">{{
                            JSON.stringify(lastSubmitResult, null, 2)
                        }}</pre>
                    </div>

                    <div v-if="formattedText" class="result-item">
                        <label class="result-label">格式化文本:</label>
                        <div class="formatted-text">{{ formattedText }}</div>
                    </div>

                    <div class="result-item">
                        <label class="result-label">生成的提示词:</label>
                        <div class="prompt-wrapper">
                            <div
                                class="prompt-text"
                                :class="{ 'prompt-text--empty': !generatedPrompt }"
                            >
                                {{ generatedPrompt || '暂无提示词内容' }}
                            </div>
                            <button
                                class="generate-btn"
                                @click="updateGeneratedPrompt"
                                :disabled="!dynamicFormRef"
                            >
                                生成提示词
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JSON 配置展示 -->
            <div class="config-wrapper">
                <h2 class="section-title">JSON 配置</h2>
                <div class="config-editor">
                    <button class="toggle-btn" @click="showConfig = !showConfig">
                        {{ showConfig ? '隐藏配置' : '显示配置' }}
                    </button>

                    <div v-if="showConfig" class="config-content">
                        <pre class="config-json">{{ JSON.stringify(formConfig, null, 2) }}</pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="log-wrapper">
            <h2 class="section-title">操作日志</h2>
            <div class="log-content">
                <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
                    <span class="log-time">{{ log.time }}</span>
                    <span class="log-type" :class="`log-type--${log.type}`">{{ log.type }}</span>
                    <span class="log-message">{{ log.message }}</span>
                </div>

                <div v-if="actionLogs.length === 0" class="log-empty">暂无操作记录</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import DynamicForm from '@/components/DynamicForm/index.vue';
import testMockData from '@/views/smart-chat/json/test-mock.json';

// 响应式状态
const dynamicFormRef = ref(null);
const formConfig = ref([]);
const currentValues = ref({});
const lastSubmitResult = ref(null);
const formattedText = ref('');
const generatedPrompt = ref('');
const showConfig = ref(false);
const actionLogs = ref([]);

/**
 * 添加操作日志
 * @param {string} type - 日志类型
 * @param {string} message - 日志消息
 */
const addLog = (type, message) => {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    actionLogs.value.unshift({
        time,
        type,
        message
    });

    // 保持最多50条日志
    if (actionLogs.value.length > 50) {
        actionLogs.value = actionLogs.value.slice(0, 50);
    }
};

/**
 * 处理表单提交
 * @param {Object} data - 提交的数据
 */
const handleFormSubmit = data => {
    lastSubmitResult.value = data;
    formattedText.value = data.formattedText;

    addLog('submit', `表单提交完成，包含 ${Object.keys(data.values).length} 个字段`);

    console.log('表单提交:', data);
};

/**
 * 处理表单重置
 */
const handleFormReset = () => {
    currentValues.value = {};
    lastSubmitResult.value = null;
    formattedText.value = '';
    generatedPrompt.value = '';

    addLog('reset', '表单已重置');

    console.log('表单重置');
};

/**
 * 处理值变化
 * @param {Object} data - 变化的数据
 */
const handleValueChange = data => {
    currentValues.value = { ...data.allValues };

    // 自动更新生成的提示词
    updateGeneratedPrompt();

    addLog('change', `字段 "${data.id}" 的值已更新: ${JSON.stringify(data.value)}`);

    console.log('值变化:', data);
};

/**
 * 更新生成的提示词
 */
const updateGeneratedPrompt = () => {
    if (dynamicFormRef.value && dynamicFormRef.value.generatePrompt) {
        const prompt = dynamicFormRef.value.generatePrompt();
        generatedPrompt.value = prompt;

        addLog(
            'prompt',
            `提示词已生成: "${prompt.length > 50 ? prompt.substring(0, 50) + '...' : prompt}"`
        );

        console.log('生成的提示词:', prompt);
    }
};

/**
 * 处理动作触发
 * @param {Object} data - 动作数据
 */
const handleActionTriggered = data => {
    const actionType = data.action.type;
    const contextId = data.context.id;

    addLog('action', `触发 ${actionType} 动作，字段: ${contextId}`);

    console.log('动作触发:', data);
};

/**
 * 初始化表单配置
 */
const initializeForm = () => {
    // 使用从 JSON 文件导入的数据
    formConfig.value = testMockData;

    addLog('init', '表单配置已加载');

    // 延迟更新提示词，确保组件已经挂载
    nextTick(() => {
        updateGeneratedPrompt();
    });
};

// 生命周期钩子
onMounted(() => {
    initializeForm();
});
</script>

<style lang="scss" scoped>
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: $system-grouped-background-primary;
    height: 100%;
    overflow-y: scroll;

    .demo-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 32px 0;
        background: linear-gradient(135deg, rgba($system-blue, 0.1), rgba($system-teal, 0.1));
        border-radius: 16px;

        .demo-title {
            font-size: 28px;
            font-weight: 700;
            color: $label-primary;
            margin: 0 0 12px 0;
        }

        .demo-description {
            font-size: 16px;
            color: $label-secondary;
            margin: 0;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    .demo-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 32px;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }

        .form-wrapper,
        .result-wrapper,
        .config-wrapper {
            background-color: $system-background-primary;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px $fill-color-quaternary;
            border: 1px solid $fill-color-quaternary;
        }

        .config-wrapper {
            grid-column: 1 / -1;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: $label-primary;
            margin: 0 0 20px 0;
            padding-bottom: 12px;
            border-bottom: 2px solid $fill-color-quaternary;
        }
    }

    .result-content {
        .result-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .result-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: $label-primary;
                margin-bottom: 8px;
            }

            .result-value {
                background-color: $system-grouped-background-secondary;
                border: 1px solid $fill-color-tertiary;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 12px;
                color: $label-primary;
                overflow-x: auto;
                white-space: pre;
                margin: 0;
                line-height: 1.4;
            }

            .formatted-text {
                background-color: rgba($system-green, 0.1);
                border: 1px solid rgba($system-green, 0.2);
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                color: $system-green;
                font-weight: 500;
                line-height: 1.5;
            }

            .prompt-wrapper {
                display: flex;
                gap: 12px;
                align-items: flex-start;

                .prompt-text {
                    flex: 1;
                    background-color: rgba($system-teal, 0.1);
                    border: 1px solid rgba($system-teal, 0.2);
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 14px;
                    color: $system-teal;
                    font-weight: 500;
                    line-height: 1.5;
                    min-height: 45px;
                    display: flex;
                    align-items: center;

                    &--empty {
                        color: $label-tertiary;
                        font-style: italic;
                        background-color: $system-grouped-background-secondary;
                        border-color: $fill-color-tertiary;
                    }
                }

                .generate-btn {
                    padding: 10px 16px;
                    border: 1px solid $system-teal;
                    border-radius: 8px;
                    background-color: rgba($system-teal, 0.1);
                    color: $system-teal;
                    font-size: 13px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    white-space: nowrap;
                    min-height: 45px;

                    &:hover:not(:disabled) {
                        background-color: rgba($system-teal, 0.2);
                        transform: translateY(-1px);
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }

    .config-editor {
        .toggle-btn {
            padding: 8px 16px;
            border: 1px solid $system-blue;
            border-radius: 8px;
            background-color: rgba($system-blue, 0.1);
            color: $system-blue;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 16px;

            &:hover {
                background-color: rgba($system-blue, 0.2);
            }
        }

        .config-content {
            .config-json {
                background-color: $system-grouped-background-secondary;
                border: 1px solid $fill-color-tertiary;
                border-radius: 8px;
                padding: 16px;
                font-family: 'Monaco', 'Consolas', monospace;
                font-size: 12px;
                color: $label-primary;
                overflow-x: auto;
                white-space: pre;
                margin: 0;
                line-height: 1.4;
                max-height: 400px;
                overflow-y: auto;
            }
        }
    }

    .log-wrapper {
        background-color: $system-background-primary;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 16px $fill-color-quaternary;
        border: 1px solid $fill-color-quaternary;

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: $label-primary;
            margin: 0 0 20px 0;
            padding-bottom: 12px;
            border-bottom: 2px solid $fill-color-quaternary;
        }

        .log-content {
            max-height: 300px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;

            .log-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px 12px;
                border-radius: 6px;
                margin-bottom: 4px;
                font-size: 13px;
                transition: background-color 0.2s ease;

                &:hover {
                    background-color: $system-grouped-background-secondary;
                }

                .log-time {
                    font-family: 'Monaco', 'Consolas', monospace;
                    color: $label-tertiary;
                    min-width: 60px;
                    font-size: 11px;
                }

                .log-type {
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 600;
                    text-transform: uppercase;
                    min-width: 60px;
                    text-align: center;

                    &--init {
                        background-color: rgba($system-blue, 0.2);
                        color: $system-blue;
                    }

                    &--change {
                        background-color: rgba($system-orange, 0.2);
                        color: $system-orange;
                    }

                    &--action {
                        background-color: rgba($system-purple, 0.2);
                        color: $system-purple;
                    }

                    &--submit {
                        background-color: rgba($system-green, 0.2);
                        color: $system-green;
                    }

                    &--reset {
                        background-color: rgba($system-red, 0.2);
                        color: $system-red;
                    }

                    &--prompt {
                        background-color: rgba($system-teal, 0.2);
                        color: $system-teal;
                    }
                }

                .log-message {
                    color: $label-primary;
                    line-height: 1.4;
                }
            }

            .log-empty {
                text-align: center;
                color: $label-tertiary;
                font-style: italic;
                padding: 40px 0;
            }
        }
    }
}
</style>
