Mermaid 流程图 生成工具


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Sequence Diagram</title>
    <script src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/cdnjs/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</head>

<body>
    <pre class="mermaid">
sequenceDiagram
    participant Iframe as iframe页面
    participant Host as 宿主页面
    Iframe->>Host: 发送API请求(action+data)
    Host->>钉钉: 调用JSAPI
    钉钉-->>Host: API响应
    Host->>Iframe: 返回结果(success/data 或 error)
    </pre>
</body>

</html> 