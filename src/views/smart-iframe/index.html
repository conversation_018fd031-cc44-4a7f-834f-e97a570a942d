<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能打卡</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f8f8;
            padding: 16px;
        }

        .card-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border-radius: 12px;
            padding: 18px 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            height: 130px;
            cursor: pointer;
        }

        .card-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #007aff;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 6px;
            color: #333;
        }

        .card-desc {
            font-size: 14px;
            color: #666;
        }

        .records-container,
        .photos-container {
            background-color: #fff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-top: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            margin: 0 0 12px;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 8px;
        }

        .record-item {
            display: flex;
            flex-direction: column;
            padding: 16px;
            border-radius: 10px;
            background-color: #f9f9f9;
            margin-bottom: 12px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .record-detail {
            display: flex;
            flex-direction: column;
            width: 100%;
            gap: 10px;
        }

        .record-location {
            display: flex;
            align-items: flex-start;
            padding: 8px 10px;
            font-size: 14px;
            color: #555;
            line-height: 1.4;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }

        .photos-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 10px;
        }

        .photo-item {
            display: flex;
            flex-direction: column;
            background-color: #f9f9f9;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .photo-image {
            width: 100%;
            aspect-ratio: 1;
            object-fit: cover;
        }

        .photo-time {
            width: 100%;
            font-size: 12px;
            color: #666;
            text-align: center;
            background-color: #fff;
            padding: 8px 5px;
            word-break: break-all;
            white-space: normal;
            line-height: 1.4;
            height: auto;
            max-height: 80px;
            overflow-y: auto;
        }

        .status-bar {
            padding: 10px;
            margin-bottom: 15px;
            background-color: #f5f5f5;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
        }

        .coordinate-item {
            display: flex;
            margin-bottom: 4px;
        }

        .coordinate-label {
            font-weight: 500;
            min-width: 45px;
            color: #666;
            margin-right: 5px;
        }

        /* 录音相关样式 */
        .record-card {
            background-color: #e6f7ff;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: visibility 0.3s, opacity 0.3s;
        }

        .modal-overlay.active {
            visibility: visible;
            opacity: 1;
        }

        .modal-content {
            width: 100%;
            background-color: #fff;
            border-radius: 16px 16px 0 0;
            padding: 20px 16px;
            transform: translateY(100%);
            transition: transform 0.3s;
        }

        .modal-overlay.active .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }

        .modal-close {
            font-size: 20px;
            color: #999;
            background: none;
            border: none;
            cursor: pointer;
        }

        .recording-status {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }

        .recording-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #f5222d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }

            50% {
                transform: scale(1.1);
                opacity: 0.8;
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .recording-time {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
        }

        .recording-tip {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .stop-button {
            width: 100%;
            padding: 12px;
            background-color: #f5222d;
            color: #fff;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 10px;
        }

        .voice-text-container {
            margin-top: 16px;
            padding: 12px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #eee;
        }

        .voice-text-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .voice-text-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .voice-records-container {
            background-color: #fff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            margin-top: 16px;
        }

        .voice-record-item {
            padding: 12px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-bottom: 12px;
        }

        .voice-record-time {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .voice-record-text {
            font-size: 16px;
            color: #333;
            line-height: 1.5;
        }

        /* 调试信息区域 */
        .debug-panel {
            margin-top: 16px;
            background-color: #f8f8f8;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: none;
        }

        .debug-panel.active {
            display: block;
        }

        .debug-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .debug-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .debug-toggle {
            padding: 4px 8px;
            background-color: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .debug-content {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            color: #333;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .debug-clear {
            margin-top: 8px;
            padding: 4px 8px;
            background-color: #ff4d4f;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="status-bar" id="statusBar">
        准备就绪，等待操作...
    </div>

    <div class="card-container">
        <!-- 打卡卡片 -->
        <div class="card" id="clockInBtn">
            <div class="card-icon">⏱️</div>
            <span class="card-title">立即打卡</span>
            <span class="card-desc" id="currentTime">00:00:00</span>
        </div>

        <!-- 拍照卡片 -->
        <div class="card" id="takePhotoBtn">
            <div class="card-icon">📷</div>
            <span class="card-title">拍照上传</span>
            <span class="card-desc">记录工作场景</span>
        </div>

        <!-- 录音卡片 -->
        <div class="card record-card" id="recordVoiceBtn">
            <div class="card-icon">🎙️</div>
            <span class="card-title">语音记录</span>
            <span class="card-desc">记录语音备注</span>
        </div>
    </div>

    <!-- 打卡记录展示 -->
    <div class="records-container" id="recordsContainer" style="display: none;">
        <h2 class="section-title">打卡记录</h2>
        <div id="recordsList"></div>
    </div>

    <!-- 照片展示区域 -->
    <div class="photos-container" id="photosContainer" style="display: none;">
        <h2 class="section-title">照片记录</h2>
        <div class="photos-grid" id="photosGrid"></div>
    </div>

    <!-- 语音记录展示 -->
    <div class="voice-records-container" id="voiceRecordsContainer" style="display: none;">
        <h2 class="section-title">语音记录</h2>
        <div id="voiceRecordsList"></div>
    </div>

    <!-- 录音弹窗 -->
    <div class="modal-overlay" id="recordingModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">正在录音</div>
                <button class="modal-close" id="closeRecordingModal">×</button>
            </div>
            <div class="recording-status">
                <div class="recording-icon">🎙️</div>
                <div class="recording-time" id="recordingTime">00:00</div>
                <div class="recording-tip">正在录音，请对着手机说话</div>
            </div>
            <button class="stop-button" id="stopRecordingBtn">停止录音</button>
        </div>
    </div>

    <!-- 调试信息区域 -->
    <div class="debug-panel" id="debugPanel">
        <div class="debug-header">
            <div class="debug-title">调试信息</div>
            <button class="debug-toggle" id="debugToggle">关闭</button>
        </div>
        <div class="debug-content" id="debugContent"></div>
        <button class="debug-clear" id="debugClear">清除日志</button>
    </div>

    <script>
        // 存储数据
        let clockRecords = [];
        let photos = [];
        let voiceRecords = []; // 存储语音记录
        let requestMap = new Map(); // 用于存储请求回调
        let isRecording = false; // 是否正在录音
        let recordingTimer = null; // 录音计时器
        let recordingStartTime = 0; // 录音开始时间
        let debugLogs = []; // 调试日志

        // 添加调试日志
        function addDebugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                message: typeof message === 'object' ? JSON.stringify(message) : message,
                type
            };

            debugLogs.push(logEntry);

            // 限制日志数量
            if (debugLogs.length > 100) {
                debugLogs.shift();
            }

            // 更新调试面板
            updateDebugPanel();

            // 同时输出到控制台
            if (type === 'error') {
                console.error(`[${timestamp}] ${logEntry.message}`);
            } else {
                console.log(`[${timestamp}] ${logEntry.message}`);
            }
        }

        // 更新调试面板
        function updateDebugPanel() {
            const debugContent = document.getElementById('debugContent');
            if (!debugContent) return;

            debugContent.innerHTML = debugLogs.map(log => {
                const color = log.type === 'error' ? 'red' :
                    log.type === 'success' ? 'green' :
                        log.type === 'warning' ? 'orange' : 'black';
                return `<div style="color: ${color};">[${log.timestamp}] ${log.message}</div>`;
            }).join('');

            // 自动滚动到底部
            debugContent.scrollTop = debugContent.scrollHeight;
        }

        // 清除调试日志
        function clearDebugLogs() {
            debugLogs = [];
            updateDebugPanel();
        }

        // 切换调试面板显示
        function toggleDebugPanel() {
            const panel = document.getElementById('debugPanel');
            const toggle = document.getElementById('debugToggle');

            if (panel.classList.contains('active')) {
                panel.classList.remove('active');
                toggle.textContent = '显示';
                localStorage.setItem('debugPanelVisible', 'false');
            } else {
                panel.classList.add('active');
                toggle.textContent = '关闭';
                localStorage.setItem('debugPanelVisible', 'true');
                updateDebugPanel();
            }
        }

        // 更新时间
        function updateTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            document.getElementById('currentTime').textContent = `${hours}:${minutes}:${seconds}`;
        }

        // 生成请求ID
        function generateRequestId() {
            return Date.now().toString() + Math.floor(Math.random() * 1000);
        }

        // 向宿主页面发送请求
        function sendRequest(action, data = {}) {
            return new Promise((resolve, reject) => {
                const requestId = generateRequestId();

                // 存储请求回调
                requestMap.set(requestId, { resolve, reject });

                // 构建消息对象
                const message = {
                    source: 'iframe-app',
                    action,
                    data,
                    requestId,
                    timestamp: Date.now()
                };

                // 添加调试日志
                addDebugLog(`发送请求: ${action}, 数据: ${JSON.stringify(data)}`);

                // 更新状态
                updateStatus(`正在请求: ${action}...`);

                // 发送消息给宿主页面
                window.parent.postMessage(message, '*');
            });
        }

        // 更新状态栏
        function updateStatus(message, type = 'info') {
            const statusBar = document.getElementById('statusBar');
            statusBar.textContent = message;

            // 根据类型设置样式
            statusBar.style.backgroundColor = type === 'error' ? '#fff2f0' :
                type === 'success' ? '#f6ffed' : '#f5f5f5';
            statusBar.style.color = type === 'error' ? '#ff4d4f' :
                type === 'success' ? '#52c41a' : '#666';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(parseInt(timestamp));

            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');

            return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
        }

        // 格式化录音时长
        function formatRecordingTime(milliseconds) {
            const totalSeconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(totalSeconds / 60).toString().padStart(2, '0');
            const seconds = (totalSeconds % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        }

        // 渲染打卡记录
        function renderClockRecords() {
            const container = document.getElementById('recordsContainer');
            const list = document.getElementById('recordsList');

            if (clockRecords.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            list.innerHTML = '';

            clockRecords.forEach(record => {
                const recordItem = document.createElement('div');
                recordItem.className = 'record-item';

                const recordHeader = document.createElement('div');
                recordHeader.className = 'record-header';

                const recordTime = document.createElement('span');
                recordTime.className = 'record-time';
                recordTime.textContent = formatTime(record.timestamp);

                const recordType = document.createElement('span');
                recordType.style.color = '#fff';
                recordType.style.backgroundColor = '#34c759';
                recordType.style.padding = '4px 10px';
                recordType.style.borderRadius = '15px';
                recordType.style.fontWeight = '500';
                recordType.style.fontSize = '13px';
                recordType.textContent = '已打卡';

                recordHeader.appendChild(recordTime);
                recordHeader.appendChild(recordType);

                const recordDetail = document.createElement('div');
                recordDetail.className = 'record-detail';

                if (record.location && record.location.address) {
                    const locationDiv = document.createElement('div');
                    locationDiv.className = 'record-location';
                    locationDiv.innerHTML = `
                        <div style="margin-right: 10px;">📍</div>
                        <div>${record.location.address}</div>
                    `;
                    recordDetail.appendChild(locationDiv);
                }

                // 显示经纬度
                if (record.location && record.location.longitude && record.location.latitude) {
                    const coordinateDiv = document.createElement('div');
                    coordinateDiv.className = 'record-location';

                    const coordinateContent = document.createElement('div');
                    coordinateContent.style.width = '100%';

                    // 经度
                    const longitudeItem = document.createElement('div');
                    longitudeItem.className = 'coordinate-item';

                    const longitudeLabel = document.createElement('span');
                    longitudeLabel.className = 'coordinate-label';
                    longitudeLabel.textContent = '经度:';

                    const longitudeValue = document.createElement('span');
                    longitudeValue.textContent = record.location.longitude.toFixed(6);

                    longitudeItem.appendChild(longitudeLabel);
                    longitudeItem.appendChild(longitudeValue);

                    // 纬度
                    const latitudeItem = document.createElement('div');
                    latitudeItem.className = 'coordinate-item';

                    const latitudeLabel = document.createElement('span');
                    latitudeLabel.className = 'coordinate-label';
                    latitudeLabel.textContent = '纬度:';

                    const latitudeValue = document.createElement('span');
                    latitudeValue.textContent = record.location.latitude.toFixed(6);

                    latitudeItem.appendChild(latitudeLabel);
                    latitudeItem.appendChild(latitudeValue);

                    coordinateContent.appendChild(longitudeItem);
                    coordinateContent.appendChild(latitudeItem);

                    coordinateDiv.innerHTML = `<div style="margin-right: 10px;">🧭</div>`;
                    coordinateDiv.appendChild(coordinateContent);

                    recordDetail.appendChild(coordinateDiv);
                }

                recordItem.appendChild(recordHeader);
                recordItem.appendChild(recordDetail);

                // 添加查看地图按钮
                if (record.location && record.location.longitude && record.location.latitude) {
                    const mapBtn = document.createElement('div');
                    mapBtn.style.marginTop = '10px';
                    mapBtn.style.textAlign = 'center';
                    mapBtn.style.padding = '8px';
                    mapBtn.style.backgroundColor = '#f0f8ff';
                    mapBtn.style.borderRadius = '6px';
                    mapBtn.style.color = '#1890ff';
                    mapBtn.style.cursor = 'pointer';
                    mapBtn.textContent = '在地图中查看/修改位置';

                    mapBtn.addEventListener('click', () => {
                        showLocationInMap(record.location.longitude, record.location.latitude, record.recordId);
                    });

                    recordItem.appendChild(mapBtn);
                }

                list.appendChild(recordItem);
            });
        }

        // 渲染照片记录
        function renderPhotos() {
            const container = document.getElementById('photosContainer');
            const grid = document.getElementById('photosGrid');

            if (photos.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            grid.innerHTML = '';

            photos.forEach(photo => {
                const photoItem = document.createElement('div');
                photoItem.className = 'photo-item';

                const img = document.createElement('img');
                img.className = 'photo-image';
                let imgUrl = 'http://10.168.160.218:9020/api/file/downloadDingImage/' + photo.url;
                img.src = imgUrl;
                img.alt = '工作场景照片';

                // 添加图片加载错误处理
                img.onerror = function (err) {
                    // err对象通常只有isTrusted属性，无需使用JSON.stringify
                    updateStatus(`图片加载失败: ${photo.url}`, 'error');
                };

                // 添加调试显示
                img.onload = function () {
                    // 将成功信息添加到 photoTime
                    const parent = this.parentNode;
                    const timeDiv = parent.querySelector('.photo-time');
                    if (timeDiv) {
                        timeDiv.innerHTML += `<div style="color:green;font-size:10px;margin-top:5px;">图片加载成功</div>`;
                    }
                };


                photoItem.appendChild(img);

                grid.appendChild(photoItem);
            });
        }

        // 渲染语音记录
        function renderVoiceRecords() {
            const container = document.getElementById('voiceRecordsContainer');
            const list = document.getElementById('voiceRecordsList');

            if (voiceRecords.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            list.innerHTML = '';

            voiceRecords.forEach(record => {
                const recordItem = document.createElement('div');
                recordItem.className = 'voice-record-item';

                const recordTime = document.createElement('div');
                recordTime.className = 'voice-record-time';
                recordTime.textContent = formatTime(record.timestamp);

                const recordText = document.createElement('div');
                recordText.className = 'voice-record-text';
                recordText.textContent = record.text || '无法识别的语音';

                recordItem.appendChild(recordTime);
                recordItem.appendChild(recordText);

                list.appendChild(recordItem);
            });
        }

        // 处理打卡按钮点击
        async function handleClockIn() {
            try {
                updateStatus('正在获取位置信息...', 'info');

                // 发送获取位置请求
                const result = await sendRequest('getLocation', {
                    accuracy: 200,
                    useCache: true
                });

                if (result && result.location) {
                    // 添加到本地记录
                    clockRecords.unshift({
                        recordId: result.recordId,
                        timestamp: result.timestamp,
                        location: result.location
                    });

                    // 渲染记录
                    renderClockRecords();

                    updateStatus('打卡成功', 'success');

                    // 3秒后恢复
                    setTimeout(() => {
                        updateStatus('准备就绪', 'info');
                    }, 3000);
                }
            } catch (error) {
                console.error('打卡失败:', error);
                updateStatus(`打卡失败: ${error.message}`, 'error');
            }
        }

        // 处理拍照按钮点击
        async function handleTakePhoto() {
            try {
                updateStatus('正在打开相机...', 'info');

                // 发送拍照请求
                const result = await sendRequest('takePhoto', {
                    count: 1,
                    position: 'back'
                });

                // 打印调试信息
                console.log('拍照结果:', JSON.stringify(result));

                if (result && result.photos && result.photos.length > 0) {
                    console.log('照片数据:', result.photos);

                    // 添加调试信息到状态栏
                    updateStatus(`获取到 ${result.photos.length} 张照片`, 'info');

                    // 添加时间戳到每个照片对象
                    const photosWithTimestamp = result.photos.map(photo => {
                        console.log('处理照片:', photo);
                        return {
                            ...photo,
                            timestamp: Date.now()
                        };
                    });

                    // 添加到本地照片数组
                    photos = [...photosWithTimestamp, ...photos];

                    // 渲染照片
                    renderPhotos();

                    updateStatus('拍照上传成功', 'success');

                    // 3秒后恢复
                    setTimeout(() => {
                        updateStatus('准备就绪', 'info');
                    }, 3000);
                }
            } catch (error) {
                console.error('拍照失败:', error);
                updateStatus(`拍照失败: ${error.message}`, 'error');
            }
        }

        // 在地图中显示位置
        async function showLocationInMap(longitude, latitude, recordId) {
            try {
                updateStatus('正在打开地图...', 'info');

                // 发送地图位置请求
                const result = await sendRequest('showLocationInMap', {
                    longitude,
                    latitude,
                    scope: 500,
                    recordId
                });

                if (result && result.location) {
                    // 更新对应的记录位置信息
                    const recordIndex = clockRecords.findIndex(record => record.recordId === recordId);

                    if (recordIndex !== -1) {
                        clockRecords[recordIndex].location = result.location;

                        // 重新渲染
                        renderClockRecords();

                        updateStatus('位置已更新', 'success');

                        // 3秒后恢复
                        setTimeout(() => {
                            updateStatus('准备就绪', 'info');
                        }, 3000);
                    }
                }
            } catch (error) {
                console.error('打开地图失败:', error);
                updateStatus(`打开地图失败: ${error.message}`, 'error');
            }
        }

        // 处理录音按钮点击
        async function handleRecordVoice() {
            try {
                updateStatus('正在准备录音...', 'info');
                addDebugLog('开始录音流程');

                // 显示录音弹窗
                const modal = document.getElementById('recordingModal');
                modal.classList.add('active');

                // 发送开始录音请求
                updateStatus('正在调用钉钉录音接口...', 'info');
                addDebugLog('调用钉钉录音接口');

                const result = await sendRequest('startRecord', {
                    maxDuration: 60 // 最大录音时长60秒
                });

                addDebugLog(`录音接口返回: ${JSON.stringify(result)}`);

                if (result && result.success) {
                    // 开始录音
                    isRecording = true;
                    recordingStartTime = Date.now();

                    // 开始计时
                    startRecordingTimer();

                    updateStatus('录音已开始，请对着麦克风说话', 'success');
                    addDebugLog('录音已开始', 'success');
                    // 在弹窗中也显示状态
                    document.querySelector('.recording-tip').textContent = '录音已开始，请对着麦克风说话';
                } else {
                    updateStatus(`录音初始化失败: ${JSON.stringify(result)}`, 'error');
                    addDebugLog(`录音初始化失败: ${JSON.stringify(result)}`, 'error');
                    document.querySelector('.recording-tip').textContent = `录音初始化失败: ${JSON.stringify(result)}`;
                }
            } catch (error) {
                console.error('开始录音失败:', error);
                updateStatus(`录音失败: ${error.message}`, 'error');
                addDebugLog(`录音失败: ${error.message}`, 'error');
                document.querySelector('.recording-tip').textContent = `录音失败: ${error.message}`;
                setTimeout(() => {
                    closeRecordingModal();
                }, 2000);
            }
        }

        // 开始录音计时器
        function startRecordingTimer() {
            const timeDisplay = document.getElementById('recordingTime');
            recordingTimer = setInterval(() => {
                const elapsed = Date.now() - recordingStartTime;
                timeDisplay.textContent = formatRecordingTime(elapsed);
            }, 1000);
        }

        // 停止录音
        async function stopRecording() {
            if (!isRecording) return;

            try {
                updateStatus('正在停止录音...', 'info');
                addDebugLog('正在停止录音...');
                document.querySelector('.recording-tip').textContent = '正在停止录音...';

                // 停止计时器
                if (recordingTimer) {
                    clearInterval(recordingTimer);
                    recordingTimer = null;
                }

                // 发送停止录音请求，并自动转为文字
                updateStatus('正在处理录音文件...', 'info');
                addDebugLog('正在处理录音文件...');
                document.querySelector('.recording-tip').textContent = '正在处理录音文件...';

                const result = await sendRequest('stopRecord', {
                    autoTranslate: true
                });

                addDebugLog(`录音结果: ${JSON.stringify(result)}`);
                console.log('录音结果:', JSON.stringify(result));
                updateStatus(`录音处理结果: ${JSON.stringify(result)}`, 'info');

                if (result && result.mediaId) {
                    // 检查是否成功转换为文字
                    if (result.text) {
                        addDebugLog(`语音识别成功: "${result.text}"`, 'success');
                        updateStatus(`语音识别成功: "${result.text}"`, 'success');
                    } else {
                        addDebugLog('语音识别失败，但录音已保存', 'error');
                        updateStatus('语音识别失败，但录音已保存', 'error');
                    }

                    // 添加到语音记录
                    voiceRecords.unshift({
                        mediaId: result.mediaId,
                        duration: result.duration,
                        timestamp: result.timestamp,
                        text: result.text || '无法识别的语音'
                    });

                    // 渲染语音记录
                    renderVoiceRecords();

                    // 3秒后恢复
                    setTimeout(() => {
                        updateStatus('准备就绪', 'info');
                    }, 3000);

                    // 关闭录音弹窗
                    closeRecordingModal();
                } else {
                    addDebugLog('未获取到录音文件ID', 'error');
                    updateStatus('未获取到录音文件ID', 'error');
                    document.querySelector('.recording-tip').textContent = '未获取到录音文件ID';
                    setTimeout(() => {
                        closeRecordingModal();
                    }, 2000);
                }
            } catch (error) {
                addDebugLog(`停止录音失败: ${error.message}`, 'error');
                updateStatus(`停止录音失败: ${error.message}`, 'error');
                document.querySelector('.recording-tip').textContent = `停止录音失败: ${error.message}`;
                setTimeout(() => {
                    closeRecordingModal();
                }, 2000);
            } finally {
                isRecording = false;
            }
        }

        // 关闭录音弹窗
        function closeRecordingModal() {
            const modal = document.getElementById('recordingModal');
            modal.classList.remove('active');

            // 如果还在录音，停止录音
            if (isRecording) {
                stopRecording();
            }

            // 重置录音状态
            isRecording = false;
            if (recordingTimer) {
                clearInterval(recordingTimer);
                recordingTimer = null;
            }
            document.getElementById('recordingTime').textContent = '00:00';
        }

        // 处理宿主页面的响应
        function handleHostResponse(response) {
            const { requestId, success, data, error } = response;

            // 查找对应的请求回调
            if (requestMap.has(requestId)) {
                const { resolve, reject } = requestMap.get(requestId);
                requestMap.delete(requestId);

                addDebugLog(`收到响应: ${JSON.stringify(response)}`);
                console.log('收到响应:', JSON.stringify(response));

                if (success) {
                    resolve(data);
                } else {
                    addDebugLog(`请求失败: ${error}`, 'error');
                    console.error('请求失败:', error);
                    reject(new Error(error || '请求失败'));
                }
            }
        }

        // 监听宿主页面消息
        window.addEventListener('message', function (event) {
            // 安全检查：确保消息来源可信
            // 在生产环境中应该指定具体的域名，而不是使用'*'

            const { data } = event;

            // 检查消息格式是否合法
            if (!data || typeof data !== 'object' || data.source !== 'host-app') {
                return;
            }

            addDebugLog(`收到宿主页面消息: ${JSON.stringify(data)}`);
            console.log('收到宿主页面消息:', JSON.stringify(data));

            // 处理响应类型的消息
            if (data.action === 'response' && data.requestId) {
                handleHostResponse(data);
            }
        });

        // 初始化时钟更新
        setInterval(updateTime, 1000);
        updateTime();

        // 页面加载完成
        window.onload = function () {
            console.log('iframe页面已加载完成');
            updateStatus('准备就绪，等待操作...', 'info');

            // 初始化调试面板
            const debugPanelVisible = localStorage.getItem('debugPanelVisible') === 'true';
            if (debugPanelVisible) {
                document.getElementById('debugPanel').classList.add('active');
                document.getElementById('debugToggle').textContent = '关闭';
            }

            // 绑定调试面板事件
            document.getElementById('debugToggle').addEventListener('click', toggleDebugPanel);
            document.getElementById('debugClear').addEventListener('click', clearDebugLogs);

            // 添加长按状态栏显示调试面板的功能
            const statusBar = document.getElementById('statusBar');
            let pressTimer;

            statusBar.addEventListener('touchstart', function () {
                pressTimer = setTimeout(function () {
                    toggleDebugPanel();
                }, 2000); // 长按2秒显示调试面板
            });

            statusBar.addEventListener('touchend', function () {
                clearTimeout(pressTimer);
            });

            addDebugLog('页面初始化完成', 'success');
        };

        // 绑定按钮事件
        document.getElementById('clockInBtn').addEventListener('click', handleClockIn);
        document.getElementById('takePhotoBtn').addEventListener('click', handleTakePhoto);
        document.getElementById('recordVoiceBtn').addEventListener('click', handleRecordVoice);
        document.getElementById('stopRecordingBtn').addEventListener('click', stopRecording);
        document.getElementById('closeRecordingModal').addEventListener('click', closeRecordingModal);
    </script>
</body>

</html>