<template>
    <div class="smart-clock-container">
        <Loading :config="loadingConfig" />

        <iframe
            ref="clockIframe"
            v-if="true"
            src="http://************:5501/src/views/smart-clock/index.html"
            width="100%"
            height="100%"
            frameborder="0"
            @load="handleIframeLoad"
        ></iframe>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useAppStore } from '@/stores/app';
import { useIframeService } from '@/services';
import Loading from '@/components/Loading.vue';
import * as dd from 'dingtalk-jsapi';
import { getBusinessUrl, BUSINESS_TYPES } from '@/utils/urls';

const title = import.meta.env.VITE_SMART_CLOCK_TITLE || '智慧打卡';
dd.biz.navigation.setTitle({
    title: title
});

const appStore = useAppStore();
const iframeService = useIframeService();

const clockIframe = ref(null);

// iframe相关
const iframeUrl = ref('');

// Loading配置
const loadingConfig = ref({
    visible: true,
    status: 'loading',
    message: '加载中...'
});

// 更新loading状态
const updateLoadingConfig = (status, message, visible = true) => {
    loadingConfig.value.status = status;
    loadingConfig.value.message = message;
    loadingConfig.value.visible = visible;
};

// 初始化
const init = async () => {
    try {
        const ticketResponse = await appStore.getTicket();
        if (ticketResponse?.returncode !== 0 || !ticketResponse?.result) {
            throw new Error(`获取钉钉票据失败: ${JSON.stringify(ticketResponse)}`);
        }

        iframeUrl.value = getBusinessUrl(BUSINESS_TYPES.SMART_CLOCK, {
            dingdingTicket: ticketResponse.result
        });
    } catch (error) {
        console.error('初始化失败:', error);
        updateLoadingConfig('failed', '初始化失败，请稍后再试', false);
    }
};

// 清理函数
let cleanupMessageListener = null;

const handleIframeLoad = () => {
    updateLoadingConfig('loaded', '初始化完成', false);

    // 添加消息事件监听（需要在iframe加载完成后）
    if (clockIframe.value?.contentWindow) {
        cleanupMessageListener = iframeService.setupMessageListener(
            clockIframe.value.contentWindow
        );
    } else {
        // 如果iframe还没准备好，稍后再试
        setTimeout(setupIframeListener, 1000);
    }
};

onMounted(() => {
    updateLoadingConfig('loading', '加载中，请稍候...');
    init();
});

onBeforeUnmount(() => {
    // 清理事件监听
    if (cleanupMessageListener) {
        cleanupMessageListener();
    }
});
</script>

<style lang="scss" scoped>
.smart-clock-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.title {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin: 10px 0 12px;
    text-align: center;
}

.iframe-container {
    width: 100%;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.clock-iframe {
    width: 100%;
    height: 85vh;
    border: none;
    background-color: #fff;
}
</style>
