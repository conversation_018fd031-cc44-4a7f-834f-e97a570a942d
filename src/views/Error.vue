<template>
    <div class="container p-5">
        <i
            class="pi pi-spin pi-spinner"
            style="font-size: 2rem"
            @click="closePage"
        ></i>
        <Button
            :label="route.query.errorMessage || '页面加载失败，请重试~'"
            severity="danger"
            @click="closePage"
            class="mt-5"
        />
    </div>
</template>
<script setup>
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useRoute } from 'vue-router';
const route = useRoute();
const closePage = () => {
    dd.closePage && dd.closePage();
    dd.quitPage && dd.quitPage();
};
</script>
<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0;
    }
}
</style>
