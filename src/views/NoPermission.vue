<template>
    <div class="container">
        <p>当前无系统权限，请联系管理员开通~</p>
        <Button label="退出页面" severity="contrast" @click="closePage" />
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import * as dd from 'dingtalk-jsapi'; // 直接导入

const closePage = () => {
    dd.closePage();
};

onMounted(() => {
    dd.biz.navigation.setTitle({
        title: '无权限'
    });
});
</script>

<style lang="scss" scoped>
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0;
    }
}
</style>
