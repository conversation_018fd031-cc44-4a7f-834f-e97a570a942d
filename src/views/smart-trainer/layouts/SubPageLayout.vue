<template>
    <div class="sub-page-layout">
        <!-- 子页面内容区域 - 不包含底部导航 -->
        <div class="sub-page-content">
            <RouterView />
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('智能训练器子页面布局（无底部导航）已加载');
});
</script>

<style lang="scss" scoped>
.sub-page-layout {
    height: 100%;
    width: 100%;

    .sub-page-content {
        height: 100%;
        overflow-y: auto;

        // 确保内容区域可以滚动
        -webkit-overflow-scrolling: touch;

        // 隐藏滚动条但保持滚动功能
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE

        &::-webkit-scrollbar {
            display: none !important; // Chrome, Safari, Edge
        }
    }
}
</style>
