/**
 * 分数等级配置
 * @type {Object}
 */
export const SCORE_LEVELS = {
    PERFECT: 'perfect',
    EXCELLENT: 'excellent',
    PASS: 'pass',
    FAIL: 'fail'
};

/**
 * 分数等级文案
 * @type {Object}
 */
export const SCORE_LEVEL_TEXT = {
    [SCORE_LEVELS.PERFECT]: '满分！保持优秀！',
    [SCORE_LEVELS.EXCELLENT]: '优秀！继续努力！',
    [SCORE_LEVELS.PASS]: '良好, 继续进步！',
    [SCORE_LEVELS.FAIL]: '需要提升, 请再接再厉！'
};

/**
 * 分数等级对应的颜色配置
 * @type {Object}
 */
export const SCORE_COLORS = {
    [SCORE_LEVELS.PERFECT]: {
        text: '#ff6b00',
        background: 'rgba(255, 107, 0, 0.1)',
        gradient: {
            start: '#ff6b00',
            end: '#ff9400'
        }
    },
    [SCORE_LEVELS.EXCELLENT]: {
        text: '#1677ff',
        background: 'rgba(22, 119, 255, 0.1)',
        gradient: {
            start: '#1677ff',
            end: '#4096ff'
        }
    },
    [SCORE_LEVELS.PASS]: {
        text: '#52c41a',
        background: 'rgba(82, 196, 26, 0.1)',
        gradient: {
            start: '#52c41a',
            end: '#73d13d'
        }
    },
    [SCORE_LEVELS.FAIL]: {
        text: '#ff4d4f',
        background: 'rgba(255, 77, 79, 0.1)',
        gradient: {
            start: '#ff4d4f',
            end: '#ff7875'
        }
    }
};

/**
 * 获取分数等级
 * @param {number} score - 当前分数
 * @param {number} maxScore - 满分值（默认100分）
 * @returns {string} 分数等级
 */
export const getScoreLevel = (score, maxScore = 100) => {
    const percentage = (score / maxScore) * 100;

    if (percentage === 100) {
        return SCORE_LEVELS.PERFECT;
    }
    if (percentage >= 80) {
        return SCORE_LEVELS.EXCELLENT;
    }
    if (percentage >= 60) {
        return SCORE_LEVELS.PASS;
    }
    return SCORE_LEVELS.FAIL;
};

/**
 * 获取分数等级文案
 * @param {number} score - 当前分数
 * @param {number} maxScore - 满分值（默认100分）
 * @returns {string} 分数等级文案
 */
export const getScoreLevelText = (score, maxScore = 100) => {
    const level = getScoreLevel(score, maxScore);
    return SCORE_LEVEL_TEXT[level];
};

/**
 * 获取分数样式
 * @param {number} score - 当前分数
 * @param {number} maxScore - 满分值（默认100分）
 * @returns {Object} 分数样式配置
 */
export const getScoreStyle = (score, maxScore = 100) => {
    const level = getScoreLevel(score, maxScore);
    return SCORE_COLORS[level];
};

/**
 * 获取分数等级类名
 * @param {number} score - 当前分数
 * @param {number} maxScore - 满分值（默认100分）
 * @returns {string} 分数等级类名
 */
export const getScoreClass = (score, maxScore = 100) => {
    return getScoreLevel(score, maxScore);
};
