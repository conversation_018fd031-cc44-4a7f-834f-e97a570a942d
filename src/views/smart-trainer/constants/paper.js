import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from './text';

// 试卷状态配置
export const PAPER_STATUS_CONFIG = {
    0: {
        text: EXAM_TEXT.PAPER_STATUS.CREATED.TEXT,
        severity: 'warning',
        showDetailBtn: true,
        showScore: false,
        canNavigate: true,
        buttonText: EXAM_TEXT.PAPER_STATUS.CREATED.BUTTON_TEXT
    },
    1: {
        text: EXAM_TEXT.PAPER_STATUS.IN_PROGRESS.TEXT,
        severity: 'info',
        showDetailBtn: true,
        showScore: false,
        canNavigate: true,
        buttonText: EXAM_TEXT.PAPER_STATUS.IN_PROGRESS.BUTTON_TEXT
    },
    3: {
        text: EXAM_TEXT.PAPER_STATUS.COMPLETED.TEXT,
        severity: 'warning',
        showDetailBtn: true,
        showScore: false,
        canNavigate: true,
        buttonText: EXAM_TEXT.PAPER_STATUS.COMPLETED.BUTTON_TEXT
    },
    4: {
        text: EXAM_TEXT.PAPER_STATUS.SCORED.TEXT,
        severity: 'success',
        showDetailBtn: true,
        showScore: true,
        canNavigate: true,
        buttonText: EXAM_TEXT.PAPER_STATUS.SCORED.BUTTON_TEXT
    }
};
