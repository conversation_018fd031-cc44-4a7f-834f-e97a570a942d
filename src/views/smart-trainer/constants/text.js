// 方案二：日常练习体系（轻松自然）
export const TRAINING_TEXT_SCHEMA2 = {
    PAGE_TITLES: {
        EXAM_REVIEW: '练习记录',
        SKILL_RADAR: '能力评价',
        TASK_DETAIL: '练习详情',
        SMART_TRAINING: '智能训练'
    },
    BUTTONS: {
        REVIEW_EXAM: '回顾练习',
        RETRY_PRACTICE: '再练一次',
        BACK_HOME: '回到主页',
        CONTINUE_ANSWER: '继续闯关',
        VIEW_SCORE: '查看成果',
        COMPLETE_ANSWER: '说完了',
        SUBMITTING: '正在提交...',
        NEXT_QUESTION: '开始下一题',
        START_TRAINING: '开始练习',
        BACK: '上一步',
        GET_ASSESSMENT: '本题已结束，单击获取反馈',
        RETRY: '重试',
        RETRYING: '重试中...',
        VIEW_RESULT: '查看整体评分',
        RETRY_QUESTION: '重说',
        RETRYING_QUESTION: '重练中...',
        CONTINUE_NEXT_QUESTION: '继续下一题'
    },
    STATUS: {
        CONVERTING: '转换中...',
        CONVERT_TEXT: '语音转文字',
        LOADING_EXAM: '加载练习记录...',
        LOADING_QUESTION: '准备题目中...',
        LOADING_HISTORY: '获取历史对话...',
        LOADING_UPLOAD: '录音上传中...',
        LOADING_UNCOMPLETED: '获取未完成练习列表...',
        // ... existing code ...
        DELETE: '删除',
        DELETING: '删除中',
        SUCCESS: '成功',
        ERROR: '错误',
        DELETE_SUCCESS: '删除成功',
        RETRY_SUCCESS: '重练成功，请重新作答',
        RESULT_LOADING: '雷达图生成中，请稍后...',
        INFO: '提示',
        CONTINUE_SUCCESS: '已跳过当前评价，进入下一题'
    },
    SCORE: {
        TOTAL_SCORE: '总分',
        QUESTION_COUNT: '练习题目',
        QUESTION_SUFFIX: '题',
        SCORE_SUFFIX: '分',
        CURRENT_SCORE: '本题得分'
    },
    AI_ASSESSMENT: {
        TITLE: '练习反馈',
        ANALYZING: '分析中'
    },
    EXAM_TIPS: {
        SUFFICIENT_TIME: '建议预留',
        STABLE_NETWORK: '保持网络通畅',
        GOOD_MINDSET: '放轻松慢慢来'
    },
    ERRORS: {
        REQUEST_FAILED: '网络不佳，请检查网络',
        MISSING_EXAM_ID: '缺少练习ID',
        CONVERT_TIMEOUT: '解析超时',
        DELETE_FAILED: '删除失败',
        RETRY_FAILED: '重练失败，请稍后再试',
        NO_RADAR_DATA: '暂无雷达图数据',
        DATA_LOAD_FAILED: '雷达图加载失败, 请检查网络'
    },
    EMPTY: {
        RANK_NO_DATA: '暂无人上榜'
    },
    PAPER_STATUS: {
        CREATED: {
            TEXT: '就绪',
            BUTTON_TEXT: '开始练习'
        },
        IN_PROGRESS: {
            TEXT: '进行中',
            BUTTON_TEXT: '继续练习'
        },
        COMPLETED: {
            TEXT: '完成',
            BUTTON_TEXT: '查看结果'
        },
        SCORED: {
            TEXT: '结果已出',
            BUTTON_TEXT: '查看详情'
        }
    },
    EXAM_INFO: {
        ABILITIES: '考核维度',
        DESCRIPTION: '题目说明',
        TYPE: '练习',
        QUESTION: '小题'
    },
    CONFIRM: {
        TITLE: '确认操作',
        DELETE_ANSWER: '确定要删除这条回答吗？删除后无法恢复。'
    }
};

// 方案一：专业测评体系（正式严谨）
export const TRAINING_TEXT_SCHEMA1 = {
    PAGE_TITLES: {
        EXAM_REVIEW: '能力评估报告',
        SKILL_RADAR: '专业能力矩阵',
        TASK_DETAIL: '测评项详情',
        SMART_TRAINING: '智能测评中心'
    },
    BUTTONS: {
        REVIEW_EXAM: '查看报告',
        RETRY_PRACTICE: '重新测评',
        BACK_HOME: '返回主控台',
        CONTINUE_ANSWER: '继续测评',
        VIEW_SCORE: '查看评估',
        COMPLETE_ANSWER: '提交答卷',
        SUBMITTING: '评估计算中...',
        NEXT_QUESTION: '进入下一测评项',
        START_TRAINING: '启动测评',
        BACK: '返回上级'
    },
    STATUS: {
        CONVERTING: '识别中...',
        CONVERT_TEXT: '语音转写',
        LOADING_EXAM: '加载测评档案...',
        LOADING_QUESTION: '获取测评项...',
        LOADING_HISTORY: '调取历史数据...'
    },
    SCORE: {
        TOTAL_SCORE: '综合得分',
        QUESTION_COUNT: '测评项数',
        QUESTION_SUFFIX: '项',
        SCORE_SUFFIX: '分',
        CURRENT_SCORE: '本项得分'
    },
    AI_ASSESSMENT: {
        TITLE: '智能评估',
        ANALYZING: '评估分析中'
    },
    EXAM_TIPS: {
        SUFFICIENT_TIME: '建议预留',
        STABLE_NETWORK: '确保5G/Wi-Fi连接稳定',
        GOOD_MINDSET: '保持专业态度'
    },
    ERRORS: {
        REQUEST_FAILED: '系统繁忙，请重试',
        MISSING_EXAM_ID: '参数缺失：examId',
        CONVERT_TIMEOUT: '语音识别超时'
    },
    EMPTY: {
        RANK_NO_DATA: '暂无评估数据'
    },
    PAPER_STATUS: {
        CREATED: {
            TEXT: '测评初始化完成',
            BUTTON_TEXT: '进入测评'
        },
        IN_PROGRESS: {
            TEXT: '测评进行中',
            BUTTON_TEXT: '继续作答'
        },
        COMPLETED: {
            TEXT: '测评已封存',
            BUTTON_TEXT: '查看报告'
        },
        SCORED: {
            TEXT: '评估已完成',
            BUTTON_TEXT: '查看评估'
        }
    },
    EXAM_INFO: {
        ABILITIES: '核心能力维度',
        DESCRIPTION: '基于岗位胜任力模型的专业能力评估',
        TYPE: '专业能力测评'
    }
};

// 方案三：情景演练体系（场景化）
export const TRAINING_TEXT_SCHEMA3 = {
    PAGE_TITLES: {
        EXAM_REVIEW: '实战演练复盘',
        SKILL_RADAR: '情景应对能力图',
        TASK_DETAIL: '作战任务简报',
        SMART_TRAINING: '战术指挥中心'
    },
    BUTTONS: {
        REVIEW_EXAM: '回放演练',
        RETRY_PRACTICE: '重新演练',
        BACK_HOME: '返回指挥中心',
        CONTINUE_ANSWER: '继续任务',
        VIEW_SCORE: '查看战报',
        COMPLETE_ANSWER: '结束演练',
        SUBMITTING: '生成战报中...',
        NEXT_QUESTION: '进入下一情景',
        START_TRAINING: '开始演练',
        BACK: '战术回撤'
    },
    STATUS: {
        CONVERTING: '情报解析中...',
        CONVERT_TEXT: '解析指令',
        LOADING_EXAM: '加载作战记录...',
        LOADING_QUESTION: '部署新情景...',
        LOADING_HISTORY: '调取作战日志...'
    },
    SCORE: {
        TOTAL_SCORE: '综合战力',
        QUESTION_COUNT: '情景任务',
        QUESTION_SUFFIX: '个情景',
        SCORE_SUFFIX: '战力',
        CURRENT_SCORE: '本情景得分'
    },
    AI_ASSESSMENT: {
        TITLE: '战术指挥官评价',
        ANALYZING: '战术分析中'
    },
    EXAM_TIPS: {
        SUFFICIENT_TIME: '每个情景',
        STABLE_NETWORK: '保持通讯畅通',
        GOOD_MINDSET: '保持战术冷静'
    },
    ERRORS: {
        REQUEST_FAILED: '指挥系统异常',
        MISSING_EXAM_ID: '作战指令丢失',
        CONVERT_TIMEOUT: '情报解析超时'
    },
    EMPTY: {
        RANK_NO_DATA: '战术排行榜生成中'
    },
    PAPER_STATUS: {
        CREATED: {
            TEXT: '作战简报就绪',
            BUTTON_TEXT: '进入战场'
        },
        IN_PROGRESS: {
            TEXT: '行动进行时',
            BUTTON_TEXT: '继续作战'
        },
        COMPLETED: {
            TEXT: '演习结束',
            BUTTON_TEXT: '查看战报'
        },
        SCORED: {
            TEXT: '战后评估完成',
            BUTTON_TEXT: '查看战绩'
        }
    },
    EXAM_INFO: {
        ABILITIES: '战术能力维度',
        DESCRIPTION: '多场景实战模拟与战术决策训练',
        TYPE: '情景战术演练'
    }
};

// 方案四：业绩逆袭体系（狼性特训）
export const TRAINING_TEXT_SCHEMA4 = {
    PAGE_TITLES: {
        EXAM_REVIEW: '战报复盘',
        SKILL_RADAR: '业绩攻坚图',
        TASK_DETAIL: '签单战场',
        SMART_TRAINING: '狼性特训营'
    },
    BUTTONS: {
        REVIEW_EXAM: '回看战局',
        RETRY_PRACTICE: '再战一单',
        BACK_HOME: '返回战壕',
        CONTINUE_ANSWER: '继续冲锋',
        VIEW_SCORE: '查看战果',
        COMPLETE_ANSWER: '拿下订单',
        SUBMITTING: '战报生成中...',
        NEXT_QUESTION: '下一客户',
        START_TRAINING: '开战',
        BACK: '调整战术'
    },
    STATUS: {
        CONVERTING: '分析客户画像...',
        CONVERT_TEXT: '查看话术',
        LOADING_EXAM: '加载战报...',
        LOADING_QUESTION: '生成客户案例...',
        LOADING_HISTORY: '调取成交记录...'
    },
    SCORE: {
        TOTAL_SCORE: '业绩指数',
        QUESTION_COUNT: '攻坚客户',
        QUESTION_SUFFIX: '个客户',
        SCORE_SUFFIX: '万业绩',
        CURRENT_SCORE: '本单进度'
    },
    AI_ASSESSMENT: {
        TITLE: '战况分析',
        ANALYZING: '生成作战方案'
    },
    EXAM_TIPS: {
        SUFFICIENT_TIME: '每个客户',
        STABLE_NETWORK: '保持通讯畅通',
        GOOD_MINDSET: '没有难缠的客户，只有不够的努力'
    },
    ERRORS: {
        REQUEST_FAILED: '业绩过载，重拨一次',
        MISSING_EXAM_ID: '客户ID丢失',
        CONVERT_TIMEOUT: '响应超时'
    },
    EMPTY: {
        RANK_NO_DATA: '战场静默中'
    },
    PAPER_STATUS: {
        CREATED: {
            TEXT: '战前准备',
            BUTTON_TEXT: '发起攻坚'
        },
        IN_PROGRESS: {
            TEXT: '战斗进行时',
            BUTTON_TEXT: '死磕到底'
        },
        COMPLETED: {
            TEXT: '战役结束',
            BUTTON_TEXT: '查看战果'
        },
        SCORED: {
            TEXT: '战绩封榜',
            BUTTON_TEXT: '查看排名'
        }
    },
    EXAM_INFO: {
        ABILITIES: '核心战力',
        DESCRIPTION: '基于真实客户案例的狼性特训',
        TYPE: '销售攻坚战'
    }
};

// 使用示例：

// 专业测评场景
// import { TRAINING_TEXT_SCHEMA1 as TEXT } from './text'

// 成长训练场景
// import { TRAINING_TEXT_SCHEMA2 as TEXT } from './text'

// 情景演练场景
// import { TRAINING_TEXT_SCHEMA3 as TEXT } from './text'
