<template>
    <Dialog
        v-model:visible="dialogVisible"
        modal
        :closable="false"
        :draggable="false"
        class="error-panel-dialog"
        :style="{ width: '90vw', maxWidth: '500px' }"
    >
        <template #header>
            <div class="error-header">
                <i class="pi pi-times-circle error-icon"></i>
                <span class="error-title">答案错误</span>
            </div>
        </template>

        <div class="error-content">
            <!-- 题目信息 -->
            <div class="question-info">
                <div class="question-text">{{ question?.question }}</div>
                <div class="question-type">{{ getQuestionTypeText(question?.type) }}</div>
            </div>

            <!-- 答案对比 -->
            <div class="answer-comparison">
                <!-- 用户答案 -->
                <div class="answer-section user-answer">
                    <div class="answer-label">
                        <i class="pi pi-user"></i>
                        <span>您的答案</span>
                    </div>
                    <div class="answer-content error">
                        {{ formatAnswer(userAnswer, question?.type) }}
                    </div>
                </div>

                <!-- 正确答案 -->
                <div class="answer-section correct-answer">
                    <div class="answer-label">
                        <i class="pi pi-check-circle"></i>
                        <span>正确答案</span>
                    </div>
                    <div class="answer-content correct">
                        {{ formatAnswer(correctAnswer, question?.type) }}
                    </div>
                </div>
            </div>

            <!-- 选项详情（仅单选和多选题显示） -->
            <div v-if="question?.options && (question.type === 'single' || question.type === 'multi')" class="options-detail">
                <div class="options-title">选项详情</div>
                <div class="options-list">
                    <div
                        v-for="option in question.options"
                        :key="option.key"
                        class="option-item"
                        :class="{
                            'correct-option': isCorrectOption(option.key),
                            'user-selected': isUserSelected(option.key),
                            'wrong-selected': isUserSelected(option.key) && !isCorrectOption(option.key)
                        }"
                    >
                        <div class="option-indicator">
                            <i v-if="isCorrectOption(option.key)" class="pi pi-check correct-mark"></i>
                            <i v-else-if="isUserSelected(option.key)" class="pi pi-times wrong-mark"></i>
                        </div>
                        <div class="option-content">
                            <span class="option-key">{{ option.key }}.</span>
                            <span class="option-text">{{ option.text }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="error-footer">
                <Button
                    class="confirm-btn"
                    @click="handleConfirm"
                    autofocus
                >
                    <i class="pi pi-arrow-right"></i>
                    <span>继续下一题</span>
                </Button>
            </div>
        </template>
    </Dialog>
</template>

<script setup>
import { computed, watch } from 'vue';

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    question: {
        type: Object,
        default: null
    },
    userAnswer: {
        type: [String, Boolean, Array],
        default: null
    },
    correctAnswer: {
        type: [String, Boolean, Array],
        default: null
    }
});

// Emits
const emit = defineEmits(['update:visible', 'confirm']);

// 计算属性
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 方法
const getQuestionTypeText = (type) => {
    const typeMap = {
        'single': '单选题',
        'multi': '多选题',
        'true_false': '判断题'
    };
    return typeMap[type] || '未知题型';
};

const formatAnswer = (answer, questionType) => {
    if (answer === null || answer === undefined) {
        return '未选择';
    }

    if (questionType === 'true_false') {
        return answer ? '正确' : '错误';
    }

    if (Array.isArray(answer)) {
        return answer.length > 0 ? answer.join(', ') : '未选择';
    }

    return answer;
};

const isCorrectOption = (optionKey) => {
    if (Array.isArray(props.correctAnswer)) {
        return props.correctAnswer.includes(optionKey);
    }
    return props.correctAnswer === optionKey;
};

const isUserSelected = (optionKey) => {
    if (Array.isArray(props.userAnswer)) {
        return props.userAnswer.includes(optionKey);
    }
    return props.userAnswer === optionKey;
};

const handleConfirm = () => {
    emit('confirm');
};
</script>

<style lang="scss" scoped>
:deep(.error-panel-dialog) {
    .p-dialog-header {
        background: #fff5f5;
        border-bottom: 1px solid #fed7d7;
        padding: 20px 24px;
    }

    .p-dialog-content {
        padding: 24px;
    }

    .p-dialog-footer {
        padding: 20px 24px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }
}

.error-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .error-icon {
        font-size: 24px;
        color: #e53e3e;
    }

    .error-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
    }
}

.error-content {
    .question-info {
        margin-bottom: 24px;
        padding: 16px;
        background: #f7fafc;
        border-radius: 8px;
        border-left: 4px solid #4299e1;

        .question-text {
            font-size: 15px;
            line-height: 1.5;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .question-type {
            font-size: 12px;
            color: #718096;
            background: #e2e8f0;
            padding: 2px 8px;
            border-radius: 4px;
            display: inline-block;
        }
    }

    .answer-comparison {
        display: grid;
        gap: 16px;
        margin-bottom: 24px;

        .answer-section {
            padding: 16px;
            border-radius: 8px;
            border: 2px solid;

            &.user-answer {
                background: #fff5f5;
                border-color: #fed7d7;
            }

            &.correct-answer {
                background: #f0fff4;
                border-color: #c6f6d5;
            }

            .answer-label {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;
                font-size: 14px;
                font-weight: 600;

                i {
                    font-size: 16px;
                }
            }

            .answer-content {
                font-size: 15px;
                font-weight: 500;

                &.error {
                    color: #e53e3e;
                }

                &.correct {
                    color: #38a169;
                }
            }
        }
    }

    .options-detail {
        .options-title {
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 12px;
        }

        .options-list {
            .option-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 12px;
                margin-bottom: 8px;
                border-radius: 6px;
                border: 1px solid #e2e8f0;
                background: white;

                &.correct-option {
                    background: #f0fff4;
                    border-color: #9ae6b4;
                }

                &.wrong-selected {
                    background: #fff5f5;
                    border-color: #feb2b2;
                }

                .option-indicator {
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    margin-top: 2px;

                    .correct-mark {
                        color: #38a169;
                        font-size: 14px;
                    }

                    .wrong-mark {
                        color: #e53e3e;
                        font-size: 14px;
                    }
                }

                .option-content {
                    flex: 1;
                    line-height: 1.4;

                    .option-key {
                        font-weight: 600;
                        color: #4299e1;
                        margin-right: 6px;
                    }

                    .option-text {
                        color: #2d3748;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

.error-footer {
    display: flex;
    justify-content: center;

    .confirm-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: linear-gradient(135deg, #4299e1, #3182ce);
        border: none;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        }

        i {
            font-size: 14px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    :deep(.error-panel-dialog) {
        .p-dialog-header,
        .p-dialog-content,
        .p-dialog-footer {
            padding: 16px;
        }
    }

    .error-content {
        .answer-comparison {
            gap: 12px;
        }

        .options-detail .options-list .option-item {
            padding: 10px;
        }
    }
}
</style>
