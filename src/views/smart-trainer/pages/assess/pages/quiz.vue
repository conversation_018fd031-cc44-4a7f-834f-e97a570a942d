<template>
    <div class="quiz-page" :class="{ 'dark-theme': isDarkMode }">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载考试题目...</p>
        </div>

        <!-- 考试不存在 -->
        <div v-else-if="!examData" class="not-found">
            <i class="pi pi-exclamation-triangle"></i>
            <h3>考试不存在</h3>
            <p>您访问的考试可能已被删除或不存在</p>
            <button class="back-btn" @click="goBack">
                <i class="pi pi-arrow-left"></i>
                <span>返回列表</span>
            </button>
        </div>

        <!-- 考试内容 -->
        <div v-else class="quiz-player">
            <!-- 题目进度指示器 -->
            <div class="quiz-header">
                <div class="exam-title">{{ examData.name }}</div>
                <div class="info-card">
                    <div class="stats-bar">
                        <div class="progress-info">
                            <span class="current-question">{{ currentQuestionIndex + 1 }}</span>
                            <span class="separator">/</span>
                            <span class="total-questions">{{ questions.length }}</span>
                        </div>
                        <div class="progress-bar">
                            <div
                                class="progress-fill"
                                :style="{ width: progressPercentage + '%' }"
                            ></div>
                        </div>
                        <div class="timer">
                            <i class="pi pi-clock"></i>
                            <span>44:20</span>
                        </div>
                    </div>
                    <div class="question-stats">
                        {{ questionTypeSummaryText }}
                    </div>
                </div>
            </div>

            <!-- Swiper 容器 -->
            <div class="quiz-content">
                <Swiper
                    ref="swiperRef"
                    :modules="[Navigation]"
                    :slides-per-view="1"
                    :space-between="0"
                    :allow-touch-move="false"
                    :navigation="false"
                    @swiper="onSwiperInit"
                    @slide-change="onSlideChange"
                    class="quiz-swiper"
                >
                    <SwiperSlide v-for="(question, index) in questions" :key="question.id">
                        <div class="question-slide">
                            <div class="question-card">
                                <!-- 题目标题 -->
                                <div class="question-header">
                                    <span class="question-index-badge">
                                        {{ index + 1 }}/{{ questions.length }}
                                    </span>
                                    <span
                                        class="question-type-badge"
                                        :class="getQuestionTypeBadgeClass(question.type)"
                                    >
                                        {{ getQuestionTypeText(question.type) }} (5分)
                                    </span>
                                </div>

                                <!-- 题目内容 -->
                                <div class="question-content">
                                    <div class="question-text">
                                        {{ question.question }}
                                    </div>
                                </div>

                                <!-- 选项区域 -->
                                <div class="options-section">
                                    <!-- 单选题选项 -->
                                    <template v-if="question.type === 'single'">
                                        <div
                                            v-for="option in question.options"
                                            :key="option.key"
                                            class="option-item"
                                            :class="{
                                                selected: userAnswers[question.id] === option.key,
                                                disabled: isAnswered(question.id),
                                                'correct-answer':
                                                    isAnswered(question.id) &&
                                                    isCorrectOption(question.id, option.key),
                                                'wrong-answer':
                                                    isAnswered(question.id) &&
                                                    isUserWrongOption(question.id, option.key)
                                            }"
                                            @click="selectSingleOption(question.id, option.key)"
                                        >
                                            <!-- 你选择的标签 -->
                                            <div
                                                v-if="
                                                    isAnswered(question.id) &&
                                                    isUserWrongOption(question.id, option.key)
                                                "
                                                class="user-choice-tag"
                                            >
                                                你选择的
                                            </div>
                                            <span class="option-key">{{ option.key }}.</span>
                                            <span class="option-text">{{ option.text }}</span>
                                        </div>
                                    </template>

                                    <!-- 多选题选项 -->
                                    <template v-else-if="question.type === 'multi'">
                                        <div
                                            v-for="option in question.options"
                                            :key="option.key"
                                            class="option-item"
                                            :class="{
                                                selected: isMultiOptionSelected(
                                                    question.id,
                                                    option.key
                                                ),
                                                disabled: isAnswered(question.id),
                                                'correct-answer':
                                                    isAnswered(question.id) &&
                                                    isCorrectOption(question.id, option.key),
                                                'wrong-answer':
                                                    isAnswered(question.id) &&
                                                    isUserWrongOption(question.id, option.key)
                                            }"
                                            @click="toggleMultiOption(question.id, option.key)"
                                        >
                                            <!-- 你选择的标签 -->
                                            <div
                                                v-if="
                                                    isAnswered(question.id) &&
                                                    isUserWrongOption(question.id, option.key)
                                                "
                                                class="user-choice-tag"
                                            >
                                                你选择的
                                            </div>
                                            <span class="option-key">{{ option.key }}.</span>
                                            <span class="option-text">{{ option.text }}</span>
                                        </div>
                                    </template>

                                    <!-- 判断题选项 -->
                                    <template v-else-if="question.type === 'true_false'">
                                        <div class="judge-options">
                                            <div
                                                class="option-item judge-option"
                                                :class="{
                                                    selected: userAnswers[question.id] === true,
                                                    disabled: isAnswered(question.id),
                                                    'correct-answer':
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, true) ===
                                                            'correct',
                                                    'wrong-answer':
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, true) ===
                                                            'wrong'
                                                }"
                                                @click="selectJudgeOption(question.id, true)"
                                            >
                                                <!-- 你选择的标签 -->
                                                <div
                                                    v-if="
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, true) ===
                                                            'wrong'
                                                    "
                                                    class="user-choice-tag"
                                                >
                                                    你选择的
                                                </div>
                                                <i class="pi pi-check judge-icon"></i>
                                                <span class="option-text">正确</span>
                                            </div>
                                            <div
                                                class="option-item judge-option"
                                                :class="{
                                                    selected: userAnswers[question.id] === false,
                                                    disabled: isAnswered(question.id),
                                                    'correct-answer':
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, false) ===
                                                            'correct',
                                                    'wrong-answer':
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, false) ===
                                                            'wrong'
                                                }"
                                                @click="selectJudgeOption(question.id, false)"
                                            >
                                                <!-- 你选择的标签 -->
                                                <div
                                                    v-if="
                                                        isAnswered(question.id) &&
                                                        getJudgeOptionStatus(question.id, false) ===
                                                            'wrong'
                                                    "
                                                    class="user-choice-tag"
                                                >
                                                    你选择的
                                                </div>
                                                <i class="pi pi-times judge-icon"></i>
                                                <span class="option-text">错误</span>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>

            <!-- 底部操作栏 -->
            <div class="quiz-footer">
                <button
                    class="footer-button next-btn"
                    @click="submitAnswer(currentQuestion.id)"
                    :disabled="!hasUserAnswer(currentQuestion.id) || isAnswered(currentQuestion.id)"
                >
                    {{ isLastQuestion ? '提交' : '下一题' }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import { useToast } from 'primevue/usetoast';

// 引入 Swiper 样式
import 'swiper/css';
import 'swiper/css/navigation';

// 路由
const route = useRoute();
const router = useRouter();

// Toast
const toast = useToast();

// 响应式数据
const loading = ref(true);
const examData = ref(null);
const swiperRef = ref(null);
const swiperInstance = ref(null);
const currentQuestionIndex = ref(0);
const userAnswers = ref({});
const answeredQuestions = ref(new Set());
const isDarkMode = ref(false); // Can be toggled for theme changes

// Mock 考试数据（实际应该从API获取）
const mockExamData = {
    exam_001: {
        id: 'exam_001',
        name: '廉洁合规测试',
        typeLabel: '廉洁合规',
        questions: [
            {
                id: 'single_1',
                type: 'single',
                question:
                    '国庆节前，公司员工李某在下班后，参加由合作供应商王老板组织的高端商务宴请。宴请中，为了对李某在公司某项目中的"照顾"表示感谢，王老板当场赠送其价值为5000元的"国庆大礼包"作为节日礼物。李某第一时间表示拒绝，但在王老板的再三游说下，其接受该了礼品，并于当日带回家中。事后，李某也未将该礼品在规定时间内向公司行政部上交登记。以下李某的行为中推荐的是（）',
                options: [
                    { key: 'A', text: '李某参加由合作供应商王老板组织的高端商务宴请' },
                    { key: 'B', text: '李某收到"国庆大礼包"时第一时间表示拒绝' },
                    { key: 'C', text: '李某接受了礼品并带回家中' },
                    {
                        key: 'D',
                        text: '李某对于未能拒收的礼品在收到之日起7个工作日内未向行政部上交登记'
                    }
                ],
                answer: 'B'
            },
            {
                id: 'multi_1',
                type: 'multi',
                question: '根据公司廉洁合规相关制度规定，下列属于从重处罚情节的是（）',
                options: [
                    { key: 'A', text: '指使、授意、教唆或胁迫其他员工进行违法违规行为' },
                    {
                        key: 'B',
                        text: '隐瞒违规事实或隐匿、伪造、篡改、毁灭证据，抗拒、妨碍、不配合调查和处理'
                    },
                    { key: 'C', text: '对检举人、证人、调查处理人等实施威胁、恐吓或打击报复' },
                    { key: 'D', text: '对自己的违规行为进行合理解释' }
                ],
                answer: ['A', 'B', 'C']
            },
            {
                id: 'true_false_1',
                type: 'true_false',
                question: '发现他人违规违纪行为后，向直属上级或公司内审稽核部门反映',
                answer: true
            }
        ]
    }
};

// 计算属性
const questions = computed(() => examData.value?.questions || []);
const progressPercentage = computed(() => {
    if (questions.value.length === 0) {
        return 0;
    }
    return ((currentQuestionIndex.value + 1) / questions.value.length) * 100;
});
const currentQuestion = computed(() => questions.value[currentQuestionIndex.value]);

const isLastQuestion = computed(() => currentQuestionIndex.value === questions.value.length - 1);

const questionTypeSummary = computed(() => {
    if (!questions.value) {
        return {};
    }
    return questions.value.reduce((acc, q) => {
        const typeText = getQuestionTypeText(q.type);
        if (!acc[typeText]) {
            acc[typeText] = 0;
        }
        acc[typeText]++;
        return acc;
    }, {});
});

const questionTypeSummaryText = computed(() => {
    return Object.entries(questionTypeSummary.value)
        .map(([type, count]) => `${type}: ${count}道`)
        .join('  ');
});

onMounted(() => {
    loadExamData();
});

// 方法
const loadExamData = async () => {
    try {
        loading.value = true;
        const examId = route.params.examId;

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 获取考试数据
        examData.value = mockExamData[examId] || null;
    } catch (error) {
        console.error('加载考试数据失败:', error);
        examData.value = null;
    } finally {
        loading.value = false;
    }
};

// Swiper 初始化
const onSwiperInit = swiper => {
    swiperInstance.value = swiper;
};

// 滑动变化处理
const onSlideChange = swiper => {
    currentQuestionIndex.value = swiper.activeIndex;
};

// 题目类型相关方法
const getQuestionTypeText = type => {
    const typeMap = {
        single: '单选题',
        multi: '多选题',
        true_false: '判断题'
    };
    return typeMap[type] || '未知题型';
};

const getQuestionTypeBadgeClass = type => {
    const classMap = {
        single: 'badge-single',
        multi: 'badge-multi',
        true_false: 'badge-judge'
    };
    return classMap[type] || '';
};

// 答案选择相关方法
const selectSingleOption = (questionId, optionKey) => {
    if (isAnswered(questionId)) {
        return;
    }
    userAnswers.value[questionId] = optionKey;
};

const toggleMultiOption = (questionId, optionKey) => {
    if (isAnswered(questionId)) {
        return;
    }

    if (!userAnswers.value[questionId]) {
        userAnswers.value[questionId] = [];
    }

    const currentAnswers = userAnswers.value[questionId];
    const index = currentAnswers.indexOf(optionKey);

    if (index > -1) {
        currentAnswers.splice(index, 1);
    } else {
        currentAnswers.push(optionKey);
    }
};

const selectJudgeOption = (questionId, value) => {
    if (isAnswered(questionId)) {
        return;
    }
    userAnswers.value[questionId] = value;
};

const isMultiOptionSelected = (questionId, optionKey) => {
    const answers = userAnswers.value[questionId];
    return Array.isArray(answers) && answers.includes(optionKey);
};

const hasUserAnswer = questionId => {
    const answer = userAnswers.value[questionId];
    if (answer === null || answer === undefined) {
        return false;
    }
    if (Array.isArray(answer)) {
        return answer.length > 0;
    }
    return true;
};

const isAnswered = questionId => {
    return answeredQuestions.value.has(questionId);
};

// 判断选项是否是正确答案
const isCorrectOption = (questionId, optionKey) => {
    const question = questions.value.find(q => q.id === questionId);
    if (!question) {
        return false;
    }

    const correctAnswer = question.answer;
    if (Array.isArray(correctAnswer)) {
        return correctAnswer.includes(optionKey);
    }
    return correctAnswer === optionKey;
};

// 判断选项是否是用户选择的错误答案
const isUserWrongOption = (questionId, optionKey) => {
    if (!isAnswered(questionId)) {
        return false;
    }

    const question = questions.value.find(q => q.id === questionId);
    if (!question) {
        return false;
    }

    const userAnswer = userAnswers.value[questionId];
    const isUserSelected = Array.isArray(userAnswer)
        ? userAnswer.includes(optionKey)
        : userAnswer === optionKey;

    return isUserSelected && !isCorrectOption(questionId, optionKey);
};

// 判断判断题选项状态
const getJudgeOptionStatus = (questionId, value) => {
    if (!isAnswered(questionId)) {
        return '';
    }

    const question = questions.value.find(q => q.id === questionId);
    if (!question) {
        return '';
    }

    const userAnswer = userAnswers.value[questionId];
    const correctAnswer = question.answer;

    // 如果是正确答案
    if (value === correctAnswer) {
        return 'correct';
    }

    // 如果是用户选择的错误答案
    if (value === userAnswer && userAnswer !== correctAnswer) {
        return 'wrong';
    }

    return '';
};

// 提交答案
const submitAnswer = questionId => {
    const question = questions.value.find(q => q.id === questionId);
    if (!question) {
        return;
    }

    // 检查答案是否正确
    const userAnswer = userAnswers.value[questionId];
    const isCorrect = checkAnswer(userAnswer, question.answer);

    // 标记为已回答
    answeredQuestions.value.add(questionId);

    // 显示答题结果提示
    if (isCorrect) {
        toast.add({
            severity: 'success',
            summary: '回答正确！',
            detail: '恭喜您答对了这道题目',
            life: 2000
        });
    } else {
        toast.add({
            severity: 'error',
            summary: '回答错误',
            detail: '很遗憾，这道题目答错了',
            life: 2000
        });
    }

    // 延迟2秒后进入下一题或完成考试
    setTimeout(() => {
        if (!isLastQuestion.value) {
            goToNextQuestion();
        } else {
            // 最后一题，完成考试
            completeQuiz();
        }
    }, 2000);
};

// 检查答案是否正确
const checkAnswer = (userAnswer, correctAnswer) => {
    if (Array.isArray(correctAnswer)) {
        // 多选题
        if (!Array.isArray(userAnswer)) {
            return false;
        }
        if (userAnswer.length !== correctAnswer.length) {
            return false;
        }
        return userAnswer.sort().join(',') === correctAnswer.sort().join(',');
    } else {
        // 单选题和判断题
        return userAnswer === correctAnswer;
    }
};

// 进入下一题
const goToNextQuestion = () => {
    if (currentQuestionIndex.value < questions.value.length - 1) {
        swiperInstance.value?.slideNext();
    } else {
        // 所有题目完成，跳转到结果页
        completeQuiz();
    }
};

// 完成考试
const completeQuiz = () => {
    const results = questions.value.map(question => {
        const userAnswer = userAnswers.value[question.id];
        const isCorrect = checkAnswer(userAnswer, question.answer);
        return {
            questionId: question.id,
            question: question.question,
            userAnswer,
            correctAnswer: question.answer,
            isCorrect
        };
    });

    const correctCount = results.filter(r => r.isCorrect).length;
    const accuracy = (correctCount / results.length) * 100;

    // 将结果存储到 sessionStorage 中，供结果页使用
    const resultData = {
        examId: route.params.examId,
        examData: examData.value,
        results,
        correctCount,
        totalCount: results.length,
        accuracy: Math.round(accuracy * 100) / 100
    };

    sessionStorage.setItem('quiz-result', JSON.stringify(resultData));

    // 跳转到结果页
    router.push(`/smart-trainer/assess/result/${route.params.examId}`);
};

const goBack = () => {
    router.push('/smart-trainer/assess/list');
};
</script>

<style lang="scss" scoped>
.quiz-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #1890ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;
    text-align: center;

    i {
        font-size: 64px;
        margin-bottom: 20px;
        color: #ffc107;
    }

    h3 {
        font-size: 24px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.5;
    }

    .back-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }

        i {
            font-size: 16px;
        }
    }
}

.quiz-player {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.quiz-header {
    padding: 16px;
    z-index: 10;
    flex-shrink: 0;

    .exam-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
    }

    .info-card {
        background-color: $system-gray7;
        border-radius: 8px;
        padding: 12px 16px;
        border: 1px solid $system-gray6;

        .stats-bar {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 10px;

            .progress-info {
                font-size: 20px;
                font-weight: 600;
                color: #262626;
                white-space: nowrap;
                display: flex;
                align-items: baseline;
                gap: 2px;

                .separator,
                .total-questions {
                    font-size: 16px;
                    font-weight: normal;
                    color: #595959;
                }
            }

            .progress-bar {
                flex-grow: 1;
                height: 6px;
                background: #f0f0f0;
                border-radius: 3px;
                overflow: hidden;

                .progress-fill {
                    height: 100%;
                    background: #1890ff;
                    border-radius: 3px;
                    transition: width 0.3s ease;
                }
            }

            .timer {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                white-space: nowrap;

                i {
                    font-size: 14px;
                }
            }
        }

        .question-stats {
            font-size: 14px;
            color: #8c8c8c;
            text-align: left;
            white-space: pre-wrap;
            line-height: 1.5;
            font-weight: 500;
        }
    }
}

.quiz-content {
    flex: 1;
    overflow: hidden;

    .quiz-swiper {
        height: 100%;

        .question-slide {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow-y: auto;

            ::-webkit-scrollbar {
                display: none;
            }
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    }
}

.question-card {
    padding: 20px;
    padding-top: 0;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    margin-bottom: 12px;

    .question-index-badge {
        font-size: 16px;
        font-weight: 500;
        color: #595959;
    }
    .question-type-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid;

        &.badge-single {
            border-color: #91d5ff;
            color: #1890ff;
            background-color: #e6f7ff;
        }

        &.badge-multi {
            border-color: #d3adf7;
            color: #722ed1;
            background-color: #f9f0ff;
        }

        &.badge-judge {
            border-color: #b7eb8f;
            color: #52c41a;
            background-color: #f6ffed;
        }
    }
}

.question-content {
    margin-bottom: 24px;

    .question-text {
        font-size: 16px;
        line-height: 1.7;
        color: #000;
        font-weight: 600;
    }
}

.options-section {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .option-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 14px;
        background: $system-gray7;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        position: relative;

        &.selected {
            // 加点透明度
            background-color: rgba(0, 127, 255, 0.12);
            border-color: #007fff;
        }

        &.disabled {
            cursor: not-allowed;
        }

        // 正确答案样式
        &.correct-answer {
            background-color: #10b981 !important;
            border-color: #059669 !important;
            color: white !important;

            .option-key {
                color: white !important;
            }

            .option-text {
                color: white !important;
            }
        }

        // 错误答案样式
        &.wrong-answer {
            background-color: $system-red !important;
            border-color: $system-red !important;
            color: white !important;

            .option-key {
                color: white !important;
            }

            .option-text {
                color: white !important;
            }
        }

        // 你选择的标签
        .user-choice-tag {
            position: absolute;
            top: -8px;
            right: 8px;
            background: $system-blue;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1;
        }

        .option-key {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            font-weight: 600;
            font-size: 14px;
            color: #000;
            transition: all 0.2s ease;
        }

        .option-text {
            color: #000;
            font-weight: 600;
            line-height: 1.6;
        }
    }

    .judge-options {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .judge-option {
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 16px;
            gap: 8px;

            &.selected {
                .judge-icon {
                    color: #1890ff;
                }
            }

            // 正确答案样式
            &.correct-answer {
                background-color: $system-green !important;
                border-color: $system-green !important;
                color: white !important;

                .judge-icon {
                    color: white !important;
                }

                .option-text {
                    color: white !important;
                }
            }

            // 错误答案样式
            &.wrong-answer {
                background-color: $system-red !important;
                border-color: $system-red !important;
                color: white !important;

                .judge-icon {
                    color: white !important;
                }

                .option-text {
                    color: white !important;
                }
            }

            .judge-icon {
                font-size: 24px;
                color: #8c8c8c;
                transition: color 0.2s ease;
            }

            .option-text {
                font-size: 16px;
                font-weight: 500;
            }
        }
    }
}

.quiz-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    z-index: 100;

    .footer-button {
        border: none;
        background-color: transparent;
        font-size: 16px;
        font-weight: 500;
        border-radius: 4px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .next-btn {
        flex-grow: 1;
        background-image: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        color: #fff;

        &:disabled {
            background: #d9d9d9;
            color: #a6a6a6;
            cursor: not-allowed;
            box-shadow: none;
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .quiz-header {
        padding: 12px 16px;
        .header-top .exam-title {
            font-size: 16px;
        }
    }

    .question-card {
        padding: 16px;
        padding-top: 0;
    }

    .question-content .question-text {
        font-size: 15px;
    }

    .options-section .option-item {
        padding: 12px;
        .option-text {
            font-size: 14px;
        }
    }
}
</style>
