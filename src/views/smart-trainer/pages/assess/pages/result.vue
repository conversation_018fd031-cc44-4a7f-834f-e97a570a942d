<template>
    <div class="result-page">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载考试结果...</p>
        </div>

        <!-- 结果不存在 -->
        <div v-else-if="!resultData" class="not-found">
            <i class="pi pi-exclamation-triangle"></i>
            <h3>结果不存在</h3>
            <p>未找到考试结果，请重新参加考试</p>
            <button class="back-btn" @click="goToList">
                <i class="pi pi-arrow-left"></i>
                <span>返回列表</span>
            </button>
        </div>

        <!-- 考试结果内容 -->
        <div v-else class="quiz-result-container">
            <div class="result-card">
                <img :src="trophyImage" alt="Trophy" class="trophy-img" />
                <p class="congrats-msg" :class="{ failed: !isPassed }">{{ getResultMessage() }}</p>

                <div class="score-display">
                    <p class="score-label">考试得分</p>
                    <p class="score-value" :class="{ failed: !isPassed }">
                        {{ Math.round(resultData.accuracy) }}
                    </p>
                </div>

                <div class="exam-info-area">
                    <p class="exam-title">{{ resultData.examData.name }}</p>
                    <div class="exam-details-card">
                        <div class="exam-details">
                            <div class="detail-item">
                                <span class="label">题目数量</span>
                                <span class="value">{{ resultData.totalCount }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">答题时长</span>
                                <span class="value"
                                    >{{ resultData.examData.duration_minutes || 45 }}分钟</span
                                >
                            </div>
                            <div class="detail-item">
                                <span class="label">总分</span>
                                <span class="value">100分</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">及格</span>
                                <span class="value"
                                    >{{ resultData.examData.passing_score || 60 }}分</span
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 答题详情筛选 -->
            <!--
            <div class="filter-section">
                <h3 class="filter-title">答题详情</h3>
                <div class="filter-buttons">
                    <button
                        class="filter-btn"
                        :class="{ active: activeFilter === 'all' }"
                        @click="activeFilter = 'all'"
                    >
                        全部 ({{ resultData.results.length }})
                    </button>
                    <button
                        class="filter-btn correct"
                        :class="{ active: activeFilter === 'correct' }"
                        @click="activeFilter = 'correct'"
                    >
                        正确 ({{ correctResults.length }})
                    </button>
                    <button
                        class="filter-btn incorrect"
                        :class="{ active: activeFilter === 'incorrect' }"
                        @click="activeFilter = 'incorrect'"
                    >
                        错误 ({{ incorrectResults.length }})
                    </button>
                </div>
            </div>
            -->

            <!-- 答题结果列表 -->
            <!--
            <div class="results-section">
                <div class="results-list">
                    <div
                        v-for="result in filteredResults"
                        :key="result.questionId"
                        class="result-item"
                        :class="{ correct: result.isCorrect, incorrect: !result.isCorrect }"
                    >
                        <div class="result-indicator">
                            <i class="pi pi-check" v-if="result.isCorrect"></i>
                            <i class="pi pi-times" v-else></i>
                        </div>
                        <div class="result-content">
                            <div class="question-text">
                                <span class="question-number"
                                    >{{ getOriginalIndex(result.questionId) + 1 }}.</span
                                >
                                {{ result.question }}
                            </div>
                            <div class="answer-info">
                                <div class="answer-row">
                                    <span class="answer-label">您的答案：</span>
                                    <span
                                        class="answer-value user-answer"
                                        :class="{ incorrect: !result.isCorrect }"
                                    >
                                        {{ formatAnswer(result.userAnswer) }}
                                    </span>
                                </div>
                                <div class="answer-row" v-if="!result.isCorrect">
                                    <span class="answer-label">正确答案：</span>
                                    <span class="answer-value correct-answer">
                                        {{ formatAnswer(result.correctAnswer) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            -->

            <!-- 操作按钮 -->
            <div class="action-section">
                <button class="action-btn secondary" @click="goToList">
                    <i class="pi pi-list"></i>
                    <span>返回列表</span>
                </button>
                <button class="action-btn primary" @click="restartExam">
                    <i class="pi pi-refresh"></i>
                    <span>重新考试</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const resultData = ref(null);
const activeFilter = ref('all');

// 奖杯图片URL - 请根据实际情况替换
const trophyImages = {
    passed: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/assess/pass.png',
    failed: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/assess/notpass.png' // 请提供不合格时的图片
};

// 计算属性
const trophyImage = computed(() => {
    return isPassed.value ? trophyImages.passed : trophyImages.failed;
});

const isPassed = computed(() => {
    if (!resultData.value) {
        return false;
    }
    // 使用动态及格分数（如果提供），否则默认为60
    const passingScore = resultData.value.examData?.passing_score || 60;
    return resultData.value.accuracy >= passingScore;
});

const correctResults = computed(() => {
    return resultData.value?.results.filter(r => r.isCorrect) || [];
});

const incorrectResults = computed(() => {
    return resultData.value?.results.filter(r => !r.isCorrect) || [];
});

const filteredResults = computed(() => {
    if (!resultData.value) {
        return [];
    }

    switch (activeFilter.value) {
        case 'correct':
            return correctResults.value;
        case 'incorrect':
            return incorrectResults.value;
        default:
            return resultData.value.results;
    }
});

onMounted(() => {
    loadResultData();
});

// 方法
const loadResultData = async () => {
    try {
        loading.value = true;

        // 从 sessionStorage 获取结果数据
        const storedResult = sessionStorage.getItem('quiz-result');
        if (storedResult) {
            const data = JSON.parse(storedResult);
            // 验证考试ID是否匹配
            if (data.examId === route.params.examId) {
                resultData.value = data;
            }
        }

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 800));
    } catch (error) {
        console.error('加载考试结果失败:', error);
        resultData.value = null;
    } finally {
        loading.value = false;
    }
};

const getResultMessage = () => {
    if (!resultData.value) {
        return '';
    }
    return isPassed.value ? '恭喜通过考试，你太棒了！' : '很遗憾，本次未通过考试。';
};

const formatAnswer = answer => {
    if (answer === null || answer === undefined) {
        return '未选择';
    }

    if (typeof answer === 'boolean') {
        return answer ? '正确' : '错误';
    }

    if (Array.isArray(answer)) {
        return answer.length > 0 ? answer.join(', ') : '未选择';
    }

    return answer;
};

const getOriginalIndex = questionId => {
    if (!resultData.value?.examData?.questions) {
        return 0;
    }
    return resultData.value.examData.questions.findIndex(q => q.id === questionId);
};

const goToList = () => {
    // 清除结果数据
    sessionStorage.removeItem('quiz-result');
    router.push('/smart-trainer/assess/list');
};

const restartExam = () => {
    if (!resultData.value) {
        return;
    }

    // 清除结果数据
    sessionStorage.removeItem('quiz-result');

    // 跳转到考试详情页
    router.push(`/smart-trainer/assess/detail/${resultData.value.examId}`);
};
</script>

<style lang="scss" scoped>
.result-page {
    width: 100%;
    height: 100%;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #4299e1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;
    text-align: center;

    i {
        font-size: 64px;
        margin-bottom: 20px;
        color: #ffc107;
    }

    h3 {
        font-size: 24px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.5;
    }

    .back-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #4299e1;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        i {
            font-size: 16px;
        }
    }
}

.quiz-result-container {
    padding: 20px;
    padding-bottom: 140px; // Space for fixed action buttons
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.result-card {
    background-color: $system-gray6;
    border-radius: 16px;
    padding: 120px 24px 24px;
    margin-bottom: 24px;
    position: relative;
    margin-top: 120px;
}

.trophy-img {
    width: 230px;
    height: auto;
    position: absolute;
    top: -70px;
    left: 50%;
    transform: translateX(-50%);
}

.congrats-msg {
    font-size: 18px;
    font-weight: 500;
    color: #000;
    margin-bottom: 20px;

    &.failed {
        color: $system-red;
    }
}

.score-display {
    margin-bottom: 18px;

    .score-label {
        font-size: 16px;
        color: #000;
        font-weight: 500;
        margin-bottom: 4px;
    }
    .score-value {
        font-size: 52px;
        font-weight: bold;
        color: $system-blue;
        line-height: 1;

        &.failed {
            color: $system-red;
        }
    }
}

.exam-info-area {
    .exam-title {
        margin-top: 24px;
        margin-bottom: 10px;
        font-size: 16px;
        text-align: center;
        color: #000;
    }

    .exam-details-card {
        background-color: #fafafa;
        padding: 24px;
        border-radius: 12px;

        .exam-details {
            display: grid;
            grid-template-rows: auto auto;
            grid-auto-flow: column;
            grid-row-gap: 20px;
            grid-column-gap: 16px;

            .detail-item {
                text-align: left;
                font-size: 14px;
                position: relative;
                display: flex;
                align-items: baseline;
                justify-content: flex-start;
                gap: 8px;

                &:nth-child(1),
                &:nth-child(2) {
                    &::after {
                        content: '';
                        position: absolute;
                        right: -8px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 1px;
                        height: 32px;
                        background-color: #eef0f2;
                    }
                }

                .label {
                    color: #6c757d;
                }
                .value {
                    font-weight: 600;
                    color: #000;
                    font-size: 15px;
                }
            }
        }
    }
}

/*
.quiz-result {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.result-header {
    text-align: center;
    background: white;
    padding: 32px 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;

    .completion-icon {
        margin-bottom: 16px;

        i {
            font-size: 64px;

            &.pi-check-circle {
                color: #28a745;
            }

            &.pi-exclamation-triangle {
                color: #ffc107;
            }

            &.pi-times-circle {
                color: #dc3545;
            }
        }
    }

    .result-title {
        font-size: 20px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 4px;
    }

    .result-subtitle {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
        margin-bottom: 8px;
    }

    .result-message {
        font-size: 16px;
        color: #6c757d;
        line-height: 1.5;
    }
}

.score-section {
    margin-bottom: 24px;

    .score-card {
        background: white;
        padding: 32px 24px;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 32px;

        .score-circle {
            .circle-progress {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(
                    #4299e1 0deg,
                    #4299e1 calc(var(--progress) * 3.6deg),
                    #e9ecef calc(var(--progress) * 3.6deg),
                    #e9ecef 360deg
                );
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                .circle-inner {
                    width: 90px;
                    height: 90px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .score-number {
                        font-size: 28px;
                        font-weight: 700;
                        color: #212529;
                        line-height: 1;
                    }

                    .score-unit {
                        font-size: 14px;
                        color: #6c757d;
                        margin-top: 2px;
                    }
                }
            }
        }

        .score-details {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;

            .score-item {
                text-align: center;

                .score-label {
                    display: block;
                    font-size: 14px;
                    color: #6c757d;
                    margin-bottom: 8px;
                }

                .score-value {
                    display: block;
                    font-size: 24px;
                    font-weight: 700;

                    &.correct {
                        color: #28a745;
                    }

                    &.incorrect {
                        color: #dc3545;
                    }

                    &.total {
                        color: #4299e1;
                    }
                }
            }
        }
    }
}

.filter-section {
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;

    .filter-title {
        font-size: 18px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 16px;
    }

    .filter-buttons {
        display: flex;
        gap: 12px;

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            background: white;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: #4299e1;
                color: #4299e1;
            }

            &.active {
                background: #4299e1;
                border-color: #4299e1;
                color: white;
            }

            &.correct.active {
                background: #28a745;
                border-color: #28a745;
            }

            &.incorrect.active {
                background: #dc3545;
                border-color: #dc3545;
            }
        }
    }
}

.results-section {
    margin-bottom: 24px;

    .results-list {
        .result-item {
            display: flex;
            gap: 16px;
            padding: 20px;
            margin-bottom: 12px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border-left: 4px solid transparent;

            &.correct {
                border-left-color: #28a745;
            }

            &.incorrect {
                border-left-color: #dc3545;
            }

            .result-indicator {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 4px;

                i {
                    font-size: 16px;
                    color: white;
                }
            }

            &.correct .result-indicator {
                background: #28a745;
            }

            &.incorrect .result-indicator {
                background: #dc3545;
            }

            .result-content {
                flex: 1;

                .question-text {
                    font-size: 15px;
                    line-height: 1.5;
                    color: #212529;
                    margin-bottom: 12px;

                    .question-number {
                        font-weight: 600;
                        color: #4299e1;
                        margin-right: 8px;
                    }
                }

                .answer-info {
                    .answer-row {
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;
                        margin-bottom: 6px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .answer-label {
                            font-size: 13px;
                            color: #6c757d;
                            font-weight: 500;
                            min-width: 80px;
                        }

                        .answer-value {
                            font-size: 13px;
                            font-weight: 500;

                            &.user-answer.incorrect {
                                color: #dc3545;
                            }

                            &.correct-answer {
                                color: #28a745;
                            }
                        }
                    }
                }
            }
        }
    }
}
*/

.action-section {
    position: fixed;
    bottom: 60px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    z-index: 100;
    gap: 16px;

    .action-btn {
        border: none;
        background-color: transparent;
        font-size: 16px;
        font-weight: 500;
        border-radius: 4px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
        gap: 8px;
        flex: 1;

        i {
            font-size: 16px;
        }

        &.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;

            &:hover {
                background: #e9ecef;
                color: #495057;
            }
        }

        &.primary {
            background-image: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
            color: #fff;

            &:hover:not(:disabled) {
                opacity: 0.9;
            }

            &:disabled {
                background: #d9d9d9;
                color: #a6a6a6;
                cursor: not-allowed;
                box-shadow: none;
            }
        }
    }
}

@media (max-width: 768px) {
    .quiz-result-container {
        padding: 16px;
    }

    .result-card {
        padding: 100px 24px 24px;
        margin-top: 120px;
    }

    .congrats-msg {
        font-size: 16px;
    }

    .score-display .score-value {
        font-size: 44px;
    }

    .exam-info-area {
        .exam-title {
            font-size: 14px;
        }
        .exam-details-card {
            padding: 16px;
            .exam-details {
                grid-template-columns: 1fr;
                grid-row-gap: 12px;
            }
        }
    }

    .action-section {
        padding: 12px 16px;
        gap: 12px;

        .action-btn {
            justify-content: center;
        }
    }
}

@media (max-width: 480px) {
    .exam-info-area .exam-details-card .exam-details {
        grid-template-columns: 1fr;
    }
}
</style>
