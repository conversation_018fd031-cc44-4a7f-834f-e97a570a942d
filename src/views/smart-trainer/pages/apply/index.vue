<template>
    <div class="apply-container">
        <div class="apply-header">
            <h1>应用模块</h1>
            <p>实际应用与价值创造</p>
        </div>
        <div class="router-view-container">
            <RouterView />
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('应用模块已加载');
});
</script>

<style lang="scss" scoped>
.apply-container {
    height: 100vh;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
}

.apply-header {
    padding: 20px 16px;
    background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
    color: white;
    text-align: center;

    h1 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        opacity: 0.9;
    }
}

.router-view-container {
    flex: 1;
    overflow-y: auto;
}
</style>
