<template>
    <div class="home-container">
        <!-- 高科技动态背景 -->
        <AIBackground :particle-count="20" :network-line-count="6" />

        <!-- 改进的头部区域 -->
        <div class="header-section">
            <div class="header-content">
                <div class="title-group">
                    <h1 class="title">AI智能训练</h1>
                    <div class="title-decoration">
                        <span class="ai-badge">AI Powered</span>
                    </div>
                </div>
                <p class="subtitle">
                    <span class="tech-word">智能评估</span> ·
                    <span class="tech-word">场景实训</span> ·
                    <span class="tech-word">能力提升</span>
                </p>
            </div>
            <div class="header-decoration">
                <div class="ai-tech-circle">
                    <div class="circle-inner"></div>
                    <div class="circle-outer"></div>
                </div>
                <img src="https://fs.autohome.com.cn/dealer_views/dealer_front/public/logo/logo464.png"
                    alt="AI Training" class="study-icon" />
            </div>
        </div>

        <div class="content-section">
            <!-- 加载状态 - 改为 LoadingState -->
            <LoadingState v-if="initialLoading" :text="'正在初始化AI训练环境'" />

            <!-- 加载完成后的内容 -->
            <div v-else>
                <!-- 推荐练习作为第一个特殊分类 -->
                <FadeInSection v-if="recommendedExams.length > 0" :delay="0">
                    <div class="category-container featured-container" data-category-id="recommended">
                        <div class="category-header">
                            <div class="title-group">
                                <h2 class="category-title">推荐练习</h2>
                                <div class="title-tag" v-if="recommendedExams.length > 0">
                                    <i class="pi pi-star-fill"></i>
                                    <span>{{ recommendedExams.length }}个精选练习</span>
                                </div>
                            </div>
                            <div class="category-actions">
                                <span class="action-tip">AI推荐</span>
                            </div>
                        </div>

                        <div class="swiper-container recommended-swiper">
                            <!-- 左侧箭头 -->
                            <div v-if="recommendedExams.length > 2" class="swiper-nav-button swiper-nav-prev" :class="{
                                'swiper-nav-disabled': swiperNavState.recommended?.isBeginning
                            }" @click.stop="slidePrev('recommended')">
                                <i class="pi pi-chevron-left"></i>
                            </div>

                            <Swiper v-bind="swiperOptions" class="course-swiper"
                                @swiper="swiper => onSwiperInit(swiper, 'recommended')"
                                @slideChange="swiper => onSlideChange(swiper, 'recommended')">
                                <SwiperSlide v-for="exam in recommendedExams" :key="'rec-' + exam.id">
                                    <div class="course-card recommended-card" @click="goToExamDetail(exam.id)"
                                        :class="getExamColorClass(exam.id)">
                                        <div class="card-tech-elements">
                                            <div class="tech-circle"></div>
                                            <div class="tech-lines">
                                                <div v-for="n in 3" :key="`tech-line-${n}`" class="tech-line"></div>
                                            </div>
                                        </div>
                                        <div class="course-content">
                                            <div class="card-header">
                                                <span v-if="exam.highlightType" class="highlight-tag" :class="[
                                                    getHighlightTypeClass(exam.highlightType)
                                                ]">
                                                    {{ getHighlightTypeText(exam.highlightType) }}
                                                </span>
                                                <div class="title-row">
                                                    <h3 class="course-title">
                                                        {{ exam.name }}
                                                    </h3>
                                                </div>
                                            </div>
                                            <div class="featured-meta-row">
                                                <div class="featured-meta">
                                                    <span class="meta-item">
                                                        <i class="pi pi-users"></i>
                                                        {{ getRandomEnrollment() }}人学习
                                                    </span>
                                                    <span class="meta-item">
                                                        <i class="pi pi-star"></i>
                                                        {{ getRandomRating() }}分
                                                    </span>
                                                </div>
                                                <Button class="start-btn" @click.stop="goToExamDetail(exam.id)">
                                                    {{ exam.progress ? '继续学习' : '开始学习' }}
                                                    <i class="pi pi-arrow-right"></i>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </SwiperSlide>
                            </Swiper>

                            <!-- 右侧箭头 -->
                            <div v-if="recommendedExams.length > 2" class="swiper-nav-button swiper-nav-next" :class="{
                                'swiper-nav-disabled': swiperNavState.recommended?.isEnd
                            }" @click.stop="slideNext('recommended')">
                                <i class="pi pi-chevron-right"></i>
                            </div>
                        </div>
                    </div>
                </FadeInSection>

                <!-- 常规分类列表 -->
                <FadeInSection v-for="(category, index) in examCategoryList" :key="category.id"
                    :delay="(index + 1) * 50">
                    <div class="category-container" :data-category-id="category.id">
                        <div class="category-header">
                            <div class="title-group">
                                <h2 class="category-title">{{ category.name }}</h2>
                                <div class="title-tag" v-if="getExamsById(category.id).length > 0">
                                    <i class="pi pi-book"></i>
                                    <span>{{ getExamsById(category.id).length }}个练习</span>
                                </div>
                            </div>
                        </div>

                        <div class="swiper-container">
                            <!-- 左侧箭头 -->
                            <div v-if="getExamsById(category.id).length > 2" class="swiper-nav-button swiper-nav-prev"
                                :class="{
                                    'swiper-nav-disabled': swiperNavState[category.id]?.isBeginning
                                }" @click.stop="slidePrev(category.id)">
                                <i class="pi pi-chevron-left"></i>
                            </div>

                            <Swiper v-bind="swiperOptions" class="course-swiper"
                                @swiper="swiper => onSwiperInit(swiper, category.id)"
                                @slideChange="swiper => onSlideChange(swiper, category.id)">
                                <template v-if="getExamsById(category.id).length > 0">
                                    <SwiperSlide v-for="exam in getExamsById(category.id)" :key="exam.id">
                                        <div class="course-card" @click="goToExamDetail(exam.id)"
                                            :class="getExamColorClass(exam.id)">
                                            <div class="card-tech-elements">
                                                <div class="tech-circle"></div>
                                                <div class="tech-lines">
                                                    <div v-for="n in 3" :key="`tech-line-${n}`" class="tech-line"></div>
                                                </div>
                                            </div>
                                            <div class="course-content">
                                                <div class="card-header">
                                                    <span v-if="exam.highlightType" class="highlight-tag" :class="[
                                                        getHighlightTypeClass(
                                                            exam.highlightType
                                                        )
                                                    ]">
                                                        {{
                                                            getHighlightTypeText(exam.highlightType)
                                                        }}
                                                    </span>
                                                    <div class="title-row">
                                                        <h3 class="course-title">
                                                            {{ exam.name }}
                                                        </h3>
                                                    </div>
                                                </div>
                                                <Button class="start-btn" @click.stop="goToExamDetail(exam.id)">
                                                    {{ EXAM_TEXT.BUTTONS.START_TRAINING }}
                                                    <i class="pi pi-arrow-right"></i>
                                                </Button>
                                            </div>
                                        </div>
                                    </SwiperSlide>
                                </template>
                                <template v-else>
                                    <div class="empty-state" :class="`empty-state-${index % 8}`">
                                        <div class="empty-icon">
                                            <i class="pi pi-inbox"></i>
                                            <div class="empty-circle"></div>
                                        </div>
                                        <p>AI练习准备中</p>
                                        <span class="empty-hint">敬请期待</span>
                                    </div>
                                </template>
                            </Swiper>

                            <!-- 右侧箭头 -->
                            <div v-if="getExamsById(category.id).length > 2" class="swiper-nav-button swiper-nav-next"
                                :class="{
                                    'swiper-nav-disabled': swiperNavState[category.id]?.isEnd
                                }" @click.stop="slideNext(category.id)">
                                <i class="pi pi-chevron-right"></i>
                            </div>
                        </div>
                    </div>
                </FadeInSection>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { isDingTalk } from '@/utils/index';
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useRouter } from 'vue-router';
import useTrainerStore from '@/stores/trainer';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import FadeInSection from '@/components/common/FadeInSection.vue';
import AIBackground from '@/components/common/AIBackground.vue';
import LoadingState from '@/components/common/LoadingState.vue';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';

// 导入必要的 swiper 样式
import 'swiper/css';
import 'swiper/css/navigation';

const router = useRouter();
const trainerStore = useTrainerStore();

// 存储所有 Swiper 实例
const swiperInstances = ref({});

// 存储 Swiper 导航状态
const swiperNavState = ref({});

// 加载引用
const loadingRef = ref(null);

// 统一 Swiper 配置
const swiperOptions = {
    modules: [Navigation],
    slidesPerView: 2, // 固定显示2个滑块
    spaceBetween: 16, // 滑块之间的间距
    touchStartPreventDefault: false, // 优化触摸体验
    slidesPerGroup: 2, // 每次滑动2个
    width: null, // 让 Swiper 自动计算宽度
    grid: {
        rows: 1, // 单行显示
        fill: 'row' // 按行填充
    },
    breakpoints: {
        // 响应式配置
        320: {
            slidesPerView: 2,
            spaceBetween: 16
        },
        768: {
            slidesPerView: 3,
            spaceBetween: 20
        },
        1024: {
            slidesPerView: 4,
            spaceBetween: 24
        }
    }
};

// 获取推荐练习的逻辑
const recommendedExams = computed(() => {
    // 从所有分类中获取推荐类型的考试
    const allExams = examCategoryList.value.flatMap(category =>
        trainerStore.getExamListByCategoryId(category.id)
    );

    // 筛选出 highlightType 为 1 (推荐) 的考试
    return allExams.filter(exam => exam.highlightType === 1);
});

// 获取高亮类型标签文本函数
function getHighlightTypeText(type) {
    switch (type) {
        case 1:
            return 'AI';
        case 2:
            return 'NEW';
        case 3:
            return 'HOT';
        default:
            return 'HOT';
    }
}

// 获取高亮类型标签样式类
function getHighlightTypeClass(type) {
    switch (type) {
        case 1:
            return 'highlight-recommend';
        case 2:
            return 'highlight-new';
        case 3:
            return 'highlight-hot';
        default:
            return 'highlight-hot';
    }
}

// 获取考试的颜色类函数
function getExamColorClass(examId) {
    // 先查找考试所属的分类ID
    const exam = findExamById(examId);
    if (!exam) {
        return 'color-0';
    } // 默认颜色

    // 使用分类ID来确定颜色
    const categoryId = exam.examCategoryId;
    if (!categoryId) {
        return 'color-0';
    } // 默认颜色

    // 将分类ID转为字符串，然后计算简单的哈希值
    const hash = String(categoryId)
        .split('')
        .reduce((acc, char) => {
            return (acc * 31 + char.charCodeAt(0)) & 0xffffffff;
        }, 0);

    // 使用哈希值对8取模，得到0-7之间的固定值
    // 然后映射到优先顺序的颜色索引
    const colorMap = [0, 2, 1, 5, 3, 6, 4, 7]; // 优化后的颜色顺序
    const colorIndex = colorMap[Math.abs(hash % 8)];
    return `color-${colorIndex}`;
}

// 辅助函数，根据ID查找考试
function findExamById(examId) {
    // 从所有分类中查找考试
    const allExams = examCategoryList.value.flatMap(category =>
        trainerStore.getExamListByCategoryId(category.id)
    );

    return allExams.find(exam => exam.id === examId);
}

// 统一初始化 Swiper
function onSwiperInit(swiper, id) {
    // 存储 Swiper 实例
    swiperInstances.value[id] = swiper;

    // 初始化导航状态
    swiperNavState.value[id] = {
        isBeginning: true,
        isEnd: swiper.isEnd
    };

    // 使用 nextTick 确保 DOM 已更新，然后再次检查以确保准确性
    nextTick(() => {
        // 更新 Swiper 实例
        swiper.update();

        // 更新导航状态
        swiperNavState.value[id] = {
            isBeginning: swiper.isBeginning,
            isEnd: swiper.isEnd
        };
    });

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        nextTick(() => {
            if (swiperInstances.value[id]) {
                const swiperInstance = swiperInstances.value[id];
                swiperInstance.update();

                // 更新导航状态
                swiperNavState.value[id] = {
                    isBeginning: swiperInstance.isBeginning,
                    isEnd: swiperInstance.isEnd
                };
            }
        });
    });
}

// 滑动变化时更新导航状态
function onSlideChange(swiper, id) {
    swiperNavState.value[id] = {
        isBeginning: swiper.isBeginning,
        isEnd: swiper.isEnd
    };
}

// 滑动到下一个
function slideNext(id) {
    const swiper = swiperInstances.value[id];
    if (swiper && !swiperNavState.value[id]?.isEnd) {
        swiper.slideNext();
    }
}

// 滑动到上一个
function slidePrev(id) {
    const swiper = swiperInstances.value[id];
    if (swiper && !swiperNavState.value[id]?.isBeginning) {
        swiper.slidePrev();
    }
}

// 考试分类列表
const examCategoryList = computed(() => {
    return trainerStore.examCategoryList || [];
});

// 根据类型ID获取考试列表
function getExamsById(categoryId) {
    return trainerStore.getExamListByCategoryId(categoryId);
}

function goToExamDetail(examId) {
    // 防护性检查：确保 examId 存在且有效
    if (!examId || examId === 'undefined' || examId === 'null') {
        console.error('examId 参数无效，无法跳转到考试详情页:', examId);
        return;
    }

    router.push({
        path: '/smart-trainer/exam/detail',
        query: {
            examId
        }
    });
}

const fetchExamCategoryList = async () => {
    try {
        // 直接使用 trainerStore 中的方法获取考试分类列表
        const res = await trainerStore.getExamCategoryList();
        if (!res) {
            throw new Error('获取考试分类列表返回空数据');
        }

        return true;
    } catch (error) {
        console.error('获取考试分类列表失败:', error);
        console.error('错误详情:', error.stack || error);
        return false;
    }
};

const initialLoading = ref(true);

// 初始化数据函数
const initData = async () => {
    try {
        await fetchExamCategoryList();

        // 强制等待至少1.5秒，确保动画效果流畅
        await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
        console.error('初始化数据失败:', error);
    } finally {
        // 直接设置 loading 状态为 false
        initialLoading.value = false;
    }
};

// 设置导航标题
if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '智能训练'
    });
}

// 随机数据生成函数（模拟数据）
function getRandomEnrollment() {
    return 100 + Math.floor(Math.random() * 900);
}

// 随机评分生成函数（8-10分，支持小数）
function getRandomRating() {
    // 生成8到10之间的随机数，保留一位小数
    return (Math.random() * 2 + 8).toFixed(1);
}

onMounted(async () => {
    try {
        await initData();
    } catch (error) {
        console.error('页面初始化失败:', error);
        initialLoading.value = false;
    }
});

onBeforeUnmount(() => {
    // 移除不必要的 loadingRef 相关代码
});
</script>

<style lang="scss" scoped>
.home-container {
    height: 100%;
    background: linear-gradient(180deg,
            rgba($system-background-primary, 0.9) 0%,
            $system-background-secondary 100%);

    overflow: hidden;
    font-weight: 500;
    position: relative;
    display: flex;
    flex-direction: column;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top right, rgba($system-blue, 0.08), transparent 70%);
        z-index: 0;
        pointer-events: none;
    }
}

// 合并所有头部相关样式
.header-section {
    padding: 20px 24px 36px;
    background: transparent;
    position: relative;
    overflow: hidden;
    color: $label-primary;
    margin-bottom: 0;
    z-index: 1;
    flex-shrink: 0;


    .header-content {
        position: relative;
        z-index: 2;
        max-width: 1200px;
        margin: 0 auto;

        .title-group {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .title {
                font-size: 24px;
                font-weight: 600;
                margin: 0;
                background: linear-gradient(135deg,
                        $system-blue 0%,
                        $system-indigo 50%,
                        $system-teal 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                letter-spacing: 0.02em;
            }

            .title-decoration {
                margin-left: 12px;
                display: flex;
                align-items: center;

                .ai-badge {
                    font-size: 10px;
                    font-weight: 600;
                    padding: 3px 8px;
                    border-radius: 12px;
                    background: linear-gradient(135deg,
                            rgba($system-blue, 0.85) 0%,
                            rgba($system-indigo, 0.75) 100%);
                    color: white;
                    letter-spacing: 0.02em;
                    box-shadow: 0 2px 8px rgba($system-blue, 0.3);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    position: relative;
                    overflow: hidden;

                    &::after {
                        content: '';
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: linear-gradient(to right,
                                rgba(255, 255, 255, 0) 0%,
                                rgba(255, 255, 255, 0.2) 50%,
                                rgba(255, 255, 255, 0) 100%);
                        transform: rotate(30deg);
                        animation: shimmer 3s infinite;
                    }
                }
            }
        }

        .subtitle {
            font-size: 13px;
            font-weight: 500;
            background: linear-gradient(to right,
                    rgba($system-blue, 0.85),
                    rgba($system-indigo, 0.75));
            -webkit-background-clip: text;
            margin: 0;
            letter-spacing: 0.01em;

            .tech-word {
                position: relative;
                display: inline-block;
                margin: 0 2px;
                color: $system-blue;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -2px;
                    left: 0;
                    width: 100%;
                    height: 1px;
                    background: linear-gradient(to right, transparent, $system-blue, transparent);
                    opacity: 0.7;
                }
            }
        }
    }

    .header-decoration {
        position: absolute;
        right: 20px;
        top: 28px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .ai-tech-circle {
            position: absolute;
            width: 72px;
            height: 72px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.7;

            .circle-inner {
                width: 46px;
                height: 46px;
                border: 2px solid rgba($system-blue, 0.4);
                border-radius: 50%;
                position: relative;
                animation: rotate 15s linear infinite;

                &::before,
                &::after {
                    content: '';
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: $system-blue;
                    border-radius: 50%;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    box-shadow: 0 0 10px rgba($system-blue, 0.8);
                }

                &::before {
                    transform: translate(-50%, -50%) translate(20px, 0);
                    animation: pulse 3s ease-in-out infinite;
                }

                &::after {
                    transform: translate(-50%, -50%) translate(-20px, 0);
                    animation: pulse 3s ease-in-out infinite 1.5s;
                }
            }

            .circle-outer {
                position: absolute;
                width: 68px;
                height: 68px;
                border: 1px dashed rgba($system-indigo, 0.5);
                border-radius: 50%;
                animation: rotate 20s linear infinite reverse;
            }
        }

        .study-icon {
            width: 52px;
            filter: drop-shadow(0 8px 16px rgba($system-blue, 0.12));
            z-index: 2;
        }
    }
}

// 内容区域样式
.content-section {
    margin: 0 auto;
    position: relative;
    z-index: 1;
    flex: 1;
    width: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 24px;
        background: linear-gradient(to bottom,
                rgba($system-background-secondary, 0.5),
                transparent);
        z-index: -1;
    }
}

// 分类容器样式
.category-container {
    margin-bottom: 16px;
    position: relative;
    animation: slideIn 0.3s ease;
    padding: 0 4px;

    &.featured-container {
        margin-top: 8px;
        margin-bottom: 20px;

        .swiper-container {
            margin-bottom: 8px;
        }
    }
}

// 分类头部样式
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 14px 0 14px;
    margin-bottom: 10px;

    .title-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .category-title {
            font-size: 16px;
            font-weight: 700;
            color: $label-primary;
            padding: 0 0 3px 0;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 65%;
                height: 2px;
                background: linear-gradient(to right, $system-blue, rgba($system-blue, 0.4));
                border-radius: 1px;
                transition: width 0.3s ease;
            }
        }

        .title-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            background: rgba($system-blue, 0.1);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: 500;
            color: $system-blue;

            .pi {
                font-size: 10px;
            }
        }
    }

    .category-actions {
        display: flex;
        align-items: center;

        .action-tip {
            font-size: 12px;
            color: $system-blue;
            font-weight: 500;
            display: flex;
            align-items: center;
            padding: 3px 8px;
            border-radius: 12px;
            background: rgba($system-blue, 0.08);
            box-shadow: 0 1px 3px rgba($system-blue, 0.1);

            &::before {
                content: '';
                display: inline-block;
                width: 6px;
                height: 6px;
                background: $system-blue;
                border-radius: 50%;
                margin-right: 5px;
                animation: pulse 2s infinite;
            }
        }
    }
}

.category-header:hover .category-title::after {
    width: 100%;
}

// 统一 Swiper 容器样式
.swiper-container {
    position: relative;
    padding: 0 14px;

    // 使用 Swiper 的配置项控制滑块宽度，而不是直接在 CSS 中设置
    :deep(.swiper-slide) {
        // 移除固定宽度设置，改为由 Swiper 配置控制
        height: auto;
    }

    // 推荐部分的 Swiper 特殊样式
    &.recommended-swiper {
        margin-bottom: 8px;
    }

    // 导航按钮样式
    .swiper-nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba($system-background-primary, 0.95);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 20;
        transition: all 0.2s ease;
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.1);

        &:active {
            transform: translateY(-50%) scale(0.95);
        }

        .pi {
            font-size: 14px;
            color: $label-primary;
        }

        // 左侧导航按钮
        &.swiper-nav-prev {
            left: 5px;
            padding-right: 2px;
        }

        // 右侧导航按钮
        &.swiper-nav-next {
            right: 5px;
            padding-left: 2px;
        }

        // 禁用状态
        &.swiper-nav-disabled {
            opacity: 0;
            pointer-events: none;
        }

        // 添加阻止事件冒泡
        &::before {
            content: '';
            position: absolute;
            top: -10px;
            bottom: -10px;
            left: -10px;
            right: -10px;
            z-index: -1;
        }
    }
}

// 统一卡片基础样式
.course-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    height: 100%;
    min-height: 140px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);

    &:hover {
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);

        .tech-circle {
            transform: scale(1.05);
        }

        .tech-line {
            width: 100%;
        }
    }

    &:active {
        transform: scale(0.97);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png') center/cover;
        opacity: 0.06;
        mix-blend-mode: overlay;
    }

    // 卡片技术元素
    .card-tech-elements {
        position: absolute;
        top: 0;
        right: 0;
        width: 60px;
        height: 60px;
        z-index: 0;
        opacity: 0.7;

        .tech-circle {
            position: absolute;
            top: -30px;
            right: -30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 1px dashed rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease;
        }

        .tech-lines {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 30px;

            .tech-line {
                height: 1px;
                background: rgba(255, 255, 255, 0.3);
                margin-bottom: 4px;
                width: 50%;
                transition: width 0.3s ease;

                &:nth-child(1) {
                    width: 70%;
                }

                &:nth-child(3) {
                    width: 40%;
                }
            }
        }
    }

    // 推荐卡片特殊样式
    &.recommended-card {
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        min-height: 160px;

        .featured-meta-row {
            gap: 8px;
        }

        .start-btn {
            padding: 6px 12px;
            font-size: 12px;

            .pi {
                font-size: 10px;
            }
        }
    }

    .course-content {
        position: relative;
        min-height: 100px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        z-index: 1;
        height: 100%;
    }

    .card-header {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .course-title {
        font-size: 15px;
        font-weight: 600;
        color: $system-background-primary;
        margin: 0;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .start-btn {
        width: fit-content;
        background: rgba(255, 255, 255, 0.18);
        border: 1px solid rgba(255, 255, 255, 0.25);
        color: $system-background-primary;
        padding: 5px 10px;
        border-radius: 16px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;
        backdrop-filter: blur(2px);

        &:active {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(0.97);
        }

        .pi {
            font-size: 10px;
            transition: transform 0.2s ease;
        }

        &:hover .pi {
            transform: translateX(2px);
        }
    }

    // 颜色类合并 - 更加正式且美观的配色
    &.color-0 {
        background: linear-gradient(135deg,
                rgba($system-blue, 0.95) 0%,
                rgba($system-indigo, 0.85) 100%);
        box-shadow: 0 4px 10px rgba($system-blue, 0.12);
    }

    &.color-1 {
        background: linear-gradient(135deg,
                rgba($system-teal, 0.95) 0%,
                rgba($system-blue, 0.8) 100%);
        box-shadow: 0 4px 10px rgba($system-teal, 0.12);
    }

    &.color-2 {
        background: linear-gradient(135deg,
                rgba($system-green, 0.95) 0%,
                rgba($system-teal, 0.8) 100%);
        box-shadow: 0 4px 10px rgba($system-green, 0.12);
    }

    &.color-3 {
        background: linear-gradient(135deg,
                rgba($system-indigo, 0.95) 0%,
                rgba($system-purple, 0.8) 100%);
        box-shadow: 0 4px 10px rgba($system-indigo, 0.12);
    }

    &.color-4 {
        background: linear-gradient(135deg,
                rgba($system-orange, 0.95) 0%,
                rgba($system-yellow, 0.85) 100%);
        box-shadow: 0 4px 10px rgba($system-orange, 0.12);
    }

    &.color-5 {
        background: linear-gradient(135deg,
                rgba($system-purple, 0.95) 0%,
                rgba($system-pink, 0.8) 100%);
        box-shadow: 0 4px 10px rgba($system-purple, 0.12);
    }

    &.color-6 {
        background: linear-gradient(135deg,
                rgba($system-pink, 0.9) 0%,
                rgba($system-red, 0.8) 100%);
        box-shadow: 0 4px 10px rgba($system-pink, 0.12);
    }

    &.color-7 {
        background: linear-gradient(135deg,
                rgba($system-yellow, 0.95) 0%,
                rgba($system-orange, 0.85) 100%);
        box-shadow: 0 4px 10px rgba($system-yellow, 0.12);
    }
}

// 推荐练习元数据样式
.featured-meta-row {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-top: auto;
}

.featured-meta {
    display: flex;
    gap: 8px;

    .meta-item {
        display: flex;
        align-items: center;
        font-size: 11px;
        color: $system-background-primary;
        opacity: 0.9;

        i {
            margin-right: 4px;
            font-size: 11px;
        }
    }
}

// 标题行样式
.title-row {
    display: flex;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 6px;
}

// 高亮标签样式
.highlight-tag {
    position: absolute;
    top: 10px;
    right: -32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 0;
    width: 100px;
    font-size: 11px;
    font-weight: 700;
    white-space: nowrap;
    color: white;
    letter-spacing: 0.05em;
    z-index: 5;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: rotate(45deg);
    text-align: center;
    text-transform: uppercase;

    // 不同类型的标签样式 - 更加现代化的设计
    &.highlight-recommend {
        background: linear-gradient(135deg, rgba($system-blue, 0.9), rgba($system-indigo, 0.85));
        box-shadow: 0 2px 6px rgba($system-blue, 0.3);
    }

    &.highlight-new {
        background: linear-gradient(135deg, rgba($system-teal, 0.9), rgba($system-green, 0.85));
        box-shadow: 0 2px 6px rgba($system-green, 0.3);
    }

    &.highlight-hot {
        background: linear-gradient(135deg, rgba($system-red, 0.9), rgba($system-orange, 0.85));
        box-shadow: 0 2px 6px rgba($system-red, 0.3);
    }
}

// 空状态样式
.empty-state {
    width: calc(50% - 6px);
    min-height: 140px;
    padding: 20px 16px;
    background: $system-background-primary;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: $label-tertiary;
    transition: all 0.3s ease;
    border-width: 1px;
    border-style: dashed;
    margin: 0;
    gap: 8px;

    .empty-icon {
        position: relative;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 4px;

        .pi {
            font-size: 20px;
            opacity: 0.7;
            z-index: 1;
        }

        .empty-circle {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(var(--system-color-rgb, 0, 122, 255), 0.1);
            animation: pulse 2s infinite;
        }
    }

    p {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: $label-secondary;
    }

    .empty-hint {
        font-size: 11px;
        color: $label-tertiary;
    }

    // 不同颜色的边框
    @for $i from 0 through 7 {
        &-#{$i} {
            border-color: var(--system-color-#{$i}, $system-blue);

            .empty-circle {
                background: rgba(var(--system-color-#{$i}-rgb, 0, 122, 255), 0.1);
            }
        }
    }
}

// 动画定义
@keyframes shimmer {
    0% {
        transform: translateX(-100%) rotate(30deg);
        opacity: 0.4;
    }

    50% {
        opacity: 0.6;
    }

    100% {
        transform: translateX(100%) rotate(30deg);
        opacity: 0.4;
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.6;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.9;
    }
}

@keyframes pulseCore {

    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }

    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes pulseLine {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.8;
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes rotateRing {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatParticle {
    0% {
        transform: translate(0, 0);
    }

    25% {
        transform: translate(10px, 10px);
    }

    50% {
        transform: translate(5px, 20px);
    }

    75% {
        transform: translate(-5px, 10px);
    }

    100% {
        transform: translate(0, 0);
    }
}
</style>
