<template>
    <div class="rank-tab">
        <LoadingState v-if="initialLoading" />

        <template v-else>
            <!-- 顶部轮播展示前三名 -->
            <div class="top-carousel-section">
                <div class="section-header">
                    <h1 class="title">排行榜精英</h1>
                    <div class="category-switch" @click="toggleCategoryDrawer">切换练习</div>
                </div>

                <Swiper
                    v-if="tabs.length"
                    :modules="[Navigation, Pagination]"
                    :pagination="{ clickable: true }"
                    :space-between="16"
                    :slides-per-view="1"
                    class="top-winners-swiper"
                    @swiper="onSwiperInit"
                >
                    <SwiperSlide v-for="tab in tabs" :key="tab.key" class="winner-slide">
                        <div class="winner-card-header">
                            <h3 class="category-name">{{ tab.label }}</h3>
                        </div>

                        <div class="winners-container">
                            <!-- 第二名 -->
                            <div class="winner-card silver" v-if="getTopThreeByTab(tab.key)[1]">
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-second.png"
                                        alt="银牌"
                                    />
                                </div>
                                <div class="winner-avatar">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png"
                                        alt="头像"
                                    />
                                </div>
                                <div class="winner-name">
                                    {{ getTopThreeByTab(tab.key)[1].employeeName }}
                                </div>
                                <div class="winner-score">
                                    {{ getTopThreeByTab(tab.key)[1].score
                                    }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}
                                </div>
                            </div>
                            <div class="winner-card silver empty-winner" v-else>
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-second.png"
                                        alt="银牌"
                                    />
                                </div>
                                <div class="empty-placeholder">
                                    <div class="empty-text">暂无人上榜</div>
                                </div>
                            </div>

                            <!-- 第一名 -->
                            <div class="winner-card gold" v-if="getTopThreeByTab(tab.key)[0]">
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-first.png"
                                        alt="金牌"
                                    />
                                </div>
                                <div class="winner-avatar">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png"
                                        alt="头像"
                                    />
                                </div>
                                <div class="winner-name">
                                    {{ getTopThreeByTab(tab.key)[0].employeeName }}
                                </div>
                                <div class="winner-score">
                                    {{ getTopThreeByTab(tab.key)[0].score
                                    }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}
                                </div>
                            </div>
                            <div class="winner-card gold empty-winner" v-else>
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-first.png"
                                        alt="金牌"
                                    />
                                </div>
                                <div class="empty-placeholder">
                                    <div class="empty-text">暂无人上榜</div>
                                </div>
                            </div>

                            <!-- 第三名 -->
                            <div class="winner-card bronze" v-if="getTopThreeByTab(tab.key)[2]">
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-third.png"
                                        alt="铜牌"
                                    />
                                </div>
                                <div class="winner-avatar">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png"
                                        alt="头像"
                                    />
                                </div>
                                <div class="winner-name">
                                    {{ getTopThreeByTab(tab.key)[2].employeeName }}
                                </div>
                                <div class="winner-score">
                                    {{ getTopThreeByTab(tab.key)[2].score
                                    }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}
                                </div>
                            </div>
                            <div class="winner-card bronze empty-winner" v-else>
                                <div class="medal-badge">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-third.png"
                                        alt="铜牌"
                                    />
                                </div>
                                <div class="empty-placeholder">
                                    <div class="empty-text">暂无人上榜</div>
                                </div>
                            </div>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>

            <!-- 业务项导航区 -->
            <div class="category-navigation">
                <div class="hot-categories" v-if="!initialLoading && tabs.length">
                    <div class="category-list-header">
                        <h3>练习分类</h3>
                        <div
                            class="toggle-btn"
                            @click="expandAllCategories = !expandAllCategories"
                            v-if="filteredTabs.length > 6"
                        >
                            {{ expandAllCategories ? '收起' : '展开全部' }}
                        </div>
                    </div>

                    <div class="category-list" :class="{ expanded: expandAllCategories }">
                        <div
                            v-for="tab in displayTabs"
                            :key="tab.key"
                            class="category-item"
                            :class="{ active: currentTab === tab.key }"
                            @click="handleTabClick(tab.key)"
                        >
                            <span class="category-name">{{ tab.label }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排行榜详情区 -->
            <div class="rank-details">
                <div class="details-header" v-if="currentTab && !initialLoading">
                    <h2>{{ getCurrentTabLabel() }}</h2>
                    <div class="rank-subtitle">完整排名</div>
                </div>

                <div class="rank-list-container">
                    <template v-if="filteredRankList.length">
                        <div
                            v-for="(user, index) in getCurrentTabRankList()"
                            :key="user.id"
                            class="rank-item"
                            :class="{
                                'is-me': user.isCurrentUser,
                                'rank-first': index === 0,
                                'rank-second': index === 1,
                                'rank-third': index === 2
                            }"
                        >
                            <!-- 排名标识 -->
                            <div class="rank-badge">
                                <template v-if="index < 3">
                                    <img
                                        :src="getRankBadgeIcon(index + 1)"
                                        :alt="'rank ' + (index + 1)"
                                        class="rank-icon"
                                    />
                                </template>
                                <template v-else>
                                    <span class="rank-number">{{ index + 1 }}</span>
                                </template>
                            </div>

                            <!-- 用户信息 -->
                            <div class="user-info">
                                <img
                                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/default-avatar.png"
                                    alt="用户头像"
                                    class="user-avatar"
                                />
                                <div class="user-detail">
                                    <div class="user-name">
                                        {{ user.employeeName
                                        }}<span class="employee-code">（{{ user.hrcode }}）</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 分数信息 -->
                            <div class="score-info">
                                <div class="score-value">
                                    {{ user.score }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}
                                </div>
                                <div class="task-count">
                                    {{ formatDate(user.modifiedStime) }}
                                </div>
                            </div>
                        </div>

                        <!-- 显示数量提示 -->
                        <div
                            v-if="
                                getCurrentTabRankList().length >= 30 &&
                                getRankListByTab(currentTab).length > 30
                            "
                            class="rank-limit-tip"
                        >
                            <div class="tip-content">
                                <span class="tip-text">仅展示前30名</span>
                                <span class="total-count"
                                    >（共{{ getRankListByTab(currentTab).length }}人）</span
                                >
                            </div>
                        </div>
                    </template>

                    <div v-else class="empty-state">
                        <img
                            src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-nodata.png"
                            alt=""
                        />
                        <p>{{ EXAM_TEXT.EMPTY.RANK_NO_DATA }}</p>
                    </div>
                </div>
            </div>

            <!-- 分类抽屉 -->
            <div class="category-drawer" :class="{ 'drawer-open': categoryDrawerVisible }">
                <div class="drawer-header">
                    <h3>选择练习</h3>
                    <div class="close-btn" @click="toggleCategoryDrawer">×</div>
                </div>
                <div class="drawer-content">
                    <div class="category-list-header">
                        <div class="category-count-total">共 {{ tabs.length }} 个练习</div>
                    </div>

                    <div class="category-section">
                        <div class="section-title">热门练习</div>
                        <div class="category-grid">
                            <div
                                v-for="tab in tabs.slice(0, 4)"
                                :key="tab.key"
                                class="category-item"
                                :class="{ active: currentTab === tab.key }"
                                @click="selectCategoryAndClose(tab.key)"
                            >
                                <div class="category-info">
                                    <span class="category-name">{{ tab.label }}</span>
                                </div>
                                <span class="category-count"
                                    >{{ getRankListByTab(tab.key).length }}人</span
                                >
                            </div>
                        </div>
                    </div>

                    <div class="category-section" v-if="tabs.length > 4">
                        <div class="section-title">全部练习</div>
                        <div
                            v-for="tab in tabs"
                            :key="tab.key"
                            class="category-item"
                            :class="{ active: currentTab === tab.key }"
                            @click="selectCategoryAndClose(tab.key)"
                        >
                            <div class="category-info">
                                <span class="category-name">{{ tab.label }}</span>
                            </div>
                            <span class="category-count"
                                >{{ getRankListByTab(tab.key).length }}人</span
                            >
                        </div>
                    </div>
                </div>
            </div>

            <!-- 背景遮罩 -->
            <div
                class="drawer-mask"
                v-if="categoryDrawerVisible"
                @click="toggleCategoryDrawer"
            ></div>
        </template>
    </div>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi';
import { ref, onMounted, computed, nextTick } from 'vue';
import useTrainerStore from '@/stores/trainer';
import dayjs from 'dayjs';
import LoadingState from '@/components/common/LoadingState.vue';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import { isDingTalk } from '@/utils/index';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// 状态
const initialLoading = ref(true);
const rankList = ref([]);
const trainerStore = useTrainerStore();
const expandAllCategories = ref(false);
const categoryDrawerVisible = ref(false);
const isRefreshing = ref(false);

// Swiper实例引用
const swiperInstance = ref(null);

// 默认选中第一个 tab
const currentTab = ref('');

// 练习项目优先级配置 - 按优先级从高到低排列
// 使用说明：
// 1. 数组中的数字对应examId，按优先级从高到低排列
// 2. 如需调整优先级，只需修改此数组的顺序即可
// 3. 如需添加新的优先级项目，将对应的examId添加到数组中合适的位置
// 4. 不在此数组中的examId将按照原始顺序排在优先级项目之后
// 示例：['15', '14', '13'] 表示examId为15的优先级最高，14次之，13再次之
const EXAM_PRIORITY_ORDER = ['14', '13'];

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '排行榜'
    });
}

/**
 * 过滤后的排行榜数据
 */
const filteredRankList = computed(() => {
    if (!rankList.value || !rankList.value.length) {
        return [];
    }
    return rankList.value;
});

/**
 * 从排行榜数据中提取 tab 信息
 */
const tabs = computed(() => {
    if (!rankList.value || !Array.isArray(rankList.value) || !rankList.value.length) {
        return [];
    }

    const uniqueExams = Array.from(
        new Set(
            rankList.value
                .map(item => {
                    if (!item || !item.examId) {
                        return null;
                    }
                    return JSON.stringify({
                        examId: item.examId,
                        name: item.name || '未命名业务项'
                    });
                })
                .filter(Boolean)
        )
    ).map(str => JSON.parse(str));

    // 根据优先级配置对练习项目进行排序
    const sortedExams = [...uniqueExams].sort((a, b) => {
        const aPriority = EXAM_PRIORITY_ORDER.indexOf(String(a.examId));
        const bPriority = EXAM_PRIORITY_ORDER.indexOf(String(b.examId));

        // 如果都在优先级列表中，按优先级排序
        if (aPriority !== -1 && bPriority !== -1) {
            return aPriority - bPriority;
        }
        // 如果只有a在优先级列表中，a排在前面
        if (aPriority !== -1 && bPriority === -1) {
            return -1;
        }
        // 如果只有b在优先级列表中，b排在前面
        if (aPriority === -1 && bPriority !== -1) {
            return 1;
        }
        // 如果都不在优先级列表中，保持原顺序
        return 0;
    });

    return sortedExams.map(exam => ({
        key: exam.examId.toString(),
        label: exam.name
    }));
});

/**
 * 修改：由于删除了搜索功能，直接使用tabs
 */
const filteredTabs = computed(() => {
    return tabs.value;
});

/**
 * 根据展开/收起状态控制显示的业务项
 */
const displayTabs = computed(() => {
    if (expandAllCategories.value) {
        return filteredTabs.value;
    }
    return filteredTabs.value.slice(0, 6); // 只显示前6个热门业务项
});

/**
 * 根据 tab 获取对应的排行榜数据
 */
const getRankListByTab = tabKey => {
    if (!rankList.value || !Array.isArray(rankList.value)) {
        return [];
    }
    return rankList.value.filter(item => item && item.examId && item.examId.toString() === tabKey);
};

/**
 * 获取当前选中tab的排名数据（限制前30条）
 */
const getCurrentTabRankList = () => {
    const fullList = getRankListByTab(currentTab.value);
    // 只展示前30条数据
    return fullList.slice(0, 30);
};

/**
 * 获取指定tab的前三名
 */
const getTopThreeByTab = tabKey => {
    const list = getRankListByTab(tabKey);
    return list.slice(0, 3);
};

/**
 * 格式化日期
 */
const formatDate = dateStr => {
    if (!dateStr) {
        return '';
    }
    return dayjs(dateStr).format('YYYY-MM-DD');
};

/**
 * 获取排名图标
 */
const getRankBadgeIcon = rank => {
    const badges = {
        1: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-first.png',
        2: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-second.png',
        3: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/rank-third.png'
    };
    return badges[rank] || '';
};

/**
 * 获取排行榜数据
 */
const fetchRankList = async () => {
    try {
        initialLoading.value = true;
        const result = await trainerStore.getRankList();
        rankList.value = Array.isArray(result) ? result : [];

        if (!rankList.value.length) {
            console.warn('排行榜数据为空');
        }
    } catch (err) {
        console.error('获取排行榜失败:', err);
        rankList.value = [];
    } finally {
        initialLoading.value = false;
        isRefreshing.value = false;

        // 设置默认选中的tab - 优先选择优先级最高的tab
        if (tabs.value && tabs.value.length > 0 && !currentTab.value) {
            // 从优先级配置中找到第一个存在的tab
            let selectedTab = null;
            for (const priorityId of EXAM_PRIORITY_ORDER) {
                selectedTab = tabs.value.find(tab => tab.key === priorityId);
                if (selectedTab) {
                    break;
                }
            }
            // 如果优先级列表中没有找到，则选择第一个
            currentTab.value = selectedTab ? selectedTab.key : tabs.value[0].key;
        }
    }
};

/**
 * 切换分类抽屉的显示状态
 */
const toggleCategoryDrawer = () => {
    categoryDrawerVisible.value = !categoryDrawerVisible.value;

    if (categoryDrawerVisible.value) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
};

/**
 * 选择分类并关闭抽屉
 */
const selectCategoryAndClose = tabKey => {
    handleTabClick(tabKey);
    toggleCategoryDrawer();
};

/**
 * 处理标签点击
 */
const handleTabClick = tabKey => {
    if (!tabs.value || !tabs.value.length) {
        return;
    }

    if (currentTab.value !== tabKey) {
        currentTab.value = tabKey;

        // 同步顶部轮播图位置
        syncSwiperPosition(tabKey);

        // 重置滚动位置到顶部
        nextTick(() => {
            const container = document.querySelector('.rank-list-container');
            if (container) {
                container.scrollTop = 0;
            }
        });
    }
};

/**
 * 同步顶部轮播图位置
 */
const syncSwiperPosition = tabKey => {
    if (!swiperInstance.value) {
        return;
    }

    // 找到对应tab的索引
    const tabIndex = tabs.value.findIndex(tab => tab.key === tabKey);
    if (tabIndex !== -1) {
        // 滑动到对应位置
        swiperInstance.value.slideTo(tabIndex);
    }
};

/**
 * 处理Swiper初始化
 */
const onSwiperInit = swiper => {
    swiperInstance.value = swiper;

    // 监听Swiper滑动事件，同步更新当前选中的tab
    swiper.on('slideChange', () => {
        const activeIndex = swiper.activeIndex;
        if (tabs.value[activeIndex]) {
            currentTab.value = tabs.value[activeIndex].key;
        }
    });
};

/**
 * 获取当前选中tab的显示名称
 */
const getCurrentTabLabel = () => {
    const currentTabObj = tabs.value.find(tab => tab.key === currentTab.value);
    return currentTabObj ? currentTabObj.label : '全部';
};

onMounted(() => {
    fetchRankList();
});
</script>

<style lang="scss" scoped>
@use 'sass:color';

::-webkit-scrollbar {
    width: 4px;
    display: none;
}

.rank-tab {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    // 顶部轮播区域样式优化
    .top-carousel-section {
        padding: 14px 14px 26px;
        background: linear-gradient(135deg, $system-blue 0%, $system-indigo 100%);
        color: $system-background-primary;
        box-shadow: 0 4px 16px rgba($system-indigo, 0.15);
        position: relative;
        border-radius: 0 0 16px 16px;

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .title {
                font-size: 20px;
                font-weight: bold;
                margin: 0;
                background: linear-gradient(
                    to right,
                    $system-background-primary,
                    rgba($system-background-primary, 0.9)
                );
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .category-switch {
                background: rgba($system-background-primary, 0.15);
                padding: 5px 10px;
                border-radius: 14px;
                font-size: 13px;
                display: flex;
                align-items: center;
                backdrop-filter: blur(4px);
            }
        }

        .loading-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .top-winners-swiper {
            margin-top: 8px;
            padding-bottom: 26px;

            :deep(.swiper-pagination) {
                bottom: 0;

                .swiper-pagination-bullet {
                    background: rgba($system-background-primary, 0.6);

                    &-active {
                        background: $system-background-primary;
                        width: 16px;
                        border-radius: 4px;
                    }
                }
            }
        }

        .winner-slide {
            padding: 4px;

            .winner-card-header {
                text-align: center;
                margin-bottom: 20px;

                .category-name {
                    font-size: 16px;
                    font-weight: 600;
                    margin: 0;
                    padding: 4px 0;
                    color: $system-background-primary;
                    text-shadow: 0 1px 2px rgba($label-primary, 0.2);
                }
            }

            .winners-container {
                display: flex;
                justify-content: center;
                align-items: flex-end;
                gap: 16px;
                min-height: 150px;

                .winner-card {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    border-radius: 10px;
                    position: relative;
                    width: 85px;
                    box-shadow: 0 4px 8px rgba($label-primary, 0.12);
                    transition: transform 0.3s ease;

                    &.empty-winner {
                        background: rgba($system-background-primary, 0.15);
                        border: 1px dashed rgba($system-background-primary, 0.3);

                        .empty-placeholder {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            height: 100%;
                            width: 100%;
                            padding: 10px 0;

                            .empty-text {
                                font-size: 12px;
                                color: rgba($system-background-primary, 0.8);
                                text-align: center;
                                margin-top: 20px;
                            }
                        }
                    }

                    &.gold {
                        background: linear-gradient(135deg, $system-yellow 0%, $system-orange 100%);
                        height: 150px;
                        z-index: 3;

                        &.empty-winner {
                            background: rgba($system-yellow, 0.15);
                        }

                        .winner-avatar {
                            width: 60px;
                            height: 60px;
                            border-radius: 50%;
                            overflow: hidden;
                            border: 2px solid $system-background-primary;
                            margin: 8px 0;
                        }
                    }

                    &.silver {
                        background: linear-gradient(135deg, $system-gray5 0%, $system-gray 100%);
                        height: 130px;

                        &.empty-winner {
                            background: rgba($system-gray5, 0.15);
                        }

                        .winner-avatar {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            overflow: hidden;
                            border: 2px solid $system-background-primary;
                        }
                    }

                    &.bronze {
                        background: linear-gradient(
                            135deg,
                            $system-orange 0%,
                            color.mix($system-orange, black, $weight: 85%) 50%,
                            color.scale($system-orange, $lightness: 10%) 100%
                        );
                        height: 110px;

                        &.empty-winner {
                            background: rgba($system-orange, 0.15);
                        }

                        .winner-avatar {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            overflow: hidden;
                            border: 2px solid $system-background-primary;
                        }
                    }

                    .medal-badge {
                        position: absolute;
                        top: -15px;
                        width: 30px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .winner-rank {
                        font-size: 18px;
                        font-weight: bold;
                        margin-top: 5px;
                        color: $system-background-primary;
                        text-shadow: 0 1px 2px rgba($label-primary, 0.2);
                    }

                    .winner-avatar {
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .winner-name {
                        font-size: 13px;
                        font-weight: 600;
                        color: $system-background-primary;
                        text-align: center;
                        margin: 4px 0;
                        text-shadow: 0 1px 1px rgba($label-primary, 0.2);
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .winner-score {
                        font-size: 14px;
                        font-weight: bold;
                        color: $system-background-primary;
                        background: rgba($label-primary, 0.2);
                        padding: 2px 8px;
                        border-radius: 10px;
                        margin-top: 4px;
                    }
                }
            }
        }
    }

    // 业务项导航区 - 优化样式
    .category-navigation {
        padding: 12px;
        background: $system-background-primary;
        border-radius: 12px;
        margin: -18px 10px 12px;
        box-shadow: 0 3px 10px rgba($label-primary, 0.06);
        position: relative;
        z-index: 10;

        .hot-categories {
            .category-list-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                h3 {
                    font-size: 15px;
                    margin: 0;
                    color: $label-primary;
                    font-weight: 600;
                    position: relative;
                    padding-left: 10px;

                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 3px;
                        height: 14px;
                        background: $system-blue;
                        border-radius: 2px;
                    }
                }

                .toggle-btn {
                    font-size: 12px;
                    color: $system-blue;
                    background: rgba($system-blue, 0.08);
                    padding: 3px 8px;
                    border-radius: 10px;
                }
            }

            .category-list {
                padding: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                max-height: 110px;
                overflow: hidden;
                transition: max-height 0.3s ease;

                &.expanded {
                    max-height: 1000px;
                }

                .category-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: calc(33.33% - 6px);
                    padding: 10px 8px;
                    border-radius: 8px;
                    background: $system-background-secondary;
                    border: 1px solid $separator-color-opaque;
                    transition: all 0.2s ease;

                    &.active {
                        background: rgba($system-blue, 0.12);
                        border-color: rgba($system-blue, 0.3);
                        position: relative;
                        box-shadow: 0 2px 8px rgba($system-blue, 0.15);
                        transform: translateY(-2px);

                        .category-name {
                            color: $system-blue;
                            font-weight: 600;
                        }

                        &::after {
                            content: '';
                            position: absolute;
                            width: 6px;
                            height: 6px;
                            background: $system-blue;
                            border-radius: 50%;
                            bottom: 4px;
                            left: 50%;
                            transform: translateX(-50%);
                        }

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            border-radius: 8px;
                            background: linear-gradient(
                                135deg,
                                rgba($system-blue, 0.1),
                                rgba($system-indigo, 0.1)
                            );
                            z-index: -1;
                        }
                    }

                    &:active {
                        background: rgba($system-blue, 0.12);
                    }

                    .category-name {
                        font-size: 12px;
                        font-weight: 500;
                        color: $label-primary;
                        flex: 1;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-align: center;
                        transition: color 0.2s ease;
                    }

                    .category-count {
                        position: absolute;
                        top: -5px;
                        right: -5px;
                        font-size: 11px;
                        color: $system-background-primary;
                        background: $system-blue;
                        padding: 2px 5px;
                        border-radius: 10px;
                        min-width: 20px;
                        text-align: center;
                    }
                }
            }
        }
    }

    // 排行榜详情区 - 优化样式
    .rank-details {
        margin: 0 10px 12px;
        background: $system-background-primary;
        border-radius: 12px;
        padding: 14px;
        display: flex;
        flex-direction: column;
        flex: 1;

        .details-header {
            margin-bottom: 16px;
            border-bottom: 1px solid rgba($label-primary, 0.05);
            padding-bottom: 10px;

            h2 {
                font-size: 16px;
                margin: 0 0 4px;
                color: $label-primary;
                display: flex;
                align-items: center;
                font-weight: 600;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 3px;
                    height: 16px;
                    background: $system-blue;
                    margin-right: 6px;
                    border-radius: 2px;
                }
            }

            .rank-subtitle {
                font-size: 12px;
                color: $label-secondary;
                margin-left: 10px;
            }
        }

        .rank-list-container {
            flex: 1;
            overflow-y: auto;
            padding: 0 2px;

            .rank-item {
                display: flex;
                align-items: center;
                padding: 12px 10px;
                border-radius: 8px;
                margin-bottom: 8px;
                background: $system-background-primary;
                position: relative;
                border: 1px solid rgba($label-primary, 0.05);
                animation: fadeIn 0.3s ease-out forwards;
                opacity: 0;
                transform: translateY(5px);

                @for $i from 1 through 20 {
                    &:nth-child(#{$i}) {
                        animation-delay: #{$i * 0.03}s;
                    }
                }

                &.is-me {
                    background: rgba($system-blue, 0.1);
                    border-left: 3px solid $system-blue;
                }

                &.rank-first {
                    background: linear-gradient(
                        135deg,
                        $system-yellow 0%,
                        $system-orange 50%,
                        color.scale($system-orange, $lightness: 15%) 100%
                    );
                    box-shadow: 0 2px 12px rgba($system-yellow, 0.3);

                    .user-name,
                    .employee-code,
                    .task-count {
                        color: $system-background-primary;
                        text-shadow: 0 1px 2px rgba($label-primary, 0.2);
                    }

                    .score-value {
                        color: $system-background-primary;
                        background: none;
                        -webkit-text-fill-color: $system-background-primary;
                    }
                }

                &.rank-second {
                    background: linear-gradient(
                        135deg,
                        $system-gray5 0%,
                        $system-gray 50%,
                        $system-gray4 100%
                    );
                    box-shadow: 0 2px 8px rgba($system-gray, 0.3);

                    .user-name,
                    .employee-code,
                    .task-count {
                        color: $system-background-primary;
                        text-shadow: 0 1px 2px rgba($label-primary, 0.2);
                    }

                    .score-value {
                        color: $system-background-primary;
                        background: none;
                        -webkit-text-fill-color: $system-background-primary;
                    }
                }

                &.rank-third {
                    background: linear-gradient(
                        135deg,
                        $system-orange 0%,
                        color.mix($system-orange, black, $weight: 85%) 50%,
                        color.scale($system-orange, $lightness: 10%) 100%
                    );
                    box-shadow: 0 2px 8px rgba($system-orange, 0.3);

                    .user-name,
                    .employee-code,
                    .task-count {
                        color: $system-background-primary;
                        text-shadow: 0 1px 2px rgba($label-primary, 0.2);
                    }

                    .score-value {
                        color: $system-background-primary;
                        background: none;
                        -webkit-text-fill-color: $system-background-primary;
                    }
                }

                .rank-badge {
                    width: 36px;
                    height: 36px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .rank-icon {
                        width: 100%;
                    }

                    .rank-number {
                        font-size: 16px;
                        font-weight: 700;
                        width: 28px;
                        height: 28px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: $label-secondary;
                        background: $system-gray6;
                        border-radius: 50%;
                    }
                }

                .user-info {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    margin: 0 6px;

                    .user-avatar {
                        width: 36px;
                        height: 36px;
                        border-radius: 50%;
                        object-fit: cover;
                        border: 2px solid $system-background-primary;
                        box-shadow: 0 2px 4px rgba($label-primary, 0.08);
                    }

                    .user-detail {
                        .user-name {
                            font-size: 14px;
                            font-weight: 600;
                            color: $label-primary;
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            .employee-code {
                                font-size: 11px;
                                font-weight: 500;
                                color: $label-secondary;
                            }
                        }
                    }
                }

                .score-info {
                    text-align: right;
                    margin-right: 8px;

                    .score-value {
                        font-size: 16px;
                        font-weight: 700;
                        margin-bottom: 2px;
                        background: linear-gradient(135deg, $system-blue, $system-indigo);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        letter-spacing: 0.5px;
                    }

                    .task-count {
                        font-size: 11px;
                        color: $label-secondary;
                    }
                }

                &.rank-first,
                &.rank-second,
                &.rank-third {
                    .user-detail {
                        .user-name {
                            color: $system-background-primary;

                            .employee-code {
                                color: rgba($system-background-primary, 0.9);
                            }
                        }
                    }
                }
            }

            .rank-limit-tip {
                margin: 16px 0 8px;
                padding: 10px 12px;
                background: rgba($system-blue, 0.05);
                border: 1px dashed rgba($system-blue, 0.2);
                border-radius: 8px;
                text-align: center;

                .tip-content {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 4px;

                    .tip-text {
                        font-size: 13px;
                        color: $system-blue;
                        font-weight: 500;
                    }

                    .total-count {
                        font-size: 12px;
                        color: $label-secondary;
                    }
                }
            }

            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 220px;
                color: $label-tertiary;
                text-align: center;
                padding: 24px;
                border-radius: 14px;
                background: $system-background-primary;
                margin: 16px 0;
                box-shadow: 0 5px 20px rgba($label-primary, 0.05);
                animation: fadeIn 0.4s ease-out forwards;

                img {
                    width: 240px;
                    margin-bottom: 14px;
                    filter: drop-shadow(0 5px 15px rgba($label-primary, 0.05));
                    animation: floatVertical 6s 0.5s ease-in-out infinite;
                }

                p {
                    letter-spacing: 1px;
                    margin: 0 0 14px;
                    font-weight: bold;
                    font-size: 15px;
                    color: $label-secondary;
                }
            }
        }
    }

    // 分类抽屉样式优化
    .category-drawer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        background: $system-background-primary;
        border-radius: 20px 20px 0 0;
        transform: translateY(100%);
        transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1), box-shadow 0.3s ease;
        z-index: 1000;
        max-height: 70vh;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        &.drawer-open {
            transform: translateY(0);

            &::before {
                opacity: 1;
            }

            .drawer-header {
                animation: fadeInDown 0.4s ease forwards;
            }

            .drawer-content {
                animation: fadeInUp 0.4s ease forwards;

                .category-item {
                    opacity: 0;
                    transform: translateY(10px);
                    animation: slideInUp 0.3s ease forwards;

                    @for $i from 1 through 20 {
                        &:nth-child(#{$i}) {
                            animation-delay: #{$i * 0.03}s;
                        }
                    }
                }
            }
        }

        .drawer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px;
            border-bottom: 1px solid $system-gray6;

            h3 {
                font-size: 15px;
                margin: 0;
                color: $label-primary;
                font-weight: 600;
                position: relative;
                padding-left: 10px;

                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3px;
                    height: 14px;
                    background: $system-blue;
                    border-radius: 2px;
                }
            }

            .close-btn {
                width: 26px;
                height: 26px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                color: $label-secondary;
                transition: all 0.2s ease;

                &:active {
                    background: $system-gray5;
                    transform: scale(0.95);
                }
            }
        }

        .drawer-content {
            padding: 14px;
            overflow-y: auto;
            flex: 1;
            -webkit-overflow-scrolling: touch;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 20px;
                background: linear-gradient(
                    to bottom,
                    $system-background-primary,
                    rgba($system-background-primary, 0)
                );
                z-index: 5;
                pointer-events: none;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 20px;
                background: linear-gradient(
                    to top,
                    $system-background-primary,
                    rgba($system-background-primary, 0)
                );
                z-index: 5;
                pointer-events: none;
            }

            .category-list-header {
                margin-bottom: 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .category-count-total {
                    font-size: 12px;
                    color: $label-secondary;
                    background: $system-background-secondary;
                    padding: 3px 8px;
                    border-radius: 10px;
                }
            }

            .category-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin-bottom: 14px;
            }

            .category-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 10px;
                border-radius: 10px;
                margin-bottom: 6px;
                border: 1px solid $system-gray6;
                transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
                position: relative;
                overflow: hidden;
                box-shadow: 0 2px 4px rgba($label-primary, 0.02);

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(
                        135deg,
                        rgba($system-blue, 0.05),
                        rgba($system-indigo, 0.05)
                    );
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    z-index: -2;
                }

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(
                        135deg,
                        rgba($system-blue, 0.1),
                        rgba($system-indigo, 0.1)
                    );
                    transform: scaleX(0);
                    transform-origin: left;
                    transition: transform 0.3s ease;
                    z-index: -1;
                }

                &:active {
                    background: rgba($system-blue, 0.05);
                    transform: scale(0.98);
                    box-shadow: 0 1px 3px rgba($label-primary, 0.02);
                }

                &.active {
                    background: rgba($system-blue, 0.05);
                    border-color: rgba($system-blue, 0.3);
                    box-shadow: 0 4px 12px rgba($system-blue, 0.15);

                    &::before {
                        opacity: 1;
                    }

                    &::after {
                        transform: scaleX(1);
                    }

                    .category-name {
                        color: $system-blue;
                        font-weight: 600;
                    }

                    .category-count {
                        background: rgba($system-blue, 0.15);
                        color: $system-blue;
                    }
                }

                .category-info {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .category-name {
                    font-size: 14px;
                    font-weight: 500;
                    color: $label-primary;
                    transition: color 0.2s ease;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .category-description {
                    font-size: 11px;
                    color: $label-secondary;
                    max-width: 140px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .category-count {
                    font-size: 12px;
                    color: $label-secondary;
                    background: $fill-color-quaternary;
                    padding: 2px 8px;
                    border-radius: 10px;
                    transition: all 0.2s ease;
                    min-width: 36px;
                    text-align: center;
                }
            }

            .category-section {
                margin-bottom: 16px;

                .section-title {
                    font-size: 13px;
                    color: $label-secondary;
                    margin-bottom: 10px;
                    padding-left: 6px;
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 2px;
                        height: 12px;
                        background: $system-blue;
                        border-radius: 1px;
                    }
                }
            }
        }
    }

    .drawer-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba($label-primary, 0.5);
        z-index: 999;
        animation: fadeIn 0.3s ease;
    }
}

// 动画关键帧
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatVertical {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%,
    100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes goldShine {
    0%,
    15% {
        left: -100%;
    }
    35%,
    100% {
        left: 150%;
    }
}

@keyframes silverShine {
    0%,
    20% {
        left: -100%;
    }
    40%,
    100% {
        left: 150%;
    }
}

@keyframes bronzeShine {
    0%,
    25% {
        left: -100%;
    }
    45%,
    100% {
        left: 150%;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
