<template>
    <div class="practice-container">
        <!-- 返回家家精灵按钮 - 只有从家家精灵跳转过来才显示 -->
        <BackToGenie v-if="isFromGenie" />

        <!-- 练习模块内部导航 -->
        <div class="practice-nav">
            <RouterLink to="/smart-trainer/practice/home" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-tab', { active: isActive }]" @click="navigate">
                    <span>首页</span>
                </div>
            </RouterLink>
            <RouterLink to="/smart-trainer/practice/rank" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-tab', { active: isActive }]" @click="navigate">
                    <span>排行榜</span>
                </div>
            </RouterLink>
            <RouterLink to="/smart-trainer/practice/my" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-tab', { active: isActive }]" @click="navigate">
                    <span>我的</span>
                </div>
            </RouterLink>
        </div>

        <div class="router-view-container">
            <RouterView />
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import BackToGenie from '../../components/BackToGenie.vue';

const router = useRouter();
const route = useRoute();

// 检查是否从家家精灵跳转过来
const isFromGenie = computed(() => {
    return Number(route.query.fromGenie) === 1;
});

onMounted(() => {
    // 如果访问 /practice 路径，重定向到 home
    if (router.currentRoute.value.path === '/smart-trainer/practice') {
        router.replace('/smart-trainer/practice/home');
    }
});
</script>

<style lang="scss" scoped>
.practice-container {
    min-height: 100vh;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
}

.practice-nav {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .nav-tab {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 0;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;

        span {
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            transition: color 0.3s ease;
        }

        &.active {
            span {
                color: #3b82f6;
                font-weight: 600;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 30px;
                height: 3px;
                background: #3b82f6;
                border-radius: 2px;
            }
        }

        &:hover:not(.active) {
            span {
                color: #4b5563;
            }
        }
    }
}

.router-view-container {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
</style>
