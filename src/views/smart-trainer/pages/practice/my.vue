<template>
    <div class="my-container">
        <!-- 头部区域保留，但稍微精简高度 -->
        <div class="header-section">
            <div class="header-content">
                <h1 class="title">我的练习</h1>
                <p class="subtitle">记录每一次成长历程</p>
            </div>
            <div class="header-decoration">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/myrecord-logo.png"
                    alt="practice"
                    class="practice-icon"
                />
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 将筛选器区域和筛选面板放在同一个父容器中 -->
            <div class="filter-container" :class="{ expanded: showFilterPanel }">
                <!-- 筛选器区域 -->
                <div class="filter-section">
                    <!-- 主要Tab切换 -->
                    <div class="primary-tabs">
                        <div
                            v-for="tab in tabs"
                            :key="tab.key"
                            class="tab-item"
                            :class="{ active: currentTab === tab.key }"
                            @click="handleTabChange(tab.key)"
                        >
                            {{ tab.label }}
                        </div>
                    </div>

                    <!-- 筛选器按钮 -->
                    <div class="filter-trigger" @click="showFilterPanel = !showFilterPanel">
                        <span>筛选</span>
                        <i
                            class="pi"
                            :class="showFilterPanel ? 'pi-chevron-up' : 'pi-chevron-down'"
                        ></i>
                    </div>
                </div>

                <!-- 筛选面板 - 可折叠 -->
                <div class="filter-panel" v-show="showFilterPanel">
                    <div class="filter-content">
                        <div class="filter-group">
                            <div class="filter-label">练习类型</div>
                            <div class="filter-options">
                                <div
                                    class="filter-option"
                                    :class="{ active: !currentExamId }"
                                    @click="handleExamFilterChange(null)"
                                >
                                    全部
                                </div>
                                <div
                                    v-for="exam in flattenedExams"
                                    :key="exam.id"
                                    class="filter-option"
                                    :class="{
                                        active: currentExamId === exam.id
                                    }"
                                    @click="handleExamFilterChange(exam.id)"
                                >
                                    {{ exam.name }}
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <div class="filter-label">时间范围</div>
                            <div class="filter-options">
                                <div
                                    v-for="filter in dateFilters"
                                    :key="filter.key"
                                    class="filter-option"
                                    :class="{
                                        active: currentDateFilter === filter.key
                                    }"
                                    @click="handleDateFilterChange(filter.key)"
                                >
                                    {{ filter.label }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已选筛选条件展示区域 -->
            <div class="selected-filters" v-if="hasActiveFilters">
                <div class="selected-filters-title">已选条件:</div>
                <div class="selected-filters-list">
                    <!-- 练习类型筛选标签 -->
                    <div class="filter-tag" v-if="currentExamId">
                        <span class="tag-text">{{ getSelectedExamName }}</span>
                        <i class="pi pi-times" @click="handleExamFilterChange(null)"></i>
                    </div>

                    <!-- 时间范围筛选标签 -->
                    <div class="filter-tag" v-if="currentDateFilter !== 'all'">
                        <span class="tag-text">{{ getSelectedDateFilterLabel }}</span>
                        <i class="pi pi-times" @click="handleDateFilterChange('all')"></i>
                    </div>
                </div>
                <div class="clear-all" @click="clearAllFilters">
                    <i class="pi pi-trash"></i>
                    <span>清除全部</span>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section">
                <!-- 加载状态 -->
                <LoadingState v-if="initialLoading" />

                <!-- 任务卡片列表 -->
                <div v-else-if="filteredPaperList.length" class="task-list">
                    <div
                        v-for="paper in filteredPaperList"
                        :key="paper.id"
                        :data-paper-id="paper.id"
                        class="task-card-wrapper"
                        @touchstart="handleTouchStart($event, paper.id)"
                        @touchmove="canDelete(paper) ? handleTouchMove($event, paper.id) : null"
                        @touchend="canDelete(paper) ? handleTouchEnd($event, paper.id) : null"
                    >
                        <div
                            class="task-card"
                            :style="{
                                transform: `translateX(${
                                    canDelete(paper) ? slideOffset[paper.id] || 0 : 0
                                }px)`,
                                background: getSlideBackground(paper.id),
                                borderRadius: slideOffset[paper.id] ? '10px 0 0 10px' : '10px'
                            }"
                            @click="handlePaperNavigation(paper)"
                        >
                            <div class="task-header">
                                <div class="task-title-section">
                                    <div class="task-title">
                                        <span class="task-name">{{
                                            paper.name || `练习任务 ${paper.id}`
                                        }}</span>
                                    </div>
                                    <Tag
                                        :class="['custom-tag', `status-${paper.paperStatus}`]"
                                        :value="getStatusText(paper.paperStatus)"
                                    />
                                </div>

                                <div
                                    v-if="getPaperConfig(paper.paperStatus).showScore"
                                    class="task-score"
                                    :class="getScoreClass(paper.score)"
                                >
                                    <span class="score-text"
                                        >{{ paper.score }}{{ EXAM_TEXT.SCORE.SCORE_SUFFIX }}</span
                                    >
                                </div>
                            </div>

                            <div class="task-info">
                                <div class="task-time">
                                    <img
                                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/calendar-icon.png"
                                        alt="time"
                                        class="time-icon"
                                    />
                                    {{ formatDate(paper.createdStime) }}
                                </div>

                                <Button
                                    v-if="getPaperConfig(paper.paperStatus).showDetailBtn"
                                    class="detail-btn"
                                    text
                                    v-ripple
                                    @click.stop="handlePaperNavigation(paper)"
                                >
                                    {{ getPaperConfig(paper.paperStatus).buttonText }}
                                    <i class="pi pi-angle-right"></i>
                                </Button>
                            </div>
                        </div>
                        <!-- 只对可删除的试卷显示删除按钮 -->
                        <div
                            v-if="canDelete(paper)"
                            class="delete-button"
                            :style="{
                                opacity: (slideOffset[paper.id] || 0) / -80
                            }"
                            @click="handleDelete(paper.id)"
                        >
                            <i class="pi pi-trash"></i>
                            删除
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="empty-state">
                    <div class="empty-wrapper">
                        <img
                            src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/no-paper.png"
                            alt="暂无练习数据"
                            class="empty-image"
                        />
                        <p class="empty-text">暂无练习数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { ref, onMounted, computed, watch, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import useTrainerStore from '@/stores/trainer';
import dayjs from 'dayjs';
import LoadingState from '@/components/common/LoadingState.vue';
import { PAPER_STATUS_CONFIG } from '../../constants/paper';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import { getScoreClass } from '../../constants/score';
import { isDingTalk } from '@/utils/index';
const toast = useToast();

const router = useRouter();
const trainerStore = useTrainerStore();

// 修改 tab 相关数据，添加状态映射
const tabs = [
    {
        key: 'ongoing',
        label: '进行中',
        paperStatuses: [0, 1] // 未开始和进行中的状态
    },
    {
        key: 'completed',
        label: '已完成',
        paperStatuses: [3, 4] // 已完成和评分完成的状态
    }
];

// 修改为从 localStorage 获取初始值，如果没有则默认为 'ongoing'
const currentTab = ref(localStorage.getItem('smart-trainer-tab') || 'ongoing');
const paperList = ref([]);
const currentPage = ref(1);
const pageSize = ref(100);
const initialLoading = ref(true);
// 新增筛选面板显示状态
const showFilterPanel = ref(false);

// 日期筛选相关数据
const dateFilters = [
    { key: 'all', label: '全部' },
    { key: '3days', label: '3天内' },
    { key: '1week', label: '1周内' },
    { key: '1month', label: '1个月内' }
];
const currentDateFilter = ref('all');

// 考试分类相关数据
const examCategories = ref([]);
const flattenedExams = ref([]); // 新增扁平化后的考试列表
const currentExamId = ref(null);

// 添加新的响应式变量
const slideOffset = ref({}); // 滑动偏移量
const touchStart = ref({ x: 0, y: 0 }); // 触摸起始位置
const isSliding = ref(false); // 是否滑动中
const deleteButtonWidth = 70; // 删除按钮宽度

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: '练习记录'
    });
}

/**
 * 获取试卷列表
 * @param {number[]} paperStatuses - 试卷状态数组
 */
async function fetchPaperList(paperStatuses) {
    try {
        initialLoading.value = true;
        const body = {
            pageIndex: currentPage.value,
            pageSize: pageSize.value,
            paperStatuses // 添加状态筛选参数
        };

        // 添加日期筛选条件
        const dateRange = getDateFilterRange();
        if (dateRange) {
            body.startDate = dateRange.startTime;
            body.endDate = dateRange.endTime;
        }

        // 添加考试ID筛选条件
        if (currentExamId.value) {
            body.examId = currentExamId.value;
        }

        const result = await trainerStore.getPaperList(body);

        if (result) {
            paperList.value = result.list;
        }
    } catch (error) {
        console.error('获取试卷列表失败:', error);
    } finally {
        initialLoading.value = false;
    }
}

/**
 * 处理 tab 切换
 * @param {string} tabKey - tab的key值
 */
const handleTabChange = tabKey => {
    currentTab.value = tabKey;
    // 保存当前 tab 到 localStorage
    localStorage.setItem('smart-trainer-tab', tabKey);

    // 获取当前 tab 对应的状态数组
    const currentStatuses = tabs.find(tab => tab.key === tabKey)?.paperStatuses;
    // 重新请求数据
    if (currentStatuses) {
        fetchPaperList(currentStatuses);
    }
};

// 修改计算属性，直接使用请求返回的数据
const filteredPaperList = computed(() => paperList.value);

// 获取试卷配置信息
function getPaperConfig(status) {
    return (
        PAPER_STATUS_CONFIG[status] || {
            text: '未知状态',
            severity: 'info',
            showDetailBtn: false,
            showScore: false,
            canNavigate: false
        }
    );
}

// 获取状态文本
function getStatusText(status) {
    return getPaperConfig(status).text;
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) {
        return '';
    }
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
}

/**
 * 处理试卷不同状态下的跳转逻辑
 * @param {Object} paper - 试卷信息对象
 */
const handlePaperNavigation = paper => {
    switch (paper.paperStatus) {
        case 0: // 试卷已创建
        case 1: // 答题进行中
            router.push(`/smart-trainer/exam/exam?paperId=${paper.id}&examId=${paper.examId}`);
            break;

        case 3: // 答题已结束
        case 4: // 评分已完成
            router.push(`/smart-trainer/exam/result?paperId=${paper.id}&examId=${paper.examId}`);
            break;

        default:
            console.warn(EXAM_TEXT.ERRORS.UNKNOWN_STATUS, paper.paperStatus);
            break;
    }
};

/**
 * 处理考试分类筛选
 * @param {number|null} examId - 考试分类ID
 */
const handleExamFilterChange = examId => {
    currentExamId.value = examId;
    // 重新请求数据
    const currentStatuses = tabs.find(tab => tab.key === currentTab.value)?.paperStatuses;

    if (currentStatuses) {
        fetchPaperList(currentStatuses);
    }

    // 选中后关闭筛选框
    showFilterPanel.value = false;
};

/**
 * 处理日期筛选切换
 * @param {string} filterKey - 筛选条件的key值
 */
const handleDateFilterChange = filterKey => {
    currentDateFilter.value = filterKey;
    // 重新请求数据时可以带上日期筛选条件
    const currentStatuses = tabs.find(tab => tab.key === currentTab.value)?.paperStatuses;

    if (currentStatuses) {
        fetchPaperList(currentStatuses);
    }

    // 选中后关闭筛选框
    showFilterPanel.value = false;
};

/**
 * 获取日期筛选的时间范围
 * @returns {Object|null} 开始时间和结束时间
 */
const getDateFilterRange = () => {
    const now = dayjs();

    switch (currentDateFilter.value) {
        case '3days':
            return {
                startTime: now.subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
                endTime: now.format('YYYY-MM-DD HH:mm:ss')
            };
        case '1week':
            return {
                startTime: now.subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
                endTime: now.format('YYYY-MM-DD HH:mm:ss')
            };
        case '1month':
            return {
                startTime: now.subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
                endTime: now.format('YYYY-MM-DD HH:mm:ss')
            };
        default:
            return null;
    }
};

/**
 * 获取考试分类数据
 * @returns {Promise<void>}
 */
async function fetchExamCategories() {
    try {
        const result = await trainerStore.getExamCategoryAndLevel();
        if (result && Array.isArray(result)) {
            examCategories.value = result;

            // 扁平化处理考试数据，提取所有exams
            flattenedExams.value = result.reduce((acc, category) => {
                if (category.exams && Array.isArray(category.exams)) {
                    return [...acc, ...category.exams];
                }
                return acc;
            }, []);
        }
    } catch (error) {
        console.error('获取考试分类数据失败:', error);
    }
}

/**
 * 判断试卷是否可删除
 * @param {Object} paper - 试卷信息
 * @returns {boolean} 是否可删除
 */
const canDelete = paper => {
    // 只允许未开始和进行中的试卷删除
    return [0, 1].includes(paper.paperStatus);
};

/**
 * 处理触摸开始事件
 * @param {TouchEvent} event - 触摸事件对象
 * @param {number} paperId - 试卷ID
 */
const handleTouchStart = (event, paperId) => {
    const paper = paperList.value.find(p => p.id === paperId);
    if (!canDelete(paper)) {
        return;
    }

    // 重置其他试卷的滑动状态
    Object.keys(slideOffset.value).forEach(key => {
        if (Number(key) !== paperId) {
            slideOffset.value[key] = 0;
        }
    });

    const touch = event.touches[0];
    touchStart.value = {
        x: touch.clientX,
        y: touch.clientY,
        paperId
    };
    isSliding.value = false;
};

/**
 * 重置所有删除抽屉状态
 */
const resetAllSlideOffsets = () => {
    slideOffset.value = {};
    touchStart.value = null;
    isSliding.value = false;
};

// 监听筛选条件变化
watch([currentTab, currentExamId, currentDateFilter], () => {
    resetAllSlideOffsets();
});

// 组件销毁时重置状态
onBeforeUnmount(() => {
    resetAllSlideOffsets();
});

/**
 * 处理触摸移动事件
 * @param {TouchEvent} event - 触摸事件对象
 * @param {number} paperId - 试卷ID
 */
const handleTouchMove = (event, paperId) => {
    if (!touchStart.value) {
        return;
    }

    const touch = event.touches[0];
    const deltaX = touch.clientX - touchStart.value.x;
    const deltaY = Math.abs(touch.clientY - touchStart.value.y);

    // 如果垂直移动距离大于水平移动距离的一半，则不进行水平滑动
    if (!isSliding.value && deltaY > Math.abs(deltaX) / 2) {
        touchStart.value = null;
        return;
    }

    isSliding.value = true;
    event.preventDefault();

    // 计算滑动偏移量
    let newOffset = (slideOffset.value[paperId] || 0) + deltaX;

    // 限制滑动范围
    newOffset = Math.min(0, Math.max(-deleteButtonWidth, newOffset));

    // 更新偏移量
    slideOffset.value = {
        ...slideOffset.value,
        [paperId]: newOffset
    };

    // 更新起始位置
    touchStart.value.x = touch.clientX;
};

/**
 * 处理触摸结束事件
 * @param {TouchEvent} event - 触摸事件对象
 * @param {number} paperId - 试卷ID
 */
const handleTouchEnd = (event, paperId) => {
    if (!isSliding.value) {
        return;
    }

    const currentOffset = slideOffset.value[paperId] || 0;

    // 根据滑动距离决定是否展开删除按钮
    const finalOffset = currentOffset < -deleteButtonWidth / 2 ? -deleteButtonWidth : 0;

    slideOffset.value = {
        ...slideOffset.value,
        [paperId]: finalOffset
    };

    touchStart.value = null;
    isSliding.value = false;
};

/**
 * 处理删除事件
 * @param {number} paperId - 试卷ID
 */
const handleDelete = async paperId => {
    try {
        // 获取要删除的元素
        const paperElement = document.querySelector(`[data-paper-id="${paperId}"]`);
        if (paperElement) {
            // 添加删除动画类
            paperElement.classList.add('deleting');

            // 等待动画完成后再从数据中移除
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // 调用删除接口
        const result = await trainerStore.deletePractice({
            paperId
        });

        // 如果删除成功，更新前端数据
        if (result) {
            paperList.value = paperList.value.filter(paper => paper.id !== paperId);

            toast.add({
                severity: 'success',
                summary: '删除成功',
                detail: '练习记录已删除',
                life: 2000
            });
        }
    } catch (error) {
        console.error('删除练习失败:', error);
        toast.add({
            severity: 'error',
            summary: '删除失败',
            detail: '请稍后再试',
            life: 2000
        });
    }
};

/**
 * 根据滑动偏移量计算背景渐变
 * @param {number} paperId - 试卷ID
 * @returns {string} 背景样式
 */
const getSlideBackground = paperId => {
    const offset = slideOffset.value[paperId] || 0;
    const progress = Math.min(Math.abs(offset) / deleteButtonWidth, 1);

    if (progress === 0) {
        return '#ffffff';
    }

    // 使用与删除按钮相匹配的渐变色
    return `linear-gradient(to right, 
        #ffffff ${100 - progress * 100}%, 
        rgba(255, 77, 79, ${progress * 0.4}) ${100 - progress * 70}%,
        rgba(255, 77, 79, ${progress * 0.8}) ${100 - progress * 30}%,
        rgb(255, 77, 79) 100%
    )`;
};

// 判断是否有激活的筛选条件
const hasActiveFilters = computed(() => {
    return currentExamId.value !== null || currentDateFilter.value !== 'all';
});

// 获取当前选中的考试名称
const getSelectedExamName = computed(() => {
    if (!currentExamId.value) {
        return '';
    }
    const selectedExam = flattenedExams.value.find(exam => exam.id === currentExamId.value);
    return selectedExam ? selectedExam.name : '';
});

// 获取当前选中的日期筛选标签
const getSelectedDateFilterLabel = computed(() => {
    if (currentDateFilter.value === 'all') {
        return '';
    }
    const selectedFilter = dateFilters.find(filter => filter.key === currentDateFilter.value);
    return selectedFilter ? selectedFilter.label : '';
});

/**
 * 清除所有筛选条件
 */
const clearAllFilters = () => {
    currentExamId.value = null;
    currentDateFilter.value = 'all';

    // 重新请求数据
    const currentStatuses = tabs.find(tab => tab.key === currentTab.value)?.paperStatuses;
    if (currentStatuses) {
        fetchPaperList(currentStatuses);
    }
};

// 判断是否有激活的筛选条件
onMounted(() => {
    // 初始化时获取当前 tab 对应的状态数据
    const currentStatuses = tabs.find(tab => tab.key === currentTab.value)?.paperStatuses;
    fetchPaperList(currentStatuses);

    // 获取考试分类数据
    fetchExamCategories();
});
</script>

<style lang="scss" scoped>
.my-container {
    height: 100%;
    background: linear-gradient(180deg, $system-gray6 0%, $system-background-primary 20%);
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
}

.header-section {
    padding: 16px;
    background: linear-gradient(135deg, $system-gray6 0%, $system-gray7 100%);
    box-shadow: 0 1px 10px rgba($system-indigo, 0.06);
    position: relative;
    overflow: hidden;
    color: $label-primary;
    margin-bottom: 0;

    .header-content {
        position: relative;
        max-width: 1200px;
        margin: 0 auto;

        .title {
            font-size: 22px;
            font-weight: 700;
            margin: 0 0 4px;
            background: linear-gradient(to right, $system-blue, $system-indigo);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .subtitle {
            font-size: 13px;
            color: rgba($system-indigo, 0.8);
            margin: 0;
            font-weight: 400;
        }
    }

    .header-decoration {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);

        .practice-icon {
            width: 70px;
            height: 70px;
            filter: drop-shadow(0 4px 8px rgba(22, 119, 255, 0.15));
        }
    }
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 新增筛选容器样式 */
.filter-container {
    margin: 12px 12px 0;
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    /* 默认状态 */
    background: $system-background-primary;
    border: 1px solid rgba($system-gray4, 0.7);

    /* 展开状态 */
    &.expanded {
        background: linear-gradient(135deg, $system-blue, rgba($system-indigo, 0.8));
        box-shadow: 0 8px 20px rgba($system-blue, 0.2);
        border: none;

        /* 展开状态下的filter-section样式 */
        .filter-section {
            background: transparent;
            box-shadow: none;
            position: relative;
            z-index: 1;

            .primary-tabs {
                .tab-item {
                    color: rgba(255, 255, 255, 0.85);

                    &.active {
                        color: #ffffff;
                        font-weight: 600;

                        &::after {
                            background-color: #ffffff;
                        }
                    }

                    &:hover:not(.active) {
                        color: rgba(255, 255, 255, 1);
                    }
                }
            }

            .filter-trigger {
                color: #ffffff;
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(4px);

                &:hover {
                    background: rgba(255, 255, 255, 0.25);
                }

                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }
}

.filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background: $system-background-primary;
    border-radius: 16px;
    transition: all 0.3s ease;

    .primary-tabs {
        display: flex;
        gap: 20px;

        .tab-item {
            position: relative;
            padding: 4px 0;
            cursor: pointer;
            color: $label-secondary;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: 500;

            &.active {
                color: $system-blue;
                font-weight: 600;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -3px;
                    left: 0;
                    width: 100%;
                    height: 3px;
                    background-color: $system-blue;
                    border-radius: 1.5px;
                }
            }

            &:hover:not(.active) {
                color: $system-blue;
            }

            &:active {
                opacity: 0.7;
            }
        }
    }

    .filter-trigger {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 6px 12px;
        background: $system-grouped-background-primary;
        border-radius: 16px;
        cursor: pointer;
        font-size: 13px;
        color: $label-secondary;
        transition: all 0.2s ease;
        border: 1px solid rgba($system-gray4, 0.7);
        font-weight: 500;

        &:hover {
            background: $fill-color-quaternary;
            border-color: $system-gray3;
        }

        &:active {
            transform: scale(0.95);
        }

        i {
            font-size: 12px;
        }
    }
}

/* 修改filter-panel样式 */
.filter-panel {
    padding: 12px 16px 16px;
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .filter-content {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .filter-group {
        position: relative;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        padding: 12px 14px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(8px);

        .filter-label {
            font-size: 14px;
            color: #ffffff;
            margin-bottom: 10px;
            font-weight: 600;
            position: relative;
            padding-left: 10px;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 3px;
                height: 14px;
                background: linear-gradient(to bottom, #ffffff, rgba(255, 255, 255, 0.7));
                border-radius: 1.5px;
            }
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .filter-option {
                padding: 6px 12px;
                font-size: 13px;
                color: rgba(255, 255, 255, 0.95);
                background: rgba(255, 255, 255, 0.12);
                border-radius: 16px;
                cursor: pointer;
                transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                border: 1px solid rgba(255, 255, 255, 0.25);
                font-weight: 500;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(
                        135deg,
                        rgba(255, 255, 255, 0.12),
                        rgba(255, 255, 255, 0.05)
                    );
                    opacity: 0;
                    transition: opacity 0.25s ease;
                    z-index: 0;
                }

                &:active {
                    transform: scale(0.95);
                }

                &:not(.active):hover {
                    border-color: rgba(255, 255, 255, 0.5);
                    box-shadow: 0 0 12px rgba(255, 255, 255, 0.2);

                    &::before {
                        opacity: 1;
                    }
                }

                &.active {
                    color: $system-blue;
                    background: rgba(255, 255, 255, 0.95);
                    font-weight: 600;
                    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.35);
                    border-color: transparent;

                    &:active {
                        transform: scale(0.95);
                    }
                }
            }
        }
    }
}

.content-section {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    /* 增加滚动平滑效果 */
    scroll-behavior: smooth;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: rgba($system-gray6, 0.5);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba($system-gray2, 0.4);
        border-radius: 3px;
    }
}

.task-list {
    padding: 4px;

    .task-card-wrapper {
        position: relative;
        margin-bottom: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        max-height: 200px;
        opacity: 1;
        border-radius: 16px;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.03);

        /* 添加删除时的动画类 */
        &.deleting {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
            transform: translateX(100%);
        }

        .task-card {
            position: relative;
            z-index: 1;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s ease,
                box-shadow 0.3s ease;
            touch-action: pan-y pinch-zoom;
            will-change: transform;
        }

        .delete-button {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 70px;
            background: linear-gradient(135deg, rgba(255, 77, 79, 0.9), rgb(255, 47, 47));
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 13px;
            font-weight: 600;
            border-radius: 0 16px 16px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            z-index: 0;
            box-shadow: -2px 0 10px rgba(255, 77, 79, 0.2);

            i {
                margin-right: 4px;
                font-size: 14px;
            }

            &:active {
                background: linear-gradient(135deg, rgb(235, 57, 59), rgb(235, 27, 27));
            }
        }
    }

    .task-card {
        background: $system-background-primary;
        padding: 16px;
        transition: all 0.3s ease;
        animation: slideInFromBottom 0.4s ease;
        border: 1px solid rgba($system-gray4, 0.7);
        border-radius: 16px;
        overflow: hidden;

        &:active {
            background: rgba($system-gray6, 0.7);
            transform: scale(0.98);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .task-title-section {
                display: flex;
                align-items: center;
                gap: 10px;
                flex: 1;

                .task-title {
                    font-size: 15px;
                    font-weight: 600;
                    color: $label-primary;
                    display: flex;
                    align-items: center;
                    letter-spacing: -0.3px;

                    .task-name {
                        display: inline-block;
                        max-width: 200px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }

            .task-score {
                font-size: 15px;
                font-weight: 700;
                border-radius: 16px;
                padding: 4px 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
                letter-spacing: -0.5px;

                &.fail {
                    color: $system-red;
                    background: linear-gradient(
                        135deg,
                        rgba($system-red, 0.08),
                        rgba($system-red, 0.12)
                    );
                    border: 1px solid rgba($system-red, 0.15);
                }

                &.pass {
                    color: $system-green;
                    background: linear-gradient(
                        135deg,
                        rgba($system-green, 0.08),
                        rgba($system-green, 0.12)
                    );
                    border: 1px solid rgba($system-green, 0.15);
                }

                &.excellent {
                    color: $system-blue;
                    background: linear-gradient(
                        135deg,
                        rgba($system-blue, 0.08),
                        rgba($system-blue, 0.12)
                    );
                    border: 1px solid rgba($system-blue, 0.15);
                }

                &.perfect {
                    color: $system-orange;
                    background: linear-gradient(
                        135deg,
                        rgba($system-orange, 0.08),
                        rgba($system-orange, 0.12)
                    );
                    border: 1px solid rgba($system-orange, 0.15);
                }
            }
        }

        .task-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 10px;
            border-top: 1px dashed rgba($system-gray4, 0.8);

            .task-time {
                font-size: 12px;
                color: $label-secondary;
                display: flex;
                align-items: center;
                gap: 6px;

                .time-icon {
                    width: 16px;
                    height: 16px;
                    opacity: 0.8;
                }
            }

            .detail-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                color: #fff;
                padding: 5px 12px;
                border-radius: 14px;
                transition: all 0.3s ease;
                background: linear-gradient(135deg, $system-blue, rgba($system-indigo, 0.7));
                box-shadow: 0 2px 8px rgba($system-blue, 0.2);
                font-weight: 500;

                .pi {
                    font-size: 12px;
                }

                &:hover {
                    box-shadow: 0 3px 12px rgba($system-blue, 0.3);
                }

                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.empty-state {
    padding: 60px 20px;
    display: flex;
    justify-content: center;
    align-items: center;

    .empty-wrapper {
        padding: 40px 32px;
        text-align: center;
        animation: fadeIn 0.5s ease;
    }

    .empty-image {
        width: 180px;
        height: 150px;
        object-fit: contain;
        margin-bottom: 20px;
        animation: floatUpDown 4s ease-in-out infinite;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }

    .empty-text {
        font-size: 16px;
        font-weight: 500;
        color: $label-secondary;
        margin: 0;
        letter-spacing: -0.3px;
    }
}

@keyframes floatUpDown {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

:deep(.custom-tag) {
    border-radius: 12px;
    padding: 3px 8px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: -0.2px;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

    /* 未开始状态 - 橙色渐变 */
    &.status-0 {
        background: linear-gradient(135deg, $system-orange, rgba($system-red, 0.8));
        color: $system-background-primary;
    }

    /* 部分作答状态 - 蓝色渐变 */
    &.status-1 {
        background: linear-gradient(135deg, $system-blue, rgba($system-indigo, 0.8));
        color: $system-background-primary;
    }

    /* 待整体评分状态 - 紫色渐变 */
    &.status-3 {
        background: linear-gradient(135deg, $system-purple, rgba($system-indigo, 0.7));
        color: $system-background-primary;
    }

    /* 评分完成状态 - 绿色渐变 */
    &.status-4 {
        background: linear-gradient(135deg, rgba($system-green, 0.9), $system-green);
        color: $system-background-primary;
    }
}

/* 已选筛选条件展示区域样式 */
.selected-filters {
    margin: 10px 12px 0;
    padding: 10px 14px;
    background: $system-background-primary;
    border-radius: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    animation: fadeInDown 0.3s ease;
    border: 1px solid rgba($system-gray4, 0.7);

    .selected-filters-title {
        font-size: 13px;
        font-weight: 500;
        color: $label-secondary;
        margin-right: 4px;
    }

    .selected-filters-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        flex: 1;
    }

    .filter-tag {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 5px 10px;
        background: rgba($system-blue, 0.08);
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        color: $system-blue;
        border: 1px solid rgba($system-blue, 0.15);
        transition: all 0.2s ease;

        .tag-text {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        i {
            font-size: 11px;
            cursor: pointer;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            &:hover {
                background: rgba($system-blue, 0.15);
            }

            &:active {
                transform: scale(0.9);
            }
        }

        &:hover {
            background: rgba($system-blue, 0.12);
            box-shadow: 0 2px 8px rgba($system-blue, 0.1);
        }
    }

    .clear-all {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 5px 10px;
        background: rgba($system-red, 0.08);
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        color: $system-red;
        cursor: pointer;
        margin-left: auto;
        transition: all 0.2s ease;
        border: 1px solid rgba($system-red, 0.15);

        i {
            font-size: 12px;
        }

        &:hover {
            background: rgba($system-red, 0.12);
            box-shadow: 0 2px 8px rgba($system-red, 0.1);
        }

        &:active {
            transform: scale(0.95);
        }
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
