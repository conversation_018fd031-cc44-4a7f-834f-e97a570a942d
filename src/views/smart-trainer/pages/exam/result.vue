<template>
    <div class="result-page">
        <div class="result-content">
            <!-- 加载中状态 -->
            <div v-if="isLoading" class="loading-state">
                <RadarLoading :text="EXAM_TEXT.STATUS.RESULT_LOADING" />
            </div>

            <!-- 数据加载失败时显示缺省图 -->
            <div v-else-if="dataError" class="empty-state">
                <img :src="emptyStateImage" alt="加载失败" class="empty-state-image" />
                <p class="empty-state-text">
                    {{ EXAM_TEXT.ERRORS.DATA_LOAD_FAILED }}
                </p>
                <div class="error-buttons">
                    <Button
                        :label="'重试'"
                        icon="pi pi-refresh"
                        @click="fetchResultData"
                        class="action-button retry-button"
                    />
                </div>
            </div>

            <!-- 数据正常时显示结果内容 -->
            <template v-else-if="dataLoaded">
                <!-- 雷达图 -->
                <div class="result-content-container" ref="resultContentRef">
                    <div class="radar-chart-container">
                        <!-- 当能力数据为空时显示缺省图 -->
                        <div v-if="!hasAbilityData" class="empty-radar">
                            <img
                                :src="emptyRadarImage"
                                alt="暂无雷达图"
                                class="empty-radar-image"
                            />
                            <p class="empty-radar-text">
                                {{ EXAM_TEXT.ERRORS.NO_RADAR_DATA }}
                            </p>
                        </div>
                        <!-- 有能力数据时显示雷达图 -->
                        <v-chart v-else class="radar-chart" :option="radarOption" autoresize />
                    </div>

                    <!-- 评价 Tab 切换 -->
                    <div class="assessment-tabs">
                        <div ref="tabsRef" class="tab-header">
                            <div
                                class="tab-item"
                                :class="{ active: activeTab === 'overall' }"
                                @click="activeTab = 'overall'"
                            >
                                <span class="tab-text">整体得分</span>
                            </div>
                            <div
                                class="tab-item"
                                :class="{ active: activeTab === 'detail' }"
                                @click="activeTab = 'detail'"
                            >
                                <span class="tab-text">维度得分</span>
                            </div>
                        </div>

                        <div class="tab-content">
                            <!-- 整体评价内容 -->
                            <div class="tab-pane" :class="{ active: activeTab === 'overall' }">
                                <!-- 总分展示 -->
                                <div class="score-overview">
                                    <div class="score-card">
                                        <div class="score-display">
                                            <span class="score-value">{{ paperScore }}</span>
                                            <span class="score-label">/ 100分</span>
                                        </div>
                                        <div class="score-level" :class="getScoreClass(paperScore)">
                                            {{ getScoreLevelText(paperScore) }}
                                        </div>
                                    </div>

                                    <div class="assessment-display">
                                        <p class="assessment-content">
                                            {{ assessment }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 能力维度详细评价内容 -->
                            <div class="tab-pane" :class="{ active: activeTab === 'detail' }">
                                <div v-if="hasAbilityData" class="ability-details">
                                    <!-- 水平滑动标签栏 -->
                                    <div class="ability-tabs">
                                        <div ref="waveTabsRef" class="wave-tabs-container">
                                            <div class="wave-tabs">
                                                <div
                                                    v-for="(ability, index) in paperAbilities"
                                                    :key="ability.abilityId"
                                                    class="wave-tab"
                                                    :class="{
                                                        active: activeAbilityTab === index
                                                    }"
                                                    @click="handleAbilityTabClick(index)"
                                                >
                                                    {{ ability.abilityName }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 使用 Swiper 替换原有的滑动内容区域 -->
                                    <Swiper
                                        v-bind="swiperOptions"
                                        class="ability-swiper"
                                        @swiper="onSwiperInit"
                                        @slideChange="handleSlideChange"
                                    >
                                        <SwiperSlide
                                            v-for="ability in paperAbilities"
                                            :key="ability.abilityId"
                                        >
                                            <div
                                                class="ability-card"
                                                :class="getScoreClass(ability.abilityScore, 10)"
                                            >
                                                <div class="ability-card-header">
                                                    <span class="ability-name">{{
                                                        ability.abilityName
                                                    }}</span>
                                                    <span class="ability-score">
                                                        {{
                                                            getScoreLevelText(
                                                                ability.abilityScore,
                                                                10
                                                            )
                                                        }}
                                                    </span>
                                                </div>

                                                <div class="ability-card-content">
                                                    <div class="ability-section">
                                                        <h4 class="section-title">维度描述</h4>
                                                        <p class="section-content">
                                                            {{ ability.abilityDesc }}
                                                        </p>
                                                    </div>

                                                    <div class="ability-section">
                                                        <h4 class="section-title">评分标准</h4>
                                                        <p class="section-content">
                                                            {{ ability.abilityCriterion }}
                                                        </p>
                                                    </div>

                                                    <div class="ability-section">
                                                        <h4 class="section-title">评价反馈</h4>
                                                        <p class="section-content assessment">
                                                            {{ ability.abilityAssessment }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </SwiperSlide>
                                    </Swiper>
                                </div>
                                <div v-else class="empty-ability-data">
                                    <img
                                        :src="emptyRadarImage"
                                        alt="暂无能力数据"
                                        class="empty-ability-image"
                                    />
                                    <p class="empty-ability-text">暂无能力维度数据</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 修改后的按钮组 -->
                <div class="action-buttons">
                    <Button
                        :label="EXAM_TEXT.BUTTONS.REVIEW_EXAM"
                        icon="pi pi-users"
                        @click="goToReview"
                        class="action-button review-button"
                    />
                    <Button
                        :label="EXAM_TEXT.BUTTONS.RETRY_PRACTICE"
                        icon="pi pi-refresh"
                        @click="retryPractice"
                        class="action-button primary-button"
                    />
                    <Button
                        :label="EXAM_TEXT.BUTTONS.BACK_HOME"
                        icon="pi pi-home"
                        @click="backToHome"
                        class="action-button secondary-button"
                    />
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, nextTick, watch } from 'vue';
import * as dd from 'dingtalk-jsapi'; // 直接导入
import { useRouter, useRoute } from 'vue-router';
import useTrainerStore from '@/stores/trainer';
import * as echarts from 'echarts/core';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { RadarChart } from 'echarts/charts';
import {
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { TRAINING_TEXT_SCHEMA2 as EXAM_TEXT } from '../../constants/text';
import RadarLoading from '@/components/common/RadarLoading.vue';
import { isDingTalk } from '@/utils/index';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import { getScoreClass, getScoreLevelText } from '../../constants/score';
import { pollAsyncResult } from '@/utils/polling';

const router = useRouter();
const route = useRoute();
const trainerStore = useTrainerStore();

// 数据状态
const paperScore = ref(0);
const paperAbilities = ref([]);
const assessment = ref('');
const paperName = ref('');

/**
 * 缺省图片路径
 * @type {import('vue').Ref<string>}
 */
const emptyStateImage = ref(
    'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/result/404-radar.png'
);

/**
 * 雷达图缺省图片路径
 * @type {import('vue').Ref<string>}
 */
const emptyRadarImage = ref(
    'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/result/404-radar.png'
);

/**
 * 判断是否有能力数据
 * @type {import('vue').ComputedRef<boolean>}
 */
const hasAbilityData = computed(() => {
    return paperAbilities.value && paperAbilities.value.length > 0;
});

/**
 * 当前激活的标签页
 * @type {import('vue').Ref<string>}
 */
const activeTab = ref('overall');

/**
 * 标签栏元素的引用
 * @type {import('vue').Ref<HTMLElement|null>}
 */
const tabsRef = ref(null);

/**
 * 当前激活的能力维度标签页索引
 * @type {import('vue').Ref<number>}
 */
const activeAbilityTab = ref(0);

/**
 * 能力维度标签栏容器引用
 * @type {import('vue').Ref<HTMLElement|null>}
 */
const waveTabsRef = ref(null);

/**
 * Swiper 实例引用
 * @type {import('vue').Ref<Swiper|null>}
 */
const swiperInstance = ref(null);

/**
 * Swiper 配置
 * @type {import('vue').Ref<Object>}
 */
const swiperOptions = {
    modules: [Navigation],
    slidesPerView: 1,
    spaceBetween: 16,
    touchStartPreventDefault: false
};

/**
 * 结果内容容器的引用
 * @type {import('vue').Ref<HTMLElement|null>}
 */
const resultContentRef = ref(null);

const isLoading = ref(false);

const dataError = ref(false);

// 添加一个新的状态变量，表示数据是否已加载完成
const dataLoaded = ref(false);

if (isDingTalk()) {
    dd.setNavigationTitle({
        title: EXAM_TEXT.PAGE_TITLES.SKILL_RADAR
    });
}

/**
 * 获取考试结果数据
 */
const fetchResultData = async () => {
    isLoading.value = true;
    dataError.value = false;
    dataLoaded.value = false; // 重置数据加载状态

    try {
        // 使用轮询工具获取结果
        const result = await pollAsyncResult(
            trainerStore.getResult,
            { paperId: route.query.paperId },
            {
                onPolling: count => {
                    console.log(`正在获取结果，第${count}次轮询`);
                },
                onSuccess: data => {
                    console.log('成功获取结果数据');
                },
                onError: error => {
                    handleResultError(error);
                },
                onTimeout: () => {
                    handleResultError('获取结果超时');
                }
            }
        );

        if (result) {
            const resultData = result.result;
            paperScore.value = resultData.paperScore || 0;
            paperAbilities.value = resultData.paperAbilities || [];
            assessment.value = resultData.assessment || '暂无整体评价';
            dataError.value = false;
            paperName.value = resultData.paperName || '';
            dataLoaded.value = true; // 标记数据已加载完成
        } else {
            // 处理结果为 null 的情况
            handleResultError('返回数据为空');
        }
    } catch (error) {
        handleResultError(error);
    } finally {
        isLoading.value = false;
    }
};

const handleResultError = error => {
    dataError.value = true;
    isLoading.value = false;
    console.error('获取考试结果失败 from handleResultError:', error);
};

/**
 * 重新练习
 */
const retryPractice = () => {
    router.push(`/smart-trainer/exam/exam?examId=${route.query.examId}&retry=true`);
};

/**
 * 返回首页
 */
const backToHome = () => {
    router.push('/smart-trainer/practice');
};

/**
 * 跳转到复盘页面
 */
const goToReview = () => {
    router.push(
        `/smart-trainer/exam/history?paperId=${route.query.paperId}&paperScore=${paperScore.value}&paperName=${paperName.value}`
    );
};

// 注册必需的 ECharts 组件
use([CanvasRenderer, RadarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent]);

// 替换原有的 chartData 和 chartOptions
const radarOption = computed(() => {
    // 如果没有能力数据，返回空配置
    if (!hasAbilityData.value) {
        return {};
    }

    // 计算雷达图的最大值，根据实际得分动态调整
    const scores = paperAbilities.value.map(item => item.abilityScore);
    const maxScore = Math.max(...scores);
    // 设置最大值为实际最高分的1.5倍，但不超过10分，且最小为5分
    const dynamicMax = Math.min(10, Math.max(5, Math.ceil(maxScore * 1.5)));

    return {
        tooltip: {
            show: false
        },
        radar: {
            shape: 'polygon',
            center: ['50%', '50%'],
            radius: '65%',
            indicator: paperAbilities.value.map((item, index) => ({
                name: item.abilityName,
                max: dynamicMax,
                index: index
            })),
            splitNumber: 4,
            axisName: {
                color: 'rgba(0, 0, 0, 1)',
                fontSize: 16,
                padding: [5, 8],
                formatter: name => {
                    return `{a|${name}}`;
                },
                rich: {
                    a: {
                        color: '#1F1F39',
                        fontSize: 13,
                        fontWeight: 600,
                        padding: [6, 10, 6, 10],
                        align: 'center',
                        lineHeight: 20
                    },
                    b: {
                        color: '#FF6B00',
                        fontSize: 13,
                        fontWeight: 700,
                        align: 'center',
                        lineHeight: 20,
                        textShadow: '0 1px 1px rgba(255, 107, 0, 0.3)'
                    }
                }
            },
            splitArea: {
                show: true,
                areaStyle: {
                    color: [
                        'rgba(22, 119, 255, 0.04)',
                        'rgba(22, 119, 255, 0.08)',
                        'rgba(22, 119, 255, 0.12)',
                        'rgba(22, 119, 255, 0.16)'
                    ].reverse()
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(22, 119, 255, 0.3)',
                    width: 2,
                    type: 'solid'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: 'rgba(22, 119, 255, 0.25)',
                    width: 1.5,
                    type: 'solid'
                }
            }
        },
        series: [
            {
                type: 'radar',
                data: [
                    {
                        value: paperAbilities.value.map(item => item.abilityScore),
                        name: '能力得分',
                        symbolSize: 8,
                        symbolRotate: 45,
                        lineStyle: {
                            width: 3.5,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#1677FF' },
                                { offset: 1, color: '#4318FF' }
                            ]),
                            shadowColor: 'rgba(67, 24, 255, 0.4)',
                            shadowBlur: 15,
                            shadowOffsetY: 3,
                            cap: 'square',
                            join: 'miter'
                        },
                        itemStyle: {
                            color: '#FFFFFF',
                            borderColor: '#1677FF',
                            borderWidth: 3,
                            shadowColor: 'rgba(22, 119, 255, 0.5)',
                            shadowBlur: 10
                        },
                        areaStyle: {
                            color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(22, 119, 255, 0.6)'
                                },
                                {
                                    offset: 0.7,
                                    color: 'rgba(67, 24, 255, 0.5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(67, 24, 255, 0.2)'
                                }
                            ]),
                            opacity: 0.9
                        },
                        emphasis: {
                            lineStyle: {
                                width: 5,
                                shadowBlur: 25
                            },
                            itemStyle: {
                                color: '#1677FF',
                                borderColor: '#FFFFFF',
                                borderWidth: 2,
                                shadowBlur: 25
                            }
                        }
                    }
                ]
            }
        ],
        animation: true,
        animationDuration: 1500,
        animationEasing: 'cubicOut',
        animationDelay: idx => idx * 120
    };
});

/**
 * 初始化 Swiper
 * @param {Swiper} swiper - Swiper 实例
 */
const onSwiperInit = swiper => {
    swiperInstance.value = swiper;
};

/**
 * 处理 Swiper 滑动改变事件
 */
const handleSlideChange = () => {
    if (swiperInstance.value) {
        activeAbilityTab.value = swiperInstance.value.activeIndex;
        scrollTabToCenter(activeAbilityTab.value);
    }
};

/**
 * 处理能力标签点击
 * @param {number} index - 目标标签索引
 */
const handleAbilityTabClick = index => {
    if (swiperInstance.value && activeAbilityTab.value !== index) {
        swiperInstance.value.slideTo(index);
    }
};

/**
 * 将标签栏滚动到使当前标签居中
 * @param {number} index - 目标标签索引
 */
const scrollTabToCenter = index => {
    if (!waveTabsRef.value) {
        return;
    }

    const tabsContainer = waveTabsRef.value;
    const tabElements = tabsContainer.querySelectorAll('.wave-tab');

    if (index >= 0 && index < tabElements.length) {
        const tabElement = tabElements[index];
        const containerWidth = tabsContainer.offsetWidth;
        const tabWidth = tabElement.offsetWidth;
        const tabLeft = tabElement.offsetLeft;

        // 计算需要滚动的位置，使标签居中
        const scrollLeft = tabLeft - containerWidth / 2 + tabWidth / 2;

        tabsContainer.scrollTo({
            left: Math.max(0, scrollLeft),
            behavior: 'smooth'
        });
    }
};

// 监听 activeTab 的变化
watch(activeTab, newTab => {
    if (newTab === 'detail') {
        // 使用 nextTick 确保 DOM 更新后再滚动
        nextTick(() => {
            if (resultContentRef.value) {
                resultContentRef.value.scrollTo({
                    top: resultContentRef.value.scrollHeight,
                    behavior: 'smooth'
                });
            }
        });
    }
});

// 在组件挂载后设置初始布局
onMounted(() => {
    fetchResultData();

    // 监听窗口大小变化，重新计算布局
    window.addEventListener('resize', scrollTabToCenter);
});

onUnmounted(() => {
    // 移除事件监听
    window.removeEventListener('resize', scrollTabToCenter);
});
</script>

<style lang="scss" scoped>
@use 'sass:color';

.result-page {
    height: 100%;
    background: $system-background-primary;
    display: flex;
    flex-direction: column;

    .result-content {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        height: 100%;

        .loading-state {
            height: 100%;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 40px 20px;
            animation: fadeIn 0.6s ease;

            .empty-state-image {
                width: 200px;
                height: auto;
                margin-bottom: 24px;
                animation: floatAnimation 3s ease-in-out infinite;
            }

            .empty-state-text {
                font-size: 14px;
                color: $label-secondary;
                text-align: center;
                margin-bottom: 32px;
            }

            .error-buttons {
                display: flex;
                gap: 12px;
                width: 100%;
                justify-content: center;
            }

            .back-home-button,
            .retry-button {
                width: 40%;
                max-width: 160px;
                border-radius: 24px;
                padding: 12px 16px;
                font-size: 16px;
                font-weight: 500;

                &:active {
                    transform: scale(0.96);
                }
            }

            .retry-button {
                background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                border: none;
                color: white;
            }
        }

        .result-content-container {
            flex: 1;
            overflow-y: auto;
        }

        .radar-chart-container {
            width: 100%;
            height: 320px;
            animation: fadeIn 0.6s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(22, 119, 255, 0.15);

            .radar-chart {
                width: 100%;
                height: 100%;
            }

            .empty-radar {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;

                .empty-radar-image {
                    width: 160px;
                    height: auto;
                    margin-bottom: 16px;
                    animation: floatAnimation 3s ease-in-out infinite;
                }

                .empty-radar-text {
                    font-size: 14px;
                    color: #666;
                    text-align: center;
                }
            }
        }

        .assessment-tabs {
            padding: 0 20px;
            animation: slideUp 0.8s ease;

            .tab-header {
                display: flex;
                position: relative;
                background: #f8f9ff;
                border-radius: 12px;
                padding: 4px;
                margin: 16px 0;

                .tab-item {
                    flex: 1;
                    text-align: center;
                    padding: 12px 0;
                    position: relative;
                    z-index: 2;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    cursor: pointer;
                    border-radius: 8px;

                    .tab-text {
                        font-size: 15px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        color: #666;
                    }

                    &.active {
                        background: #1677ff;

                        .tab-text {
                            color: #fff;
                        }
                    }

                    &:active {
                        transform: scale(0.97);
                    }
                }
            }

            .tab-content {
                position: relative;

                .tab-pane {
                    display: none;
                    transition: opacity 0.3s ease, transform 0.3s ease;
                    transform: translateY(10px);

                    &.active {
                        display: block;
                        opacity: 1;
                        transform: translateY(0);
                        animation: fadeIn 0.3s ease;
                    }
                }
            }

            .score-overview {
                display: flex;
                flex-direction: column;
                align-items: center;
                animation: fadeIn 0.6s ease;
                gap: 20px;
                margin-top: 30px;

                .score-card {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 100%;

                    .score-display {
                        display: flex;
                        align-items: baseline;
                        margin-bottom: 20px;

                        .score-value {
                            font-size: 36px;
                            font-weight: 700;
                            color: #1677ff;
                            line-height: 1;
                            background: linear-gradient(135deg, #1677ff 0%, #4318ff 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            text-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
                        }

                        .score-label {
                            font-size: 14px;
                            color: #666;
                            margin-left: 4px;
                        }
                    }

                    .score-level {
                        font-size: 14px;
                        font-weight: 600;
                        padding: 4px 12px;
                        border-radius: 12px;
                        animation: fadeIn 0.8s ease;

                        &.perfect {
                            background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
                            color: #fff;
                        }

                        &.excellent {
                            background: linear-gradient(135deg, #1677ff 0%, #4318ff 100%);
                            color: #fff;
                        }

                        &.pass {
                            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                            color: #fff;
                        }

                        &.fail {
                            background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
                            color: #fff;
                        }
                    }
                }

                .assessment-display {
                    width: 100%;

                    .assessment-content {
                        font-size: 14px;
                        line-height: 1.8;
                        color: $label-primary;
                        background: linear-gradient(
                            135deg,
                            rgba(22, 119, 255, 0.03) 0%,
                            rgba(67, 24, 255, 0.05) 100%
                        );
                        border-radius: 16px;
                        padding: 16px;
                        border: 1px solid rgba(22, 119, 255, 0.15);
                        position: relative;
                        min-height: 120px;

                        &::before {
                            content: '"';
                            position: absolute;
                            top: 8px;
                            left: 12px;
                            font-size: 24px;
                            color: rgba(22, 119, 255, 0.2);
                            font-family: serif;
                            line-height: 1;
                        }

                        &::after {
                            content: '"';
                            position: absolute;
                            bottom: 0;
                            right: 12px;
                            font-size: 24px;
                            color: rgba(22, 119, 255, 0.2);
                            font-family: serif;
                            line-height: 1;
                        }
                    }
                }
            }

            .ability-details {
                margin-bottom: 20px;
                overflow-x: hidden;

                .ability-tabs {
                    position: relative;
                    margin-bottom: 10px;

                    .wave-tabs-container {
                        overflow-x: auto;
                        overflow-y: hidden;
                        scrollbar-width: none; /* Firefox */
                        -ms-overflow-style: none; /* IE and Edge */
                        padding: 12px 0 8px;
                        border-radius: 16px;
                        margin: 0 -4px;
                        padding: 12px 8px;

                        &::-webkit-scrollbar {
                            display: none; /* Chrome, Safari, Opera */
                        }

                        .wave-tabs {
                            display: flex;
                            min-width: min-content;
                            gap: 10px;
                            position: relative;

                            .wave-tab {
                                position: relative;
                                padding: 10px 20px;
                                font-size: 14px;
                                font-weight: 600;
                                color: $label-secondary;
                                background: $system-background-primary;
                                border-radius: 20px;
                                border: 1px solid rgba($system-blue, 0.15);
                                overflow: hidden;
                                z-index: 1;
                                white-space: nowrap;
                                flex-shrink: 0;
                                transition: all 0.3s ease;

                                &:hover {
                                    transform: translateY(-1px);
                                }

                                &.active {
                                    color: $system-background-primary;
                                    background-color: $system-blue;
                                    border: none;
                                    transform: translateY(-1px);
                                }
                            }
                        }
                    }
                }
            }

            .ability-swiper {
                width: 100%;
                height: auto;
                overflow: visible;
                margin-top: 16px;

                :deep(.swiper-slide) {
                    height: auto;
                    transition: opacity 0.3s ease;
                }
            }

            .empty-ability-data {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 0;

                .empty-ability-image {
                    width: 120px;
                    height: auto;
                    margin-bottom: 16px;
                    animation: floatAnimation 3s ease-in-out infinite;
                }

                .empty-ability-text {
                    font-size: 14px;
                    color: $label-secondary;
                    text-align: center;
                }
            }
        }

        .action-buttons {
            flex-shrink: 0;
            padding: 10px 10px 10px;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            gap: 12px;
            animation: fadeIn 0.6s ease;
            background: #fff;

            .action-button {
                height: 40px;
                flex: 1;
                border-radius: 24px;
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                min-width: 0;

                :deep(.p-button-icon) {
                    font-size: 16px;
                    margin-right: 6px;
                }

                :deep(.p-button-label) {
                    white-space: nowrap;
                }

                &.review-button {
                    background: linear-gradient(135deg, #ff9533 0%, #ff6b00 100%);
                    border: none;
                    color: white;
                }

                &.primary-button {
                    background: linear-gradient(135deg, #1677ff 0%, #4318ff 100%);
                    border: none;
                    color: white;
                }

                &.secondary-button {
                    background: #f8f9ff;
                    border: 1px solid rgba(22, 119, 255, 0.15);
                    color: #1677ff;
                }

                &:active {
                    transform: scale(0.96);
                }
            }
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatAnimation {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(0.95);
    }
}

@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.ability-card {
    background: $system-background-primary;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: currentColor;
        opacity: 0.1;
    }

    &.perfect {
        background: linear-gradient(
            135deg,
            rgba($system-yellow, 0.05) 0%,
            rgba($system-orange, 0.08) 100%
        );
        border: 1px solid rgba($system-yellow, 0.2);
        color: $system-yellow;

        .ability-card-header {
            background: linear-gradient(
                135deg,
                rgba($system-yellow, 0.1) 0%,
                rgba($system-orange, 0.15) 100%
            );
        }

        .ability-score {
            background: linear-gradient(135deg, $system-yellow, $system-orange);
        }

        .ability-card-content {
            border-left: 3px solid $system-yellow;
        }

        .section-title {
            color: $system-yellow;
        }
    }

    &.excellent {
        background: linear-gradient(
            135deg,
            rgba($system-blue, 0.05) 0%,
            rgba($system-indigo, 0.08) 100%
        );
        border: 1px solid rgba($system-blue, 0.2);
        color: $system-blue;

        .ability-card-header {
            background: linear-gradient(
                135deg,
                rgba($system-blue, 0.1) 0%,
                rgba($system-indigo, 0.15) 100%
            );
        }

        .ability-score {
            background: linear-gradient(135deg, $system-blue, $system-indigo);
        }

        .ability-card-content {
            border-left: 3px solid $system-blue;
        }

        .section-title {
            color: $system-blue;
        }
    }

    &.pass {
        background: linear-gradient(
            135deg,
            rgba($system-green, 0.05) 0%,
            rgba($system-green, 0.08) 100%
        );
        border: 1px solid rgba($system-green, 0.2);
        color: $system-green;

        .ability-card-header {
            background: linear-gradient(
                135deg,
                rgba($system-green, 0.1) 0%,
                rgba($system-green, 0.15) 100%
            );
        }

        .ability-score {
            background: linear-gradient(
                135deg,
                $system-green,
                color.mix($system-green, black, $weight: 90%)
            );
        }

        .ability-card-content {
            border-left: 3px solid $system-green;
        }

        .section-title {
            color: $system-green;
        }
    }

    &.fail {
        background: linear-gradient(
            135deg,
            rgba($system-red, 0.05) 0%,
            rgba($system-red, 0.08) 100%
        );
        border: 1px solid rgba($system-red, 0.2);
        color: $system-red;

        .ability-card-header {
            background: linear-gradient(
                135deg,
                rgba($system-red, 0.1) 0%,
                rgba($system-red, 0.15) 100%
            );
        }

        .ability-score {
            background: linear-gradient(
                135deg,
                $system-red,
                color.mix($system-red, black, $weight: 90%)
            );
        }

        .ability-card-content {
            border-left: 3px solid $system-red;
        }

        .section-title {
            color: $system-red;
        }
    }

    .ability-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        margin: -20px -20px 16px;
        border-radius: 16px 16px 0 0;

        .ability-name {
            font-size: 16px;
            font-weight: 600;
            color: inherit;
        }

        .ability-score {
            font-size: 16px;
            font-weight: 600;
            -webkit-background-clip: text;
            color: inherit;
        }
    }

    .ability-card-content {
        padding: 0 16px;
        margin: 0;

        .section-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                width: 4px;
                height: 16px;
                border-radius: 2px;
                margin-right: 8px;
                background: currentColor;
            }
        }

        .section-content {
            color: $label-primary;
            font-size: 14px;
            line-height: 1.8;
            padding: 16px;
            margin-bottom: 20px;
            background: $system-background-primary;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.06);

            &:last-child {
                margin-bottom: 0;
            }

            &.assessment {
                position: relative;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

                &::before,
                &::after {
                    content: '"';
                    position: absolute;
                    font-size: 32px;
                    color: currentColor;
                    opacity: 0.15;
                    font-family: serif;
                }

                &::before {
                    top: 8px;
                    left: 12px;
                }

                &::after {
                    bottom: 0;
                    right: 12px;
                }
            }
        }
    }

    &:hover {
        transform: translateY(-2px);
    }
}
</style>
