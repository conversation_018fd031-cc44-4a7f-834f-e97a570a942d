<template>
    <nav class="bottom-navigation">
        <div class="nav-container">
            <RouterLink
                v-for="item in navigationItems"
                :key="item.key"
                :to="item.path"
                custom
                v-slot="{ isActive, navigate }"
            >
                <div
                    :class="['nav-item', { active: isActive }]"
                    :style="isActive ? { '--active-color': item.activeColor } : {}"
                    @click="handleNavClick(item, navigate)"
                >
                    <div class="nav-icon">
                        <img
                            :src="isActive ? item.activeImage : item.normalImage"
                            :alt="item.label"
                            class="nav-image"
                        />
                    </div>
                    <span class="nav-label">{{ item.label }}</span>
                </div>
            </RouterLink>
        </div>
    </nav>
</template>

<script setup>
import { ref } from 'vue';

// 导航项配置
const navigationItems = ref([
    {
        key: 'main',
        label: '首页',
        normalImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/home1.png',
        activeImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/home1.png',
        activeColor: '#333333',
        path: '/smart-trainer/main'
    },
    {
        key: 'learn',
        label: '课程',
        normalImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/study2.png',
        activeImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/study2-active.png',
        activeColor: '#E93E43',
        path: '/smart-trainer/learn/home'
    },
    {
        key: 'practice',
        label: '练习',
        normalImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/practice1.png',
        activeImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/practive-active.png',
        activeColor: '#FCA43D',
        path: '/smart-trainer/practice/home'
    },
    {
        key: 'assess',
        label: '考试',
        normalImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/assess1.png',
        activeImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/assess-active.png',
        activeColor: '#52C41A',
        path: '/smart-trainer/assess/home'
    },
    {
        key: 'profile',
        label: '我的',
        normalImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/mine.png',
        activeImage:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/index/mine-active.png',
        activeColor: '#1890FF',
        path: '/smart-trainer/profile'
    }
]);

/**
 * 处理导航点击
 * @param {Object} item - 导航项配置
 * @param {Function} navigate - 路由导航函数
 */
const handleNavClick = (item, navigate) => {
    console.log(`导航到: ${item.label} (${item.path})`);
    navigate();
};
</script>

<style lang="scss" scoped>
.bottom-navigation {
    flex-shrink: 0;
    background: #ffffff;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;

    .nav-container {
        display: flex;
        height: 60px;
        max-width: 100%;
        margin: 0 auto;
        position: relative;

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 6px 4px 4px;
            position: relative;

            .nav-icon {
                position: relative;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                justify-content: center;

                .nav-image {
                    width: 20px;
                    height: 20px;
                    object-fit: contain;
                    transition: all 0.3s ease;
                }
            }

            .nav-label {
                font-size: 12px;
                color: #999999;
                font-weight: 500;
                transition: all 0.3s ease;
                text-align: center;
                line-height: 1.2;
                letter-spacing: 0.2px;
            }

            // 激活状态
            &.active {
                .nav-icon .nav-image {
                    transform: scale(1.1);
                }

                .nav-label {
                    color: var(--active-color, #3b82f6);
                    font-weight: 600;
                }

                // 添加激活状态的底部指示器
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 20px;
                    height: 3px;
                    background: var(--active-color, #3b82f6);
                    border-radius: 2px 2px 0 0;
                    animation: slideUp 0.3s ease;
                }
            }

            // 悬停效果
            &:hover:not(.active) {
                .nav-icon .nav-image {
                    transform: scale(1.05);
                    opacity: 0.8;
                }

                .nav-label {
                    color: #666666;
                }
            }

            // 点击反馈效果
            &:active {
                transform: scale(0.95);
            }
        }
    }
}

// 激活指示器动画
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

// 适配不同屏幕尺寸
@media (max-width: 360px) {
    .bottom-navigation {
        .nav-container {
            height: 55px;

            .nav-item {
                .nav-icon .nav-image {
                    width: 18px;
                    height: 18px;
                }

                .nav-label {
                    font-size: 10px;
                }
            }
        }
    }
}

@media (min-width: 768px) {
    .bottom-navigation {
        .nav-container {
            height: 65px;

            .nav-item {
                .nav-icon .nav-image {
                    width: 22px;
                    height: 22px;
                }

                .nav-label {
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
