<template>
    <div class="assessment-section" v-if="assessments && assessments.length">
        <div class="assessment-header">
            <span class="header-title">AI 评分</span>
            <div class="score-level-tag" :class="scoreLevel">{{ scoreLevelText }}</div>
        </div>
        <div class="total-score-overview">
            <div class="circular-progress-container">
                <svg class="circular-progress" width="120" height="120">
                    <circle
                        class="progress-bg"
                        cx="60"
                        cy="60"
                        r="50"
                        fill="none"
                        stroke="#d1d5db"
                        stroke-width="8"
                    />
                    <circle
                        class="progress-fill"
                        :class="scoreLevel"
                        cx="60"
                        cy="60"
                        r="50"
                        fill="none"
                        stroke-width="8"
                        :stroke-dasharray="circumference"
                        :stroke-dashoffset="strokeDashoffset"
                        stroke-linecap="round"
                        transform="rotate(-90 60 60)"
                    />
                </svg>
                <div class="score-center">
                    <div class="current-score" :class="scoreLevel">{{ totalCurrentScore }}</div>
                    <div class="max-score">/ {{ totalMaxScore }} 分</div>
                </div>
            </div>
        </div>
        <div class="assessment-content">
            <div
                class="criterion-item"
                v-for="(criterion, index) in assessments"
                :key="criterion.id"
                :style="{
                    animationDelay: `${index * 0.1}s`
                }"
            >
                <div class="criterion-icon">
                    <i
                        :class="getModernIcon(index).icon"
                        :style="{ color: getModernIcon(index).color }"
                    ></i>
                </div>
                <div class="criterion-content">
                    <div class="criterion-title">评分项 {{ index + 1 }}</div>
                    <div class="criterion-description">{{ criterion.assessmentText }}</div>
                </div>
                <div class="criterion-score">
                    <span
                        class="current-score-text"
                        :style="{ color: getScoreColor(criterion.score, criterion.criterionScore) }"
                    >
                        {{ criterion.score || 0 }}
                    </span>
                    <span class="score-separator"> / </span>
                    <span class="max-score-text">{{ criterion.criterionScore }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getScoreClass, getScoreLevelText, SCORE_COLORS } from '../constants/score';

const props = defineProps({
    assessments: {
        type: Array,
        default: () => []
    }
});

// 定义考试维度相关的现代化图标和颜色配置
const MODERN_ICONS = [
    { icon: 'pi pi-book', color: '#6366f1' }, // 知识理解 - 紫色
    { icon: 'pi pi-lightbulb', color: '#f59e0b' }, // 创新思维 - 橙色
    { icon: 'pi pi-chart-line', color: '#10b981' }, // 分析能力 - 绿色
    { icon: 'pi pi-pencil', color: '#ec4899' }, // 表达能力 - 粉色
    { icon: 'pi pi-verified', color: '#06b6d4' } // 综合表现 - 青色
];

// 计算总分和总满分
const totalCurrentScore = computed(() => {
    return props.assessments.reduce((sum, item) => sum + item.score, 0);
});

const totalMaxScore = computed(() => {
    return props.assessments.reduce((sum, item) => sum + item.criterionScore, 0);
});

// 计算得分百分比
const scorePercentage = computed(() => {
    return (totalCurrentScore.value / totalMaxScore.value) * 100;
});

// 根据得分确定等级和对应的样式类名
const scoreLevel = computed(() => getScoreClass(totalCurrentScore.value, totalMaxScore.value));

// 根据等级返回对应的文本
const scoreLevelText = computed(() =>
    getScoreLevelText(totalCurrentScore.value, totalMaxScore.value)
);

// 圆形进度条相关计算
const circumference = computed(() => 2 * Math.PI * 50); // r=50
const strokeDashoffset = computed(() => {
    const progress = scorePercentage.value / 100;
    return circumference.value * (1 - progress);
});

// 根据索引获取现代化图标
const getModernIcon = index => {
    const iconIndex = index % MODERN_ICONS.length;
    return MODERN_ICONS[iconIndex];
};

// 根据分数获取颜色（用于 criterion.score）
const getScoreColor = (score, maxScore) => {
    const scoreClass = getScoreClass(score, maxScore);
    return SCORE_COLORS[scoreClass].text;
};
</script>

<style lang="scss" scoped>
.assessment-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(67, 24, 255, 0.08);
    overflow: hidden;
    position: relative;

    .assessment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 16px;
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);

        .header-title {
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 18px;
            font-weight: 600;
        }

        .score-level-tag {
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 16px;
            font-weight: 500;

            &.fail {
                background-color: rgba(255, 77, 79, 0.1);
                color: #ff4d4f;
            }

            &.pass {
                background-color: rgba(82, 196, 26, 0.1);
                color: #52c41a;
            }

            &.excellent {
                background-color: rgba(22, 119, 255, 0.1);
                color: #1677ff;
            }

            &.perfect {
                background-color: rgba(250, 173, 20, 0.1);
                color: #faad14;
            }
        }
    }

    .total-score-overview {
        padding: 16px;
        display: flex;
        justify-content: center;

        .circular-progress-container {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .circular-progress {
                .progress-fill {
                    transition: stroke-dashoffset 0.6s ease;

                    &.fail {
                        stroke: #ff4d4f;
                    }

                    &.pass {
                        stroke: #52c41a;
                    }

                    &.excellent {
                        stroke: #1677ff;
                    }

                    &.perfect {
                        stroke: #faad14;
                    }
                }
            }

            .score-center {
                position: absolute;
                text-align: center;

                .current-score {
                    font-size: 28px;
                    font-weight: 600;
                    line-height: 1;

                    &.fail {
                        color: v-bind('SCORE_COLORS.fail.text');
                    }

                    &.pass {
                        color: v-bind('SCORE_COLORS.pass.text');
                    }

                    &.excellent {
                        color: v-bind('SCORE_COLORS.excellent.text');
                    }

                    &.perfect {
                        color: v-bind('SCORE_COLORS.perfect.text');
                    }
                }

                .max-score {
                    margin-top: 4px;
                    font-size: 14px;
                    color: $label-secondary;
                    font-weight: 500;
                }
            }
        }
    }

    .assessment-content {
        padding: 0 16px 0 6px;

        .criterion-item {
            display: flex;
            animation: slideIn 0.3s ease forwards;
            opacity: 0;
            margin-bottom: 12px;

            .criterion-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;

                i {
                    font-size: 20px;
                }
            }

            .criterion-content {
                flex: 1;
                margin-right: 16px;

                .criterion-title {
                    font-size: 14px;
                    font-weight: 400;
                    color: $label-primary;
                    margin-bottom: 4px;
                }

                .criterion-description {
                    font-size: 12px;
                    color: $label-secondary;
                    line-height: 1.4;
                    font-weight: 600;
                }
            }

            .criterion-score {
                font-size: 16px;
                font-weight: 600;
                flex-shrink: 0;
                display: flex;

                .current-score-text {
                    font-weight: 600;
                    margin: 0 4px;
                    font-size: 18px;
                }

                .score-separator {
                    margin-top: 2px;
                    color: $label-secondary;
                }

                .max-score-text {
                    color: $label-secondary;
                    font-weight: 400;
                    font-size: 14px;
                    margin: 3px 0 0 2px;
                }
            }
        }
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
