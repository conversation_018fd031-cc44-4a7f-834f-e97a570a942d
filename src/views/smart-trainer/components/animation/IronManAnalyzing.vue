<template>
    <div class="ironman-analyzing">
        <div class="analyzing-container">
            <!-- 钢铁侠全息投影分析动效 -->
            <div class="hologram-animation">
                <div class="arc-reactor">
                    <div class="reactor-core"></div>
                    <div class="reactor-ring"></div>
                    <div class="reactor-glow"></div>
                </div>

                <!-- 全息投影扫描效果 -->
                <div class="hologram-container">
                    <div v-for="n in 12" :key="n" class="hologram-circle"></div>
                    <div v-for="n in 8" :key="`line-${n}`" class="hologram-line"></div>
                    <div class="data-particles">
                        <div v-for="n in 20" :key="`particle-${n}`" class="data-particle"></div>
                    </div>
                </div>

                <!-- 扫描光束 -->
                <div class="scan-beam"></div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = [
    '正在分析语音内容',
    '评估回答完整度',
    '计算专业度得分',
    '分析表达流畅度',
    '生成综合评价'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
});
</script>

<style lang="scss" scoped>
.ironman-analyzing {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #111111, #222222);
    color: #fff;
    margin: 12px auto;
    box-shadow: 0 8px 24px rgba(229, 57, 53, 0.3);
    border: 1px solid rgba(229, 57, 53, 0.2);

    .analyzing-container {
        .hologram-animation {
            position: relative;
            height: 160px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            perspective: 1000px;
            overflow: hidden;
            background: linear-gradient(
                180deg,
                rgba(229, 57, 53, 0.08) 0%,
                rgba(255, 187, 0, 0.05) 100%
            );
            border-radius: 12px;

            // 钢铁侠方舟反应堆
            .arc-reactor {
                position: absolute;
                width: 70px;
                height: 70px;
                border-radius: 50%;
                background: #111;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2;
                box-shadow: 0 0 30px rgba(229, 57, 53, 0.4);
                border: 2px solid rgba(255, 187, 0, 0.3);

                .reactor-core {
                    width: 30px;
                    height: 30px;
                    background: #fff;
                    border-radius: 50%;
                    box-shadow:
                        0 0 20px rgba(255, 255, 255, 0.9),
                        0 0 40px rgba(229, 57, 53, 0.8);
                    animation: pulseCore 2s ease-in-out infinite;
                }

                .reactor-ring {
                    position: absolute;
                    width: 50px;
                    height: 50px;
                    border: 3px solid rgba(229, 57, 53, 0.9);
                    border-radius: 50%;
                    animation: rotateRing 4s linear infinite;
                    box-shadow: 0 0 15px rgba(229, 57, 53, 0.6);
                }

                .reactor-glow {
                    position: absolute;
                    width: 80px;
                    height: 80px;
                    background: radial-gradient(circle, rgba(229, 57, 53, 0.8) 0%, transparent 70%);
                    border-radius: 50%;
                    animation: pulseGlow 2s ease-in-out infinite;
                }
            }

            // 全息投影效果
            .hologram-container {
                position: absolute;
                width: 100%;
                height: 100%;
                transform-style: preserve-3d;

                .hologram-circle {
                    position: absolute;
                    border: 1px solid rgba(255, 187, 0, 0.4);
                    border-radius: 50%;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    opacity: 0;

                    @for $i from 1 through 12 {
                        &:nth-child(#{$i}) {
                            width: #{10 + $i * 10}px;
                            height: #{10 + $i * 10}px;
                            animation: expandCircle 3s ease-in-out #{$i * 0.2}s infinite;
                        }
                    }
                }

                .hologram-line {
                    position: absolute;
                    height: 1px;
                    background: linear-gradient(
                        90deg,
                        transparent,
                        rgba(229, 57, 53, 0.6),
                        rgba(255, 187, 0, 0.6),
                        transparent
                    );
                    left: 0;
                    right: 0;
                    top: 50%;
                    transform-origin: center;

                    @for $i from 1 through 8 {
                        &:nth-child(#{$i}) {
                            transform: translateY(-50%) rotate(#{$i * 22.5}deg);
                            animation: pulseLine 2s ease-in-out #{$i * 0.1}s infinite;
                        }
                    }
                }

                .data-particles {
                    position: absolute;
                    width: 100%;
                    height: 100%;

                    .data-particle {
                        position: absolute;
                        width: 3px;
                        height: 3px;
                        background: #ffb300;
                        border-radius: 50%;

                        // 使用预定义的位置替代随机函数
                        &:nth-child(1) {
                            left: 15%;
                            top: 25%;
                            opacity: 0.7;
                            animation: moveParticle 4s linear 0.1s infinite;
                        }
                        &:nth-child(2) {
                            left: 35%;
                            top: 45%;
                            opacity: 0.3;
                            animation: moveParticle 4s linear 0.2s infinite;
                        }
                        &:nth-child(3) {
                            left: 65%;
                            top: 15%;
                            opacity: 0.5;
                            animation: moveParticle 4s linear 0.3s infinite;
                        }
                        &:nth-child(4) {
                            left: 85%;
                            top: 75%;
                            opacity: 0.8;
                            animation: moveParticle 4s linear 0.4s infinite;
                        }
                        &:nth-child(5) {
                            left: 25%;
                            top: 85%;
                            opacity: 0.4;
                            animation: moveParticle 4s linear 0.5s infinite;
                        }
                        &:nth-child(6) {
                            left: 50%;
                            top: 50%;
                            opacity: 0.6;
                            animation: moveParticle 4s linear 0.6s infinite;
                        }
                        &:nth-child(7) {
                            left: 75%;
                            top: 35%;
                            opacity: 0.2;
                            animation: moveParticle 4s linear 0.7s infinite;
                        }
                        &:nth-child(8) {
                            left: 10%;
                            top: 60%;
                            opacity: 0.9;
                            animation: moveParticle 4s linear 0.8s infinite;
                        }
                        &:nth-child(9) {
                            left: 45%;
                            top: 20%;
                            opacity: 0.5;
                            animation: moveParticle 4s linear 0.9s infinite;
                        }
                        &:nth-child(10) {
                            left: 90%;
                            top: 30%;
                            opacity: 0.7;
                            animation: moveParticle 4s linear 1s infinite;
                        }
                        &:nth-child(11) {
                            left: 20%;
                            top: 40%;
                            opacity: 0.3;
                            animation: moveParticle 4s linear 1.1s infinite;
                        }
                        &:nth-child(12) {
                            left: 60%;
                            top: 80%;
                            opacity: 0.6;
                            animation: moveParticle 4s linear 1.2s infinite;
                        }
                        &:nth-child(13) {
                            left: 30%;
                            top: 70%;
                            opacity: 0.8;
                            animation: moveParticle 4s linear 1.3s infinite;
                        }
                        &:nth-child(14) {
                            left: 80%;
                            top: 10%;
                            opacity: 0.4;
                            animation: moveParticle 4s linear 1.4s infinite;
                        }
                        &:nth-child(15) {
                            left: 40%;
                            top: 65%;
                            opacity: 0.7;
                            animation: moveParticle 4s linear 1.5s infinite;
                        }
                        &:nth-child(16) {
                            left: 70%;
                            top: 55%;
                            opacity: 0.3;
                            animation: moveParticle 4s linear 1.6s infinite;
                        }
                        &:nth-child(17) {
                            left: 5%;
                            top: 95%;
                            opacity: 0.5;
                            animation: moveParticle 4s linear 1.7s infinite;
                        }
                        &:nth-child(18) {
                            left: 95%;
                            top: 5%;
                            opacity: 0.9;
                            animation: moveParticle 4s linear 1.8s infinite;
                        }
                        &:nth-child(19) {
                            left: 55%;
                            top: 30%;
                            opacity: 0.2;
                            animation: moveParticle 4s linear 1.9s infinite;
                        }
                        &:nth-child(20) {
                            left: 15%;
                            top: 50%;
                            opacity: 0.6;
                            animation: moveParticle 4s linear 2s infinite;
                        }
                    }
                }
            }

            .scan-beam {
                position: absolute;
                width: 120%;
                height: 4px;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(229, 57, 53, 0.8),
                    rgba(255, 187, 0, 0.8),
                    transparent
                );
                animation: scanBeam 3s linear infinite;
            }
        }

        .analyzing-text {
            margin-top: 8px;
            text-align: center;

            .text-container {
                margin-bottom: 8px;
                font-size: 13px;
                color: #e6e6e6;
                font-family: 'Roboto', sans-serif;
                letter-spacing: 0.5px;

                .current-step {
                    color: #ffb300;
                    font-weight: 500;
                }
            }

            .progress-bar {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #e53935, #ffb300);
                    transition: width 0.3s ease;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 20px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

// 钢铁侠方舟反应堆动画
@keyframes pulseCore {
    0%,
    100% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes rotateRing {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulseGlow {
    0%,
    100% {
        opacity: 0.6;
        transform: scale(0.9);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

// 全息投影动画
@keyframes expandCircle {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0.8;
        border-color: rgba(229, 57, 53, 0.7);
    }
    50% {
        border-color: rgba(255, 187, 0, 0.5);
    }
    70% {
        opacity: 0.3;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0;
        border-color: rgba(255, 187, 0, 0.2);
    }
}

@keyframes pulseLine {
    0% {
        opacity: 0.2;
        height: 1px;
    }
    50% {
        opacity: 0.8;
        height: 2px;
    }
    100% {
        opacity: 0.2;
        height: 1px;
    }
}

@keyframes moveParticle {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 0.2;
    }
    25% {
        transform: translate(15px, 15px) scale(1.5);
        opacity: 0.8;
    }
    50% {
        transform: translate(0, 30px) scale(1);
        opacity: 0.5;
    }
    75% {
        transform: translate(-15px, 15px) scale(1.5);
        opacity: 0.8;
    }
    100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.2;
    }
}

// 扫描光束动画
@keyframes scanBeam {
    0% {
        transform: translateY(-100%) rotate(-30deg);
        opacity: 0;
        height: 6px;
    }
    20% {
        opacity: 1;
    }
    50% {
        height: 4px;
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateY(100%) rotate(-30deg);
        opacity: 0;
        height: 6px;
    }
}

// 进度条闪光效果
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 优化移动端性能
@media (max-width: 768px) {
    .hologram-animation {
        .arc-reactor {
            transform: scale(0.8);
        }

        .hologram-container {
            transform: scale(0.9);
        }
    }
}
</style>
