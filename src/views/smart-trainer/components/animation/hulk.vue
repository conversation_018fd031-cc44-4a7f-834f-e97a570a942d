<template>
    <div class="hulk-analyzing">
        <div class="analyzing-container">
            <!-- 绿巨人能量分析动效 -->
            <div class="energy-animation">
                <!-- 绿巨人能量核心 -->
                <div class="energy-core">
                    <div class="core-inner"></div>
                    <div class="core-pulse"></div>
                    <div class="core-shockwave"></div>
                </div>

                <!-- 能量波纹效果 -->
                <div class="energy-waves">
                    <div v-for="n in 5" :key="`wave-${n}`" class="energy-wave"></div>
                </div>

                <!-- 能量裂痕 -->
                <div class="energy-cracks">
                    <div v-for="n in 8" :key="`crack-${n}`" class="energy-crack"></div>
                </div>

                <!-- 伽马射线粒子 -->
                <div class="gamma-particles">
                    <div v-for="n in 15" :key="`gamma-${n}`" class="gamma-particle"></div>
                </div>

                <!-- 能量冲击波 -->
                <div class="shockwave-ring"></div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = [
    '正在分析语音内容',
    '评估回答完整度',
    '计算专业度得分',
    '分析表达流畅度',
    '生成综合评价'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
});
</script>

<style lang="scss" scoped>
.hulk-analyzing {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #1a2f1a, #2a3a2a);
    color: #fff;
    margin: 12px auto;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    border: 1px solid rgba(76, 175, 80, 0.2);

    .analyzing-container {
        .energy-animation {
            position: relative;
            height: 160px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: linear-gradient(
                180deg,
                rgba(76, 175, 80, 0.05) 0%,
                rgba(0, 200, 83, 0.02) 100%
            );
            border-radius: 12px;

            // 浩克能量核心 - 更加爆发性
            .energy-core {
                position: absolute;
                width: 70px;
                height: 70px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2;

                .core-inner {
                    width: 40px;
                    height: 40px;
                    background: #4caf50;
                    border-radius: 50%;
                    box-shadow:
                        0 0 30px rgba(76, 175, 80, 0.8),
                        inset 0 0 15px rgba(255, 255, 255, 0.6);
                    animation: hulkPulseCore 1.2s ease-in-out infinite alternate;
                }

                .core-pulse {
                    position: absolute;
                    width: 60px;
                    height: 60px;
                    border: 4px solid rgba(76, 175, 80, 0.8);
                    border-radius: 50%;
                    animation: hulkPulseBorder 1s ease-out infinite;
                }

                .core-shockwave {
                    position: absolute;
                    width: 80px;
                    height: 80px;
                    background: radial-gradient(circle, rgba(76, 175, 80, 0.7) 0%, transparent 70%);
                    border-radius: 50%;
                    animation: hulkShockwave 1.5s ease-in-out infinite alternate;
                }
            }

            // 能量波纹效果 - 更加爆裂
            .energy-waves {
                position: absolute;
                width: 100%;
                height: 100%;

                .energy-wave {
                    position: absolute;
                    border: 3px solid rgba(76, 175, 80, 0.5);
                    border-radius: 50%;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    opacity: 0;

                    @for $i from 1 through 5 {
                        &:nth-child(#{$i}) {
                            width: #{30 + $i * 35}px;
                            height: #{30 + $i * 35}px;
                            animation: hulkExpandWave
                                2.5s
                                cubic-bezier(0.12, 0.81, 0.16, 1)
                                #{$i *
                                0.2}s
                                infinite;
                        }
                    }
                }
            }

            // 能量裂痕 - 更像浩克的破坏力
            .energy-cracks {
                position: absolute;
                width: 100%;
                height: 100%;

                .energy-crack {
                    position: absolute;
                    height: 3px;
                    background: linear-gradient(
                        90deg,
                        transparent,
                        #4caf50,
                        #8bc34a,
                        #fff,
                        transparent
                    );
                    left: 50%;
                    top: 50%;
                    transform-origin: left center;

                    @for $i from 1 through 8 {
                        &:nth-child(#{$i}) {
                            width: #{50 + $i * 15}px;
                            transform: rotate(#{$i * 45}deg);
                            opacity: 0.7;
                            animation: hulkCrack 1.8s ease-in-out #{$i * 0.1}s infinite alternate;
                        }
                    }
                }
            }

            // 伽马射线粒子 - 更加活跃
            .gamma-particles {
                position: absolute;
                width: 100%;
                height: 100%;

                .gamma-particle {
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    background: #8bc34a;
                    border-radius: 50%;
                    box-shadow: 0 0 8px #4caf50;

                    // 预设粒子位置 - 使用固定值替代随机值
                    &:nth-child(1) {
                        left: 25%;
                        top: 30%;
                        animation: hulkGammaParticle 2.5s ease-in-out 0.1s infinite;
                    }
                    &:nth-child(2) {
                        left: 75%;
                        top: 65%;
                        animation: hulkGammaParticle 3.2s ease-in-out 0.3s infinite;
                    }
                    &:nth-child(3) {
                        left: 35%;
                        top: 85%;
                        animation: hulkGammaParticle 2.8s ease-in-out 0.5s infinite;
                    }
                    &:nth-child(4) {
                        left: 60%;
                        top: 20%;
                        animation: hulkGammaParticle 3.5s ease-in-out 0.2s infinite;
                    }
                    &:nth-child(5) {
                        left: 15%;
                        top: 45%;
                        animation: hulkGammaParticle 2.3s ease-in-out 0.7s infinite;
                    }
                    &:nth-child(6) {
                        left: 85%;
                        top: 35%;
                        animation: hulkGammaParticle 3s ease-in-out 0.4s infinite;
                    }
                    &:nth-child(7) {
                        left: 45%;
                        top: 15%;
                        animation: hulkGammaParticle 2.7s ease-in-out 0.6s infinite;
                    }
                    &:nth-child(8) {
                        left: 70%;
                        top: 80%;
                        animation: hulkGammaParticle 3.3s ease-in-out 0.2s infinite;
                    }
                    &:nth-child(9) {
                        left: 20%;
                        top: 70%;
                        animation: hulkGammaParticle 2.9s ease-in-out 0.5s infinite;
                    }
                    &:nth-child(10) {
                        left: 90%;
                        top: 50%;
                        animation: hulkGammaParticle 3.1s ease-in-out 0.3s infinite;
                    }
                    &:nth-child(11) {
                        left: 30%;
                        top: 60%;
                        animation: hulkGammaParticle 2.6s ease-in-out 0.7s infinite;
                    }
                    &:nth-child(12) {
                        left: 80%;
                        top: 25%;
                        animation: hulkGammaParticle 3.4s ease-in-out 0.1s infinite;
                    }
                    &:nth-child(13) {
                        left: 40%;
                        top: 40%;
                        animation: hulkGammaParticle 2.2s ease-in-out 0.4s infinite;
                    }
                    &:nth-child(14) {
                        left: 65%;
                        top: 55%;
                        animation: hulkGammaParticle 3.6s ease-in-out 0.6s infinite;
                    }
                    &:nth-child(15) {
                        left: 55%;
                        top: 75%;
                        animation: hulkGammaParticle 2.4s ease-in-out 0.8s infinite;
                    }
                }
            }

            // 能量冲击波 - 更加强烈
            .shockwave-ring {
                position: absolute;
                width: 100px;
                height: 100px;
                border: 5px solid rgba(76, 175, 80, 0.6);
                border-radius: 50%;
                animation: hulkShockwaveExpand 2s cubic-bezier(0.15, 0.86, 0.35, 1.2) infinite;
            }

            // 添加浩克拳头冲击效果
            &::before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: radial-gradient(
                    circle at center,
                    rgba(76, 175, 80, 0.2) 0%,
                    transparent 70%
                );
                animation: hulkSmash 3s ease-in-out infinite;
            }
        }

        .analyzing-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 10px;
                font-size: 14px;
                color: #e6e6e6;
                font-family: 'Roboto', sans-serif;
                letter-spacing: 0.5px;

                .current-step {
                    color: #4caf50;
                    font-weight: 600;
                    text-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
                }
            }

            .progress-bar {
                height: 6px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
                overflow: hidden;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #388e3c, #8bc34a);
                    transition: width 0.3s ease;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 30px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

// 浩克能量核心动画 - 更加爆发性
@keyframes hulkPulseCore {
    0% {
        transform: scale(0.85);
        opacity: 0.8;
        box-shadow:
            0 0 15px rgba(76, 175, 80, 0.6),
            inset 0 0 10px rgba(255, 255, 255, 0.4);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1.3);
        opacity: 1;
        box-shadow:
            0 0 40px rgba(76, 175, 80, 1),
            inset 0 0 20px rgba(255, 255, 255, 0.8);
    }
}

@keyframes hulkPulseBorder {
    0% {
        transform: scale(0.7);
        opacity: 0.9;
    }
    50% {
        opacity: 0.6;
    }
    100% {
        transform: scale(1.8);
        opacity: 0;
    }
}

@keyframes hulkShockwave {
    0% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% {
        opacity: 0.6;
    }
    100% {
        opacity: 0.9;
        transform: scale(1.5);
    }
}

// 能量波纹动画 - 更加爆裂
@keyframes hulkExpandWave {
    0% {
        transform: translate(-50%, -50%) scale(0.2);
        opacity: 0.9;
        border-width: 4px;
    }
    40% {
        opacity: 0.7;
    }
    70% {
        opacity: 0.4;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.8);
        opacity: 0;
        border-width: 1px;
    }
}

// 能量裂痕动画 - 更像浩克的破坏力
@keyframes hulkCrack {
    0% {
        opacity: 0.4;
        width: 60%;
        height: 3px;
    }
    50% {
        opacity: 0.9;
        height: 4px;
    }
    100% {
        opacity: 0.6;
        width: 110%;
        height: 3px;
    }
}

// 伽马射线粒子动画 - 更加活跃
@keyframes hulkGammaParticle {
    0% {
        transform: scale(0.8) translate(0, 0);
        opacity: 0.3;
    }
    25% {
        transform: scale(1.8) translate(20px, -15px);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.4) translate(25px, 20px);
        opacity: 0.7;
    }
    75% {
        transform: scale(2) translate(-15px, 25px);
        opacity: 1;
    }
    100% {
        transform: scale(0.8) translate(0, 0);
        opacity: 0.3;
    }
}

// 能量冲击波动画 - 更加强烈
@keyframes hulkShockwaveExpand {
    0% {
        transform: scale(0.4);
        opacity: 0.9;
        border-width: 6px;
    }
    50% {
        opacity: 0.6;
        border-width: 4px;
    }
    100% {
        transform: scale(2.8);
        opacity: 0;
        border-width: 1px;
    }
}

// 浩克拳头冲击效果
@keyframes hulkSmash {
    0%,
    100% {
        transform: scale(1);
        opacity: 0.1;
    }
    30% {
        transform: scale(1.1);
        opacity: 0.3;
    }
    50% {
        transform: scale(0.95);
        opacity: 0.2;
    }
    70% {
        transform: scale(1.2);
        opacity: 0.4;
    }
}

// 进度条闪光效果
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 优化移动端性能
@media (max-width: 768px) {
    .energy-animation {
        .energy-core {
            transform: scale(0.8);
        }

        .energy-waves {
            transform: scale(0.9);
        }
    }
}
</style>
