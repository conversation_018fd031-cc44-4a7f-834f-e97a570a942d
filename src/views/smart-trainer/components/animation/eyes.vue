<template>
    <div class="eyes-tracking">
        <div class="tracking-container">
            <!-- 眼睛追踪动画 -->
            <div class="eye-animation">
                <!-- 眼睛扫描区域 -->
                <div class="eye-scanner">
                    <div class="iris">
                        <div class="pupil"></div>
                        <div class="scan-lines"></div>
                    </div>
                    <div class="targeting-grid">
                        <div v-for="n in 4" :key="`h-${n}`" class="grid-line horizontal"></div>
                        <div v-for="n in 4" :key="`v-${n}`" class="grid-line vertical"></div>
                        <div class="target-point" :style="targetPointStyle"></div>
                    </div>
                </div>

                <!-- 数据分析波形 -->
                <div class="data-waves">
                    <div v-for="n in 5" :key="`wave-${n}`" class="wave" :class="`wave-${n}`"></div>
                </div>
            </div>

            <!-- 分析进度文本 -->
            <div class="tracking-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';

// 分析步骤
const steps = [
    '正在追踪眼球运动',
    '分析注视点分布',
    '计算视线停留时长',
    '评估阅读流畅度',
    '生成视觉专注报告'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;
let targetMoveInterval = null;

// 目标点位置
const targetX = ref(50);
const targetY = ref(50);

// 计算目标点样式
const targetPointStyle = computed(() => {
    return {
        left: `${targetX.value}%`,
        top: `${targetY.value}%`
    };
});

// 随机移动目标点
const moveTargetPoint = () => {
    targetX.value = Math.floor(Math.random() * 80) + 10; // 10-90%范围内
    targetY.value = Math.floor(Math.random() * 80) + 10;
};

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);

    // 移动目标点
    targetMoveInterval = setInterval(() => {
        moveTargetPoint();
    }, 2000);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
    clearInterval(targetMoveInterval);
});
</script>

<style lang="scss" scoped>
.eyes-tracking {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #1e2235, #2c3347);
    color: #fff;
    margin: 12px auto;

    .tracking-container {
        .eye-animation {
            position: relative;
            height: 160px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: linear-gradient(
                180deg,
                rgba(255, 87, 34, 0.05) 0%,
                rgba(255, 87, 34, 0.02) 100%
            );
            border-radius: 12px;

            .eye-scanner {
                position: relative;
                width: 120px;
                height: 120px;
                display: flex;
                align-items: center;
                justify-content: center;

                .iris {
                    width: 80px;
                    height: 80px;
                    border-radius: 50%;
                    background: radial-gradient(circle, #ff5722 0%, #d84315 70%);
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 0 20px rgba(255, 87, 34, 0.6);
                    animation: pulseIris 3s ease-in-out infinite;

                    .pupil {
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        background: #000;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.8) inset;
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            top: 20%;
                            left: 20%;
                            width: 8px;
                            height: 8px;
                            border-radius: 50%;
                            background: rgba(255, 255, 255, 0.8);
                        }
                    }

                    .scan-lines {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background: repeating-linear-gradient(
                            0deg,
                            transparent,
                            transparent 2px,
                            rgba(255, 255, 255, 0.1) 2px,
                            rgba(255, 255, 255, 0.1) 4px
                        );
                        animation: rotateScan 8s linear infinite;
                    }
                }

                .targeting-grid {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    border: 2px solid rgba(255, 87, 34, 0.6);
                    border-radius: 50%;

                    .grid-line {
                        position: absolute;
                        background: rgba(255, 87, 34, 0.3);

                        &.horizontal {
                            width: 100%;
                            height: 1px;
                            left: 0;

                            &:nth-child(1) {
                                top: 20%;
                            }
                            &:nth-child(2) {
                                top: 40%;
                            }
                            &:nth-child(3) {
                                top: 60%;
                            }
                            &:nth-child(4) {
                                top: 80%;
                            }
                        }

                        &.vertical {
                            width: 1px;
                            height: 100%;
                            top: 0;

                            &:nth-child(5) {
                                left: 20%;
                            }
                            &:nth-child(6) {
                                left: 40%;
                            }
                            &:nth-child(7) {
                                left: 60%;
                            }
                            &:nth-child(8) {
                                left: 80%;
                            }
                        }
                    }

                    .target-point {
                        position: absolute;
                        width: 10px;
                        height: 10px;
                        background: rgba(255, 255, 255, 0.8);
                        border-radius: 50%;
                        transform: translate(-50%, -50%);
                        box-shadow: 0 0 10px #ff5722;

                        &::before,
                        &::after {
                            content: '';
                            position: absolute;
                            background: rgba(255, 87, 34, 0.6);
                        }

                        &::before {
                            width: 20px;
                            height: 2px;
                            top: 4px;
                            left: -5px;
                        }

                        &::after {
                            width: 2px;
                            height: 20px;
                            top: -5px;
                            left: 4px;
                        }
                    }
                }
            }

            .data-waves {
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 40px;
                display: flex;
                align-items: flex-end;

                .wave {
                    flex: 1;
                    height: 100%;
                    margin: 0 1px;
                    background: rgba(255, 87, 34, 0.3);
                    transform-origin: bottom;

                    @for $i from 1 through 5 {
                        &.wave-#{$i} {
                            animation: waveAnimation 1.2s ease-in-out infinite;
                            animation-delay: $i * 0.1s;
                        }
                    }
                }
            }
        }

        .tracking-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 8px;
                font-size: 13px;
                color: #e6e6e6;

                .current-step {
                    color: #ff5722;
                    font-weight: 500;
                }
            }

            .progress-bar {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #ff5722, #ff9800);
                    transition: width 0.3s ease;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 20px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

@keyframes pulseIris {
    0%,
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 15px rgba(255, 87, 34, 0.6);
    }
    50% {
        transform: scale(1);
        box-shadow: 0 0 25px rgba(255, 87, 34, 0.8);
    }
}

@keyframes rotateScan {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes waveAnimation {
    0%,
    100% {
        transform: scaleY(0.2);
    }
    50% {
        transform: scaleY(0.8 + (random(5) / 10));
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 优化移动端性能
@media (max-width: 768px) {
    .eye-animation {
        .eye-scanner {
            transform: scale(0.8);
        }
    }
}
</style>
