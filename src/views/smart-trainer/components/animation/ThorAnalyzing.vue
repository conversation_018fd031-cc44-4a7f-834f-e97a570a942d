<template>
    <div class="hero-analyzing thor-theme">
        <div class="analyzing-container">
            <!-- 雷电风暴动画 -->
            <div class="storm-animation">
                <!-- 云层效果 -->
                <div class="clouds-container">
                    <div class="cloud cloud-1"></div>
                    <div class="cloud cloud-2"></div>
                    <div class="cloud cloud-3"></div>
                </div>

                <!-- 闪电效果 -->
                <div class="lightning-container">
                    <div
                        v-for="n in 8"
                        :key="`lightning-${n}`"
                        class="lightning"
                        :class="`lightning-${n}`"
                    ></div>
                </div>

                <!-- 能量中心 -->
                <div class="energy-core">
                    <div class="core-inner"></div>
                    <div class="core-outer"></div>
                </div>

                <!-- 能量波纹 -->
                <div class="energy-ripples">
                    <div
                        v-for="n in 4"
                        :key="`ripple-${n}`"
                        class="ripple"
                        :class="`ripple-${n}`"
                    ></div>
                </div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = [
    '正在分析语音内容',
    '评估回答完整度',
    '计算专业度得分',
    '分析表达流畅度',
    '生成综合评价'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;
let lightningInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

// 随机生成闪电
const generateLightning = () => {
    const lightnings = document.querySelectorAll('.lightning');
    // 随机选择1-3个闪电同时显示
    const count = Math.floor(Math.random() * 3) + 1;
    const indices = [];

    while (indices.length < count) {
        const idx = Math.floor(Math.random() * lightnings.length);
        if (!indices.includes(idx)) {
            indices.push(idx);
        }
    }

    indices.forEach((idx) => {
        const lightning = lightnings[idx];
        lightning.classList.add('active');

        // 随机调整闪电宽度，增加变化
        const width = 2 + Math.random() * 2;
        lightning.style.width = `${width}px`;

        // 随机调整闪电高度
        const height = 80 + Math.random() * 40;
        lightning.style.setProperty('--lightning-height', `${height}px`);

        setTimeout(() => {
            lightning.classList.remove('active');
        }, 300);
    });
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);

    // 随机闪电效果
    lightningInterval = setInterval(() => {
        generateLightning();
    }, 600);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
    clearInterval(lightningInterval);
});
</script>

<style lang="scss" scoped>
.hero-analyzing.thor-theme {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #1a2a4a, #2c3e67);
    color: #fff;
    margin: 12px auto;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

    .analyzing-container {
        .storm-animation {
            position: relative;
            height: 180px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: linear-gradient(
                180deg,
                rgba(20, 30, 60, 0.9) 0%,
                rgba(40, 70, 130, 0.7) 100%
            );
            border-radius: 12px;

            // 云层效果
            .clouds-container {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;

                .cloud {
                    position: absolute;
                    background: rgba(255, 255, 255, 0.15);
                    border-radius: 50%;
                    filter: blur(10px);

                    &.cloud-1 {
                        width: 120px;
                        height: 40px;
                        top: 20%;
                        left: 10%;
                        animation: cloudMove 15s linear infinite;
                        opacity: 0.7;
                    }

                    &.cloud-2 {
                        width: 150px;
                        height: 50px;
                        top: 40%;
                        right: 5%;
                        animation: cloudMove 18s linear infinite reverse;
                        opacity: 0.5;
                    }

                    &.cloud-3 {
                        width: 100px;
                        height: 35px;
                        bottom: 25%;
                        left: 30%;
                        animation: cloudMove 20s linear infinite;
                        opacity: 0.6;
                    }
                }
            }

            // 闪电效果
            .lightning-container {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: 10;

                .lightning {
                    position: absolute;
                    width: 3px;
                    height: 0;
                    background: rgba(255, 255, 255, 0.9);
                    box-shadow:
                        0 0 10px #64b5f6,
                        0 0 20px #2196f3,
                        0 0 30px #1976d2;
                    opacity: 0;
                    transform-origin: top center;
                    clip-path: polygon(
                        0 0,
                        100% 0,
                        90% 20%,
                        100% 20%,
                        80% 40%,
                        100% 40%,
                        90% 60%,
                        100% 60%,
                        80% 80%,
                        100% 100%,
                        0 100%
                    );

                    &.active {
                        opacity: 1;
                        animation: lightningFlash 0.3s ease-out;
                    }

                    &.lightning-1 {
                        top: 0;
                        left: 30%;
                        transform: rotate(10deg);
                    }

                    &.lightning-2 {
                        top: 0;
                        left: 45%;
                        transform: rotate(-5deg);
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            80% 15%,
                            100% 15%,
                            90% 35%,
                            100% 35%,
                            85% 55%,
                            100% 55%,
                            90% 75%,
                            100% 100%,
                            0 100%
                        );
                    }

                    &.lightning-3 {
                        top: 0;
                        left: 60%;
                        transform: rotate(15deg);
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            85% 25%,
                            100% 25%,
                            75% 45%,
                            100% 45%,
                            85% 65%,
                            100% 65%,
                            75% 85%,
                            100% 100%,
                            0 100%
                        );
                    }

                    &.lightning-4 {
                        top: 0;
                        left: 75%;
                        transform: rotate(-10deg);
                    }

                    &.lightning-5 {
                        top: 0;
                        left: 20%;
                        transform: rotate(-15deg);
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            75% 20%,
                            100% 20%,
                            85% 40%,
                            100% 40%,
                            75% 60%,
                            100% 60%,
                            85% 80%,
                            100% 100%,
                            0 100%
                        );
                    }

                    &.lightning-6 {
                        top: 0;
                        left: 50%;
                        transform: rotate(5deg);
                    }

                    &.lightning-7 {
                        top: 0;
                        left: 65%;
                        transform: rotate(-20deg);
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            80% 10%,
                            100% 10%,
                            90% 30%,
                            100% 30%,
                            80% 50%,
                            100% 50%,
                            90% 70%,
                            100% 70%,
                            80% 90%,
                            100% 100%,
                            0 100%
                        );
                    }

                    &.lightning-8 {
                        top: 0;
                        left: 35%;
                        transform: rotate(20deg);
                    }

                    // 闪电分叉
                    &::before,
                    &::after {
                        content: '';
                        position: absolute;
                        width: 2px;
                        height: 0;
                        background: rgba(255, 255, 255, 0.8);
                        box-shadow: 0 0 8px #64b5f6;
                        opacity: 0;
                        z-index: 2;
                    }

                    &.active::before,
                    &.active::after {
                        opacity: 1;
                        animation: lightningBranch 0.2s ease-out 0.1s;
                    }

                    &::before {
                        left: 0;
                        top: 40%;
                        transform: rotate(30deg);
                        transform-origin: top left;
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            80% 30%,
                            100% 30%,
                            70% 60%,
                            100% 60%,
                            80% 100%,
                            0 100%
                        );
                    }

                    &::after {
                        right: 0;
                        top: 60%;
                        transform: rotate(-40deg);
                        transform-origin: top right;
                        clip-path: polygon(
                            0 0,
                            100% 0,
                            90% 30%,
                            100% 30%,
                            80% 60%,
                            100% 60%,
                            90% 100%,
                            0 100%
                        );
                    }

                    // 额外的分叉
                    &.lightning-1::before,
                    &.lightning-3::before,
                    &.lightning-5::before,
                    &.lightning-7::before {
                        top: 30%;
                    }

                    &.lightning-2::after,
                    &.lightning-4::after,
                    &.lightning-6::after,
                    &.lightning-8::after {
                        top: 50%;
                    }

                    // 二级分叉
                    &.lightning-1::before::after,
                    &.lightning-5::before::after {
                        content: '';
                        position: absolute;
                        width: 1px;
                        height: 0;
                        background: rgba(255, 255, 255, 0.7);
                        box-shadow: 0 0 5px #64b5f6;
                        bottom: 0;
                        right: 0;
                        transform: rotate(-25deg);
                        transform-origin: top right;
                        opacity: 0;
                    }

                    &.active::before::after,
                    &.active::after::before {
                        opacity: 1;
                        animation: lightningBranch 0.15s ease-out 0.15s;
                    }
                }
            }

            // 能量中心
            .energy-core {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 5;

                .core-inner {
                    width: 40px;
                    height: 40px;
                    background: radial-gradient(circle, #90caf9 0%, #2196f3 60%, #1565c0 100%);
                    border-radius: 50%;
                    box-shadow:
                        0 0 15px #64b5f6,
                        0 0 30px #2196f3;
                    animation: pulseCore 2s ease-in-out infinite;
                }

                .core-outer {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 60px;
                    height: 60px;
                    border: 2px solid rgba(144, 202, 249, 0.6);
                    border-radius: 50%;
                    box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
                    animation: rotateCore 8s linear infinite;

                    &::before,
                    &::after {
                        content: '';
                        position: absolute;
                        width: 10px;
                        height: 10px;
                        background: #90caf9;
                        border-radius: 50%;
                        box-shadow: 0 0 8px #64b5f6;
                    }

                    &::before {
                        top: -5px;
                        left: 50%;
                        transform: translateX(-50%);
                    }

                    &::after {
                        bottom: -5px;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                }
            }

            // 能量波纹
            .energy-ripples {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 4;

                .ripple {
                    position: absolute;
                    top: 0;
                    left: 0;
                    border: 1px solid rgba(144, 202, 249, 0.3);
                    border-radius: 50%;
                    transform: translate(-50%, -50%);

                    &.ripple-1 {
                        animation: rippleEffect 4s linear infinite;
                    }

                    &.ripple-2 {
                        animation: rippleEffect 4s linear 1s infinite;
                    }

                    &.ripple-3 {
                        animation: rippleEffect 4s linear 2s infinite;
                    }

                    &.ripple-4 {
                        animation: rippleEffect 4s linear 3s infinite;
                    }
                }
            }
        }

        .analyzing-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 10px;
                font-size: 14px;
                color: #e6e6e6;

                .current-step {
                    color: #90caf9;
                    font-weight: 500;
                    text-shadow: 0 0 8px rgba(144, 202, 249, 0.6);
                }
            }

            .progress-bar {
                height: 6px;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 3px;
                overflow: hidden;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #1976d2, #64b5f6);
                    transition: width 0.3s ease;
                    position: relative;
                    box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 30px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

@keyframes lightningFlash {
    0% {
        height: 0;
        opacity: 0.3;
    }
    5% {
        opacity: 0.6;
    }
    10% {
        opacity: 0.9;
    }
    15% {
        opacity: 0.4;
    }
    20% {
        opacity: 0.8;
    }
    25% {
        height: var(--lightning-height, 100px);
        opacity: 1;
    }
    30% {
        opacity: 0.6;
    }
    35% {
        opacity: 0.9;
    }
    40% {
        opacity: 0.4;
    }
    45% {
        opacity: 0.8;
    }
    100% {
        height: 0;
        opacity: 0;
    }
}

@keyframes lightningBranch {
    0% {
        height: 0;
        opacity: 0.4;
    }
    20% {
        opacity: 0.8;
    }
    40% {
        opacity: 0.5;
    }
    60% {
        opacity: 0.9;
    }
    80% {
        height: 40px;
        opacity: 0.7;
    }
    100% {
        height: 0;
        opacity: 0;
    }
}

@keyframes cloudMove {
    0% {
        transform: translateX(-10%);
    }
    100% {
        transform: translateX(110%);
    }
}

@keyframes pulseCore {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes rotateCore {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes rippleEffect {
    0% {
        width: 0;
        height: 0;
        opacity: 0.8;
    }
    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 添加新的闪电辉光动画
@keyframes lightningGlow {
    0%,
    100% {
        box-shadow:
            0 0 10px #64b5f6,
            0 0 20px #2196f3,
            0 0 30px #1976d2;
    }
    50% {
        box-shadow:
            0 0 15px #64b5f6,
            0 0 30px #2196f3,
            0 0 45px #1976d2;
    }
}

// 优化移动端性能
@media (max-width: 768px) {
    .storm-animation {
        .lightning-container {
            .lightning {
                &::before,
                &::after {
                    display: none; // 移动端简化闪电分叉效果
                }
            }
        }
    }
}
</style>
