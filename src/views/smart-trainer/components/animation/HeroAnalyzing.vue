<template>
    <div class="hero-analyzing">
        <div class="analyzing-container">
            <!-- 美国队长盾牌动画 -->
            <div class="shield-animation">
                <!-- 盾牌主体 -->
                <div class="shield">
                    <div class="shield-outer-ring"></div>
                    <div class="shield-middle-ring"></div>
                    <div class="shield-inner-circle">
                        <div class="star"></div>
                    </div>
                    <!-- 能量波纹效果 -->
                    <div class="energy-ripple ripple-1"></div>
                    <div class="energy-ripple ripple-2"></div>
                    <div class="energy-ripple ripple-3"></div>
                </div>

                <!-- 分析光束效果 -->
                <div class="scan-rays">
                    <div
                        v-for="n in 8"
                        :key="n"
                        class="ray"
                        :style="`transform: rotate(${n * 45}deg)`"
                    ></div>
                </div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = [
    '正在分析语音内容',
    '评估回答完整度',
    '计算专业度得分',
    '分析表达流畅度',
    '生成综合评价'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
});
</script>

<style lang="scss" scoped>
.hero-analyzing {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #0a1f44, #1a3a6c);
    color: #fff;
    margin: 12px auto;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

    .analyzing-container {
        .shield-animation {
            position: relative;
            height: 160px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            perspective: 1000px;
            overflow: hidden;
            background: linear-gradient(
                180deg,
                rgba(30, 60, 120, 0.2) 0%,
                rgba(30, 60, 120, 0.05) 100%
            );
            border-radius: 12px;

            .shield {
                position: relative;
                width: 120px;
                height: 120px;
                border-radius: 50%;
                animation:
                    pulseShield 2s ease-in-out infinite,
                    rotateShield 8s linear infinite;
                transform-style: preserve-3d;

                .shield-outer-ring {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    background: #e63946;
                    box-shadow: 0 0 20px rgba(230, 57, 70, 0.6);
                }

                .shield-middle-ring {
                    position: absolute;
                    width: 80%;
                    height: 80%;
                    top: 10%;
                    left: 10%;
                    border-radius: 50%;
                    background: #f1faee;
                    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
                }

                .shield-inner-circle {
                    position: absolute;
                    width: 50%;
                    height: 50%;
                    top: 25%;
                    left: 25%;
                    border-radius: 50%;
                    background: #457b9d;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 0 15px rgba(69, 123, 157, 0.8);

                    .star {
                        width: 60%;
                        height: 60%;
                        position: relative;
                        color: white;

                        &:before {
                            content: '★';
                            position: absolute;
                            font-size: 30px;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            color: #f1faee;
                            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
                        }
                    }
                }
            }

            .energy-ripple {
                position: absolute;
                border-radius: 50%;
                border: 2px solid rgba(255, 255, 255, 0.4);
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                opacity: 0;

                &.ripple-1 {
                    animation: rippleEffect 2s ease-out infinite;
                }

                &.ripple-2 {
                    animation: rippleEffect 2s ease-out 0.6s infinite;
                }

                &.ripple-3 {
                    animation: rippleEffect 2s ease-out 1.2s infinite;
                }
            }

            .scan-rays {
                position: absolute;
                width: 100%;
                height: 100%;
                animation: rotateScan 4s linear infinite;

                .ray {
                    position: absolute;
                    width: 2px;
                    height: 50%;
                    background: linear-gradient(
                        to top,
                        transparent,
                        rgba(255, 255, 255, 0.8),
                        transparent
                    );
                    top: 0;
                    left: 50%;
                    transform-origin: bottom center;
                    animation: pulseRay 1.5s ease-in-out infinite;
                }
            }
        }

        .analyzing-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 10px;
                font-size: 14px;
                color: #e6e6e6;

                .current-step {
                    color: #a8dadc;
                    font-weight: 500;
                    text-shadow: 0 0 8px rgba(168, 218, 220, 0.6);
                }
            }

            .progress-bar {
                height: 6px;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 3px;
                overflow: hidden;
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #457b9d, #a8dadc);
                    transition: width 0.3s ease;
                    position: relative;
                    box-shadow: 0 0 8px rgba(168, 218, 220, 0.6);

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 30px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

@keyframes pulseShield {
    0%,
    100% {
        transform: scale(0.95);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes rotateShield {
    from {
        transform: rotateY(0deg);
    }
    to {
        transform: rotateY(360deg);
    }
}

@keyframes rippleEffect {
    0% {
        width: 40%;
        height: 40%;
        opacity: 0.8;
    }
    100% {
        width: 160%;
        height: 160%;
        opacity: 0;
    }
}

@keyframes pulseRay {
    0%,
    100% {
        opacity: 0.3;
        height: 40%;
    }
    50% {
        opacity: 0.8;
        height: 60%;
    }
}

@keyframes rotateScan {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 优化移动端性能
@media (max-width: 768px) {
    .shield-animation {
        .shield {
            transform: scale(0.8);
        }
    }
}
</style>
