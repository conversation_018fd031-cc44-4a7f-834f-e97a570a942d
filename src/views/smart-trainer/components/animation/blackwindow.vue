<template>
    <div class="blackwidow-analyzing">
        <div class="analyzing-container">
            <!-- 黑寡妇标志性动画 -->
            <div class="blackwidow-animation">
                <!-- 蜘蛛网动画 -->
                <div class="web-container">
                    <div class="hourglass">
                        <div class="top-container">
                            <div class="sand"></div>
                        </div>
                        <div class="middle"></div>
                        <div class="bottom-container">
                            <div class="sand-pile"></div>
                        </div>
                    </div>

                    <div class="web-lines">
                        <div
                            v-for="n in 8"
                            :key="`line-${n}`"
                            class="web-line"
                            :style="`transform: rotate(${n * 45}deg)`"
                        ></div>
                    </div>

                    <div class="web-circles">
                        <div
                            v-for="n in 3"
                            :key="`circle-${n}`"
                            class="web-circle"
                            :style="`width: ${n * 60}px; height: ${n * 60}px`"
                        ></div>
                    </div>

                    <!-- 红色标志 -->
                    <div class="blackwidow-symbol">
                        <div class="symbol-inner"></div>
                    </div>
                </div>

                <!-- 扫描效果 -->
                <div class="scan-effect"></div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = ['正在入侵安全系统', '绕过防火墙', '解密加密数据', '分析目标情报', '生成任务报告'];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
});
</script>

<style lang="scss" scoped>
.blackwidow-analyzing {
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
    color: #fff;
    margin: 12px auto;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);

    .analyzing-container {
        .blackwidow-animation {
            position: relative;
            height: 160px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%);
            border-radius: 12px;
            box-shadow: inset 0 0 20px rgba(255, 0, 0, 0.2);

            .web-container {
                position: relative;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                .hourglass {
                    position: absolute;
                    width: 60px;
                    height: 100px;
                    z-index: 2;

                    .top-container {
                        position: relative;
                        width: 60px;
                        height: 40px;
                        background: transparent;
                        border-left: 2px solid rgba(255, 0, 0, 0.7);
                        border-right: 2px solid rgba(255, 0, 0, 0.7);
                        border-top: 2px solid rgba(255, 0, 0, 0.7);
                        border-radius: 50% 50% 0 0;
                        overflow: hidden;

                        .sand {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(255, 0, 0, 0.2);
                            animation: emptySand 10s linear infinite;
                        }
                    }

                    .middle {
                        width: 0;
                        height: 0;
                        border-left: 30px solid transparent;
                        border-right: 30px solid transparent;
                        border-top: 10px solid rgba(255, 0, 0, 0.7);
                        position: relative;
                        z-index: 1;
                    }

                    .bottom-container {
                        position: relative;
                        width: 60px;
                        height: 40px;
                        background: transparent;
                        border-left: 2px solid rgba(255, 0, 0, 0.7);
                        border-right: 2px solid rgba(255, 0, 0, 0.7);
                        border-bottom: 2px solid rgba(255, 0, 0, 0.7);
                        border-radius: 0 0 50% 50%;
                        overflow: hidden;

                        .sand-pile {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 0%;
                            background: rgba(255, 0, 0, 0.2);
                            animation: fillSand 10s linear infinite;
                        }
                    }
                }

                .web-lines {
                    position: absolute;
                    width: 100%;
                    height: 100%;

                    .web-line {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        width: 100%;
                        height: 1px;
                        background: linear-gradient(
                            90deg,
                            transparent,
                            rgba(255, 0, 0, 0.3),
                            rgba(255, 0, 0, 0.5),
                            rgba(255, 0, 0, 0.3),
                            transparent
                        );
                        transform-origin: center;
                    }
                }

                .web-circles {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);

                    .web-circle {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        border: 1px solid rgba(255, 0, 0, 0.3);
                        border-radius: 50%;
                        animation: pulseCircle 3s infinite alternate;

                        &:nth-child(1) {
                            animation-delay: 0s;
                        }

                        &:nth-child(2) {
                            animation-delay: 1s;
                        }

                        &:nth-child(3) {
                            animation-delay: 2s;
                        }
                    }
                }

                .blackwidow-symbol {
                    position: absolute;
                    width: 40px;
                    height: 40px;
                    background-color: #ff0000;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    animation: pulseSymbol 2s infinite;
                    z-index: 3;

                    .symbol-inner {
                        width: 24px;
                        height: 24px;
                        background-color: black;
                        clip-path: polygon(50% 0%, 0% 100%, 30% 50%, 50% 70%, 70% 50%, 100% 100%);
                    }
                }
            }

            .scan-effect {
                position: absolute;
                width: 100%;
                height: 10px;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 0, 0, 0.2),
                    rgba(255, 0, 0, 0.5),
                    rgba(255, 0, 0, 0.2),
                    transparent
                );
                animation: scanEffect 3s linear infinite;
                z-index: 4;
            }
        }

        .analyzing-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 8px;
                font-size: 14px;
                color: #e6e6e6;

                .current-step {
                    color: #ff0000;
                    font-weight: 500;
                    text-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
                }
            }

            .progress-bar {
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #ff0000, #aa0000);
                    transition: width 0.3s ease;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 20px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

@keyframes pulseSymbol {
    0%,
    100% {
        transform: scale(0.9);
        box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
    }
    50% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 0, 0, 0.8);
    }
}

@keyframes pulseCircle {
    0% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes scanEffect {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        transform: translateY(100%);
        opacity: 0;
    }
}

@keyframes emptySand {
    0% {
        height: 100%;
    }
    90%,
    100% {
        height: 0%;
    }
}

@keyframes fillSand {
    0% {
        height: 0%;
    }
    90%,
    100% {
        height: 100%;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
</style>
