<template>
    <div class="ai-analyzing">
        <div class="analyzing-container">
            <!-- 3D DNA分析动效 -->
            <div class="dna-animation">
                <div class="dna-container">
                    <div v-for="n in 20" :key="n" class="dna-slice">
                        <div class="left-point"></div>
                        <div class="connector"></div>
                        <div class="right-point"></div>
                    </div>
                </div>
                <!-- AI分析光束 -->
                <div class="scan-beam"></div>
            </div>

            <!-- 分析进度文本 -->
            <div class="analyzing-text">
                <div class="text-container">
                    <span class="current-step">{{ currentStep }}</span>
                    <span class="dots">{{ dots }}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress" :style="{ width: `${progress}%` }"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const steps = [
    '正在分析语音内容',
    '评估回答完整度',
    '计算专业度得分',
    '分析表达流畅度',
    '生成综合评价'
];

const currentStep = ref(steps[0]);
const dots = ref('');
const progress = ref(0);
let stepIndex = 0;
let dotsInterval = null;
let progressInterval = null;

// 更新步骤
const updateStep = () => {
    stepIndex = (stepIndex + 1) % steps.length;
    currentStep.value = steps[stepIndex];
};

onMounted(() => {
    // 动态更新点点
    dotsInterval = setInterval(() => {
        dots.value = '.'.repeat((dots.value.length + 1) % 4);
    }, 500);

    // 更新进度条和步骤
    progressInterval = setInterval(() => {
        progress.value += 1;
        if (progress.value % 20 === 0) {
            updateStep();
        }
        if (progress.value >= 100) {
            clearInterval(progressInterval);
        }
    }, 100);
});

onBeforeUnmount(() => {
    clearInterval(dotsInterval);
    clearInterval(progressInterval);
});
</script>

<style lang="scss" scoped>
.ai-analyzing {
    width: 100%;
    padding: 16px;
    border-radius: 12px;
    background: #f0f6ff;
    color: #333;
    margin: 12px auto;
    position: relative;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.1);

    .analyzing-container {
        .dna-animation {
            position: relative;
            height: 120px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            perspective: 1000px;
            overflow: hidden;

            .dna-container {
                position: relative;
                width: 80px;
                height: 100px;
                transform-style: preserve-3d;
                animation: rotateDNA 8s linear infinite;

                .dna-slice {
                    position: absolute;
                    width: 100%;
                    height: 6px;
                    transform-style: preserve-3d;

                    @for $i from 1 through 20 {
                        &:nth-child(#{$i}) {
                            transform: translateY(#{($i - 1) * 5}px) rotateY(#{$i * 18}deg);
                        }
                    }

                    .left-point,
                    .right-point {
                        position: absolute;
                        width: 8px;
                        height: 8px;
                        background: #1677ff;
                        border-radius: 50%;
                        box-shadow: 0 0 15px rgba(22, 119, 255, 0.6);
                        animation: pulseDot 2s ease-in-out infinite;
                    }

                    .left-point {
                        left: 0;
                        animation-delay: 0.5s;
                    }

                    .right-point {
                        right: 0;
                    }

                    .connector {
                        position: absolute;
                        left: 4px;
                        right: 4px;
                        height: 2px;
                        background: linear-gradient(
                            90deg,
                            rgba(22, 119, 255, 0.2),
                            rgba(22, 119, 255, 0.8),
                            rgba(22, 119, 255, 0.2)
                        );
                        transform-origin: center;
                        animation: pulseConnector 2s ease-in-out infinite;
                    }
                }
            }

            .scan-beam {
                position: absolute;
                width: 120px;
                height: 4px;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(22, 119, 255, 0.8),
                    transparent
                );
                animation: scanBeam 3s linear infinite;
            }
        }

        .analyzing-text {
            margin-top: 12px;
            text-align: center;

            .text-container {
                margin-bottom: 8px;
                font-size: 14px;
                color: #555;

                .current-step {
                    color: #1677ff;
                    font-weight: 500;
                }
            }

            .progress-bar {
                height: 6px;
                background: rgba(22, 119, 255, 0.1);
                border-radius: 3px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    background: linear-gradient(90deg, #1677ff, #4318ff);
                    transition: width 0.3s ease;
                    position: relative;
                    border-radius: 3px;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 20px;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
                        animation: shimmer 1s infinite;
                    }
                }
            }
        }
    }
}

@keyframes rotateDNA {
    from {
        transform: rotateY(0deg) rotateX(60deg);
    }
    to {
        transform: rotateY(360deg) rotateX(60deg);
    }
}

@keyframes pulseDot {
    0%,
    100% {
        transform: scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes pulseConnector {
    0%,
    100% {
        opacity: 0.4;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes scanBeam {
    0%,
    100% {
        transform: translateY(-50px);
    }
    50% {
        transform: translateY(50px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-20px);
    }
    100% {
        transform: translateX(20px);
    }
}
</style>
