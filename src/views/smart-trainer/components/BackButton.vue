<template>
    <div class="back-button" @click="handleBack">
        <i class="pi pi-arrow-left"></i>
        <span v-if="title">{{ title }}</span>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const props = defineProps({
    /**
     * 返回按钮标题
     */
    title: {
        type: String,
        default: ''
    },
    /**
     * 自定义返回路径
     */
    backPath: {
        type: String,
        default: ''
    }
});

const router = useRouter();

/**
 * 处理返回按钮点击
 */
const handleBack = () => {
    if (props.backPath) {
        router.push(props.backPath);
    } else {
        router.back();
    }
};
</script>

<style lang="scss" scoped>
.back-button {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    
    i {
        margin-right: 8px;
        font-size: 18px;
    }
}
</style>
