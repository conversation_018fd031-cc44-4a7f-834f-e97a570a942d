<template>
    <div class="back-to-genie" :class="{ 'is-visible': isVisible }" @click="handleBackToGenie">
        <i class="pi pi-home"></i>
        <span>返回家家精灵</span>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
    // 是否自动显示
    autoShow: {
        type: Boolean,
        default: true
    },
    // 显示延迟时间(毫秒)
    delay: {
        type: Number,
        default: 500
    },
    // 按钮位置类型
    position: {
        type: String,
        default: 'bottom-right',
        validator: value => ['bottom-right', 'bottom-left', 'top-right', 'top-left'].includes(value)
    }
});

const router = useRouter();
const isVisible = ref(false);

/**
 * 返回家家精灵主页
 */
const handleBackToGenie = () => {
    router.replace('/smart-chat/home');
};

/**
 * 显示按钮
 */
const show = () => {
    setTimeout(() => {
        isVisible.value = true;
    }, props.delay);
};

/**
 * 隐藏按钮
 */
const hide = () => {
    isVisible.value = false;
};

// 暴露给父组件的方法
defineExpose({
    show,
    hide
});

onMounted(() => {
    if (props.autoShow) {
        show();
    }
});
</script>

<style lang="scss" scoped>
.back-to-genie {
    position: fixed;
    bottom: 80px;
    right: 16px;
    z-index: 9999;

    display: flex;
    align-items: center;
    gap: 8px;

    // 现代化配色方案 - 清新蓝绿渐变 + 毛玻璃效果
    background: linear-gradient(
        135deg,
        rgba(56, 189, 248, 0.9) 0%,
        rgba(16, 185, 129, 0.9) 50%,
        rgba(34, 197, 94, 0.9) 100%
    );
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 28px;
    padding: 12px 18px;

    // 现代化阴影效果
    box-shadow: 0 8px 32px rgba(56, 189, 248, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);

    font-size: 14px;
    font-weight: 600;
    cursor: pointer;

    opacity: 0;
    transform: translateX(100px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.is-visible {
        opacity: 1;
        transform: translateX(0);
    }

    &:hover {
        transform: translateY(-3px) scale(1.02);
        background: linear-gradient(
            135deg,
            rgba(56, 189, 248, 1) 0%,
            rgba(16, 185, 129, 1) 50%,
            rgba(34, 197, 94, 1) 100%
        );
        box-shadow: 0 12px 40px rgba(56, 189, 248, 0.4), 0 4px 16px rgba(0, 0, 0, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
    }

    &:active {
        transform: translateY(-1px) scale(0.98);
        box-shadow: 0 4px 20px rgba(56, 189, 248, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    i {
        font-size: 16px;
        flex-shrink: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    span {
        white-space: nowrap;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    // 不同位置的样式
    &.position-bottom-left {
        right: auto;
        left: 16px;
        transform: translateX(-100px);

        &.is-visible {
            transform: translateX(0);
        }
    }

    &.position-top-right {
        bottom: auto;
        top: 16px;
        transform: translateY(-100px);

        &.is-visible {
            transform: translateY(0);
        }
    }

    &.position-top-left {
        bottom: auto;
        top: 16px;
        right: auto;
        left: 16px;
        transform: translate(-100px, -100px);

        &.is-visible {
            transform: translate(0, 0);
        }
    }
}

// 响应式适配
@media (max-width: 480px) {
    .back-to-genie {
        bottom: 70px;
        right: 12px;
        padding: 10px 14px;
        font-size: 13px;

        &.position-bottom-left {
            left: 12px;
        }

        &.position-top-right,
        &.position-top-left {
            top: 12px;
        }

        &.position-top-left {
            left: 12px;
        }
    }
}
</style>
