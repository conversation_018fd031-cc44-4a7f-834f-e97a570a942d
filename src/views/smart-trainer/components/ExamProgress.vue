<template>
    <div class="exam-progress" :class="{ 'with-title': withTitle }">
        <div class="progress-container">
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: progressPercentage }"></div>
                </div>
                <div class="progress-character" :style="characterPosition">
                    <img
                        class="character-icon"
                        src="https://fs.autohome.com.cn/dealer_views/dealer_front/public/athm_loong/athm_loong_loading.gif"
                        alt=""
                    />
                </div>

                <!-- 修改里程碑标记逻辑 只展示0% 跟 100% 的里程碑 -->
                <div class="milestones">
                    <div
                        v-for="(milestone, index) in milestones"
                        :key="index"
                        class="milestone"
                        :class="{
                            completed: currentQuestion >= milestone.position,
                        }"
                        :style="{
                            left: `${(milestone.position / totalQuestions) * 100}%`,
                        }"
                        @click="handleMilestoneClick(milestone)"
                    >
                        <div class="milestone-dot"></div>
                        <div class="milestone-label" v-if="milestone.position === 0 || milestone.position === totalQuestions">{{ milestone.label }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';

const props = defineProps({
    currentQuestion: Number,
    totalQuestions: Number,
    withTitle: {
        type: Boolean,
        default: true
    },
    showMilestones: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['milestone-click']);

// 修改里程碑数据生成逻辑
const milestones = computed(() => {
    if (!props.showMilestones) {
        return [];
    }

    const total = props.totalQuestions;
    const milestonePositions = [];

    // 修改百分比计算逻辑
    for (let i = 0; i <= total; i++) {
        milestonePositions.push({
            position: i,
            // 将百分比四舍五入到整数
            label: `${Math.round((i / total) * 100)}%`,
            questionIndex: i - 1
        });
    }

    return milestonePositions;
});

// 计算进度百分比
const progressPercentage = computed(
    () => `${Math.min(100, (props.currentQuestion / props.totalQuestions) * 100)}%`
);

// 计算小人位置
const characterPosition = computed(() => ({
    left: progressPercentage.value
}));

// 添加里程碑点击处理函数
const handleMilestoneClick = (milestone) => {
    if (milestone.position === 0) {
        return;
    }
    // 只有已完成的里程碑才能点击
    if (props.currentQuestion >= milestone.position) {
        emit('milestone-click', milestone);
    }
};

// 生命周期钩子
onMounted(async () => {
    // 组件挂载完成后的初始化操作
});
</script>

<style lang="scss" scoped>
.exam-progress {
    position: relative;
    width: 100%;
    background: transparent;
    padding: 0 10px 0;
    transition: all 0.3s ease;

    &.with-title {
        background: linear-gradient(to right, rgba(22, 119, 255, 0.05), rgba(22, 119, 255, 0.08));
        border-bottom: 1px solid rgba(22, 119, 255, 0.1);
    }

    .progress-container {
        max-width: 680px;
        margin: 0 auto;
    }

    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .progress-count {
            font-size: 14px;
            font-weight: 500;
            color: #1677ff;
            display: flex;
            align-items: center;

            .progress-label {
                margin-right: 4px;
                color: #666;
                font-weight: normal;
            }

            .current {
                font-size: 16px;
                font-weight: 600;
            }

            .separator {
                margin: 0 2px;
                opacity: 0.7;
            }

            .total {
                opacity: 0.8;
            }
        }
    }

    .progress-bar-container {
        position: relative;
        height: 30px;

        .progress-bar {
            height: 6px;
            background: rgba(22, 119, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #1677ff, #4096ff);
                border-radius: 3px;
                transition: width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
                box-shadow: 0 0 6px rgba(22, 119, 255, 0.5);
            }
        }

        .progress-character {
            position: absolute;
            top: -12px;
            transform: translateX(-50%);
            transition: left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);

            .character-icon {
                width: 24px;
                height: 24px;
                filter: drop-shadow(0 2px 4px rgba(22, 119, 255, 0.4));
            }
        }

        .milestones {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .milestone {
                position: absolute;
                top: -2px;
                transform: translateX(-50%);
                cursor: pointer; /* 添加指针样式表示可点击 */
                transition: transform 0.2s ease; /* 添加点击动效 */

                &:active {
                    transform: translateX(-50%) scale(1.2); /* 点击缩小效果 */
                }

                .milestone-dot {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background: #ccc;
                    margin: 0 auto;
                    margin-bottom: 4px;
                    transition: all 0.3s ease;
                }

                .milestone-label {
                    font-size: 12px;
                    color: #999;
                    text-align: center;
                    transition: all 0.3s ease;
                }

                &.completed {
                    .milestone-dot {
                        background: #ff6600;
                        box-shadow: 0 0 6px rgba(255, 102, 0, 0.5);
                    }

                    .milestone-label {
                        color: #ff6600;
                        font-weight: 500;
                    }
                }
            }
        }
    }

    @keyframes pulse {
        0% {
            opacity: 0.7;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.7;
        }
    }
}
</style>
