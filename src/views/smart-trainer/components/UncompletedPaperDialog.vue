<template>
    <Dialog
        :visible="visible"
        @update:visible="updateVisible"
        :modal="true"
        :style="{ width: '90%', maxWidth: '500px' }"
        :closable="false"
        :closeOnEscape="false"
    >
        <template #header>
            <div class="dialog-header">
                <i class="pi pi-info-circle"></i>
                <span>{{ TEXT.PAPER_STATUS.IN_PROGRESS.TEXT }}的{{ TEXT.EXAM_INFO.TYPE }}</span>
            </div>
        </template>
        <div class="dialog-content">
            <div class="uncompleted-papers">
                <div v-if="papers.length === 0" class="no-data">
                    <i class="pi pi-inbox"></i>
                    <p>{{ TEXT.EMPTY.RANK_NO_DATA }}</p>
                </div>
                <div
                    v-else
                    v-for="paper in papers"
                    :key="paper.id"
                    class="paper-item"
                    @click="selectPaper(paper)"
                >
                    <div class="paper-info">
                        <div class="paper-time">开始时间：{{ formatDate(paper.createdStime) }}</div>
                        <div class="paper-status">
                            <Badge :value="TEXT.PAPER_STATUS.IN_PROGRESS.TEXT" severity="warning" />
                        </div>
                    </div>
                    <Button class="continue-btn">
                        {{ TEXT.PAPER_STATUS.IN_PROGRESS.BUTTON_TEXT }}
                        <i class="pi pi-arrow-right"></i>
                    </Button>
                </div>
            </div>
            <div class="bottom-action">
                <Button class="new-exam-btn" @click="startNewExam" severity="secondary">
                    {{ TEXT.BUTTONS.START_TRAINING }}
                </Button>
            </div>
        </div>
    </Dialog>
</template>

<script setup>
import { nextTick } from 'vue';
import dayjs from 'dayjs';
import { TRAINING_TEXT_SCHEMA2 as TEXT } from '../constants/text';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    papers: {
        type: Array,
        required: true
    }
});

const emit = defineEmits(['update:visible', 'select-paper', 'start-new-exam']);

function updateVisible(value) {
    emit('update:visible', value);
}

function formatDate(dateStr) {
    return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
}

function selectPaper(paper) {
    nextTick(() => {
        emit('select-paper', paper);
    });
}

function startNewExam() {
    emit('start-new-exam');
}
</script>

<style lang="scss" scoped>
.dialog-header {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1e293b;
    font-size: 16px;
    font-weight: 500;

    .pi {
        color: #3b82f6;
        font-size: 18px;
    }
}

.dialog-content {
    position: relative;
    height: 60vh;
    display: flex;
    flex-direction: column;
}

.uncompleted-papers {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px; // 为底部按钮留出空间

    .no-data {
        text-align: center;
        padding: 40px 20px;
        color: #64748b;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .pi {
            font-size: 24px;
        }

        p {
            margin: 0;
            font-size: 14px;
        }
    }

    .paper-item {
        padding: 16px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #1e293b;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);

        &:active {
            transform: scale(0.98);
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        }

        .paper-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .paper-time {
            font-size: 14px;
            color: #64748b;
        }

        .continue-btn {
            background: #475569;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(71, 85, 105, 0.1);

            .pi {
                font-size: 12px;
            }

            &:active {
                background: #334155;
                transform: translateY(1px);
            }
        }
    }
}

.bottom-action {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: #ffffff;
}

.new-exam-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #1677ff 0%, #4318ff 100%);
    border: none;
    border-radius: 8px;
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(67, 24, 255, 0.15);

    .pi {
        font-size: 14px;
        margin-right: 8px;
    }

    &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #0e5edb 0%, #3612d1 100%);
        box-shadow: 0 2px 6px rgba(67, 24, 255, 0.1);
    }
}
</style>
<style lang="scss">
.p-dialog-header {
    padding: 1.25rem;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(to right, #f8fafc, #f1f5f9);
}

.p-dialog-content {
    padding: 1.25rem;
    background: #ffffff;
    overflow: hidden; // 防止出现双滚动条
}
</style>
