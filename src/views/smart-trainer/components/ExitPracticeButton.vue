<template>
    <div>
        <div class="end-btn-container" @click="showExitConfirmation">
            <img
                class="action-btn end-btn"
                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/exam/exit.png"
                alt="exit"
            />
        </div>

        <!-- 使用原生弹窗替代 Dialog 组件 -->
        <Teleport to="body">
            <div class="custom-dialog-overlay" v-if="exitDialogVisible" @click="closeDialog">
                <div class="custom-dialog" @click.stop>
                    <div class="exit-dialog-content">
                        <div class="dialog-header">
                            <h2>退出练习</h2>
                            <p>请选择您要退出的方式</p>
                        </div>

                        <div class="exit-options">
                            <div
                                class="exit-option"
                                :class="{
                                    selected: selectedExitOption === 'pause',
                                }"
                                @click="selectedExitOption = 'pause'"
                            >
                                <div class="option-icon">
                                    <i class="pi pi-save"></i>
                                </div>
                                <div class="option-content">
                                    <h3>保存并退出</h3>
                                    <div class="option-description">
                                        <p>保存当前练习进度，下次可继续练习</p>
                                    </div>
                                </div>
                            </div>

                            <div
                                class="exit-option"
                                :class="{
                                    selected: selectedExitOption === 'end',
                                }"
                                @click="selectedExitOption = 'end'"
                            >
                                <div class="option-icon">
                                    <i class="pi pi-trash"></i>
                                </div>
                                <div class="option-content">
                                    <h3>废弃并退出</h3>
                                    <div class="option-description">
                                        <p>不保存当前练习记录，直接退出</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="dialog-footer">
                            <button class="cancel-btn" @click="closeDialog">取消</button>
                            <button class="confirm-btn" @click="confirmExit">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        </Teleport>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const emit = defineEmits(['endPractice', 'pausePractice']);

/**
 * 退出确认弹窗显示状态
 * @type {import('vue').Ref<boolean>}
 */
const exitDialogVisible = ref(false);

/**
 * 选择的退出选项：'pause' - 保存并退出，'end' - 废弃并退出
 * @type {import('vue').Ref<string>}
 */
const selectedExitOption = ref('pause');

/**
 * 显示退出确认弹窗
 */
const showExitConfirmation = () => {
    exitDialogVisible.value = true;
    selectedExitOption.value = 'pause'; // 默认选择保存并退出
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
};

/**
 * 关闭弹窗
 */
const closeDialog = () => {
    exitDialogVisible.value = false;
    // 恢复背景滚动
    document.body.style.overflow = '';
};

/**
 * 确认退出操作
 */
const confirmExit = () => {
    if (selectedExitOption.value === 'pause') {
        emit('pausePractice');
    } else {
        emit('endPractice');
    }
    closeDialog();
};
</script>

<style lang="scss" scoped>
.action-btn {
    width: 24px;
}

/* 自定义弹窗样式 */
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease;
}

.custom-dialog {
    width: 90%;
    max-width: 360px;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    min-height: 350px;
    display: flex;
    flex-direction: column;
}

.exit-dialog-content {
    display: flex;
    flex-direction: column;
    flex: 1;

    .dialog-header {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
        padding: 16px;
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
        flex-shrink: 0;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png')
                center/cover;
            opacity: 0.1;
        }

        h2 {
            margin: 0 0 4px 0;
            font-size: 18px;
            font-weight: 600;
            position: relative;
        }

        p {
            margin: 0;
            font-size: 13px;
            opacity: 0.9;
            position: relative;
        }
    }

    .exit-options {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 16px;

        .exit-option {
            display: flex;
            padding: 12px;
            border-radius: 10px;
            border: 1px solid #e8e8e8;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            background: #ffffff;
            position: relative;
            overflow: hidden;
            min-height: 70px;
            height: 80px;

            &:active {
                transform: scale(0.98);
            }

            &.selected {
                border-color: transparent;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

                &:nth-child(1) {
                    background: linear-gradient(135deg, #1677ff, #4096ff);

                    .option-icon {
                        background-color: rgba(255, 255, 255, 0.2);
                        color: white;
                    }

                    h3,
                    p {
                        color: white;
                    }

                    .option-description p {
                        color: rgba(255, 255, 255, 0.85);
                    }
                }

                &:nth-child(2) {
                    background: linear-gradient(135deg, #ff6600, #ff8533);

                    .option-icon {
                        background-color: rgba(255, 255, 255, 0.2);
                        color: white;
                    }

                    h3,
                    p {
                        color: white;
                    }

                    .option-description p {
                        color: rgba(255, 255, 255, 0.85);
                    }
                }
            }

            .option-icon {
                align-self: center;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 12px;
                transition: all 0.3s ease;
                flex-shrink: 0;

                i {
                    font-size: 18px;
                    transition: all 0.3s ease;
                }
            }

            &:nth-child(1) .option-icon {
                background-color: rgba(22, 119, 255, 0.1);
                color: #1677ff;
            }

            &:nth-child(2) .option-icon {
                background-color: rgba(255, 102, 0, 0.1);
                color: #ff6600;
            }

            .option-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;

                h3 {
                    font-size: 15px;
                    margin: 0;
                    font-weight: 500;
                    transition: all 0.3s ease;
                    padding: 2px 0;
                }

                .option-description {
                    opacity: 1;
                    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
                    transform: translateY(0);
                    margin-top: 2px;

                    p {
                        font-size: 12px;
                        margin: 0;
                        color: #999;
                        text-align: left;
                        line-height: 1.4;
                        transition: color 0.3s ease;
                    }
                }
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 12px 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);

        button {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.96);
            }
        }

        .cancel-btn {
            background: transparent;
            color: #666;
            border: none;

            &:active {
                background: rgba(0, 0, 0, 0.05);
            }
        }

        .confirm-btn {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

            &:active {
                transform: translateY(1px);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
            }
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
