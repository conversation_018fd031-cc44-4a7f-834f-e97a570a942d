<template>
    <div>
        <!-- 设置按钮 -->
        <div class="settings-btn-container" @click="toggleSettings">
            <img
                class="settings-btn"
                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/exam/set.png"
                alt="setting"
            />
        </div>

        <!-- 音频设置面板 -->
        <Transition name="slide-up">
            <div v-if="showSettings" class="audio-settings-panel">
                <div class="panel-header">
                    <h3>设置中心</h3>
                    <button class="close-btn" @click="showSettings = false">
                        <i class="pi pi-times"></i>
                    </button>
                </div>
                <div class="panel-content">
                    <div class="setting-section audio-settings">
                        <h4 class="section-title">音频设置</h4>
                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">自动播放音频</span>
                                <span class="setting-desc">切换题目时自动播放音频</span>
                            </div>
                            <div class="toggle-switch">
                                <input
                                    type="checkbox"
                                    id="autoplay-toggle"
                                    v-model="autoPlayAudio"
                                    @change="updateAudioSettings"
                                />
                                <label for="autoplay-toggle"></label>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">播放延迟</span>
                                <span class="setting-desc">{{ formatDelay(audioPlayDelay) }}</span>
                            </div>
                            <div class="slider-container">
                                <input
                                    type="range"
                                    min="0"
                                    max="10"
                                    step="0.5"
                                    v-model.number="audioPlayDelayInSeconds"
                                    @change="updateAudioSettings"
                                    class="delay-slider"
                                />
                            </div>
                        </div>

                        <!-- 音色选择 -->
                        <div class="setting-item voice-type-setting">
                            <div class="setting-info">
                                <span class="setting-label">音色选择</span>
                                <span class="setting-desc">选择不同的音频播放音色</span>
                            </div>
                            <div class="voice-selector" @click="toggleVoiceSelector">
                                <div class="selected-voice">
                                    <span>{{ selectedVoice.name }}</span>
                                    <i
                                        class="pi"
                                        :class="showVoiceList ? 'pi-chevron-up' : 'pi-chevron-down'"
                                    ></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="setting-section interface-settings">
                        <h4 class="section-title">界面设置</h4>
                        <div class="setting-item">
                            <div class="setting-info">
                                <span class="setting-label">显示里程碑</span>
                                <span class="setting-desc">在进度条上显示学习里程碑</span>
                            </div>
                            <div class="toggle-switch">
                                <input
                                    type="checkbox"
                                    id="milestone-toggle"
                                    v-model="showMilestones"
                                    @change="updateInterfaceSettings"
                                />
                                <label for="milestone-toggle"></label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-footer">
                    <button class="reset-btn" @click="resetSettings">
                        <i class="pi pi-refresh"></i>
                        <span>恢复默认设置</span>
                    </button>
                </div>
            </div>
        </Transition>

        <!-- 遮罩层 -->
        <div v-if="showSettings" class="settings-overlay" @click="showSettings = false"></div>

        <!-- 音色选择列表 - 独立面板 -->
        <Transition name="fade">
            <div
                v-if="showVoiceList"
                class="voice-panel-overlay"
                @click.self="showVoiceList = false"
            >
                <div class="voice-panel">
                    <div class="voice-panel-header">
                        <h4>选择音色</h4>
                        <button class="close-btn" @click="showVoiceList = false">
                            <i class="pi pi-times"></i>
                        </button>
                    </div>
                    <div class="voice-panel-content">
                        <div
                            v-for="voice in voiceList"
                            :key="voice.id"
                            class="voice-item"
                            :class="{ active: selectedVoice.id === voice.id }"
                            @click="selectVoice(voice)"
                        >
                            <div class="voice-item-content">
                                <span class="voice-name">{{ voice.name }}</span>
                                <span class="voice-desc">{{ voice.description }}</span>
                            </div>
                            <i v-if="selectedVoice.id === voice.id" class="pi pi-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import useTrainerStore from '@/stores/trainer';
import audioManager from '@/utils/audioManager';
import { useToast } from 'primevue/usetoast';

const trainerStore = useTrainerStore();
const toast = useToast();

// 设置面板显示状态
const showSettings = ref(false);

// 音频设置相关状态
const autoPlayAudio = ref(false);
const audioPlayDelay = ref(500);

// 计算属性：秒级别的延迟值
const audioPlayDelayInSeconds = computed({
    get: () => audioPlayDelay.value / 1000,
    set: (value) => {
        audioPlayDelay.value = value * 1000;
    }
});

// 界面设置相关状态
const showMilestones = ref(true);

// 音色选择相关状态
const showVoiceList = ref(false);
const voiceList = ref([
    { id: 1, name: '标准女声', description: '清晰自然的女性声音' },
    { id: 2, name: '温柔女声', description: '柔和亲切的女性声音' },
    { id: 3, name: '标准男声', description: '清晰自然的男性声音' },
    { id: 4, name: '磁性男声', description: '低沉有磁性的男性声音' },
    { id: 5, name: '活力童声', description: '活泼可爱的儿童声音' }
]);
const selectedVoice = ref(voiceList.value[0]);

/**
 * 格式化延迟时间显示
 * @param {number} delay - 延迟时间(毫秒)
 * @returns {string} 格式化后的延迟时间
 */
const formatDelay = (delay) => {
    const seconds = delay / 1000;
    return seconds === 0 ? '无延迟' : `${seconds}秒`;
};

/**
 * 切换设置面板显示状态
 */
const toggleSettings = () => {
    showSettings.value = !showSettings.value;

    // 如果有其他音频正在播放，可以考虑暂停
    if (showSettings.value) {
        audioManager.stopAll();
    }
};

/**
 * 更新音频设置
 */
const updateAudioSettings = () => {
    trainerStore.setAudioPlayConfig({
        autoPlay: autoPlayAudio.value,
        delay: audioPlayDelay.value
    });
};

/**
 * 更新界面设置
 */
const updateInterfaceSettings = () => {
    trainerStore.setInterfaceConfig({
        showMilestones: showMilestones.value
    });
};

/**
 * 重置所有设置为默认值
 */
const resetSettings = async () => {
    await trainerStore.resetAllSettings();

    // 从 store 中重新获取设置值
    autoPlayAudio.value = trainerStore.autoPlayAudio;
    audioPlayDelay.value = trainerStore.audioPlayDelay;
    showMilestones.value = trainerStore.showMilestones;
};

/**
 * 切换音色选择列表显示状态
 */
const toggleVoiceSelector = () => {
    showVoiceList.value = !showVoiceList.value;
};

/**
 * 选择音色
 * @param {Object} voice - 选中的音色对象
 */
const selectVoice = (voice) => {
    selectedVoice.value = voice;
    showVoiceList.value = false;

    // 更新音色设置到 store
    trainerStore.setVoiceType(voice.id);

    // 播放音色切换成功的提示
    toast.add({
        severity: 'success',
        summary: '音色已切换',
        detail: `已切换为${voice.name}`,
        life: 2000
    });
};

/**
 * 显示音色选择功能开发中的提示
 */
const showVoiceTypeInDevelopment = () => {
    toast.add({
        severity: 'info',
        summary: '功能开发中',
        detail: '音色选择功能正在开发中，敬请期待！',
        life: 3000
    });
};

// 监听 store 中的设置变化
watch(
    () => trainerStore.autoPlayAudio,
    (newValue) => {
        autoPlayAudio.value = newValue;
    }
);

watch(
    () => trainerStore.audioPlayDelay,
    (newValue) => {
        audioPlayDelay.value = newValue;
    }
);

watch(
    () => trainerStore.showMilestones,
    (newValue) => {
        showMilestones.value = newValue;
    }
);

// 生命周期钩子
onMounted(() => {
    // 从 store 中获取设置值
    autoPlayAudio.value = trainerStore.autoPlayAudio;
    audioPlayDelay.value = trainerStore.audioPlayDelay;
    showMilestones.value = trainerStore.showMilestones;

    // 如果 store 中有保存的音色设置，则加载
    if (trainerStore.voiceType) {
        const savedVoice = voiceList.value.find((v) => v.id === trainerStore.voiceType);
        if (savedVoice) {
            selectedVoice.value = savedVoice;
        }
    }
});

// 向父组件暴露设置状态
defineExpose({
    showMilestones
});
</script>

<style lang="scss" scoped>
.settings-btn {
    width: 24px;
}

// 音频设置面板样式
.audio-settings-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 80vh;
    background: #fff;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1002;
    overflow: hidden;

    .panel-header {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background: linear-gradient(to right, #1677ff, #4096ff);

        &:before {
            content: '';
            position: absolute;
            top: 6px;
            width: 40px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 4px;
        }

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: white;
        }

        .close-btn {
            position: absolute;
            right: 16px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;

            &:active {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0.95);
            }
        }
    }

    .panel-content {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;

        .setting-section {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .section-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0 0 12px 0;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }

            &.audio-settings {
                .section-title {
                    color: #1677ff;
                    border-bottom-color: rgba(22, 119, 255, 0.1);
                }

                .toggle-switch {
                    input:checked + label {
                        background-color: #1677ff;
                    }
                }

                .slider-container {
                    .delay-slider,
                    .volume-slider {
                        &::-webkit-slider-thumb {
                            background: linear-gradient(135deg, #1677ff, #4096ff);
                        }

                        &::-moz-range-thumb {
                            background: linear-gradient(135deg, #1677ff, #4096ff);
                        }
                    }
                }
            }

            &.interface-settings {
                .section-title {
                    color: #ff9f43;
                    border-bottom-color: rgba(255, 159, 67, 0.1);
                }

                .toggle-switch {
                    input:checked + label {
                        background-color: #ff9f43;
                    }
                }

                .slider-container {
                    .delay-slider,
                    .volume-slider {
                        &::-webkit-slider-thumb {
                            background: linear-gradient(135deg, #ff9f43, #ffbe7d);
                        }

                        &::-moz-range-thumb {
                            background: linear-gradient(135deg, #ff9f43, #ffbe7d);
                        }
                    }
                }
            }
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 8px 0;

            &:last-child {
                margin-bottom: 0;
            }

            .setting-info {
                display: flex;
                flex-direction: column;

                .setting-label {
                    font-size: 14px;
                    color: #333;
                    font-weight: 500;
                    margin-bottom: 2px;
                }

                .setting-desc {
                    font-size: 12px;
                    color: #999;
                }
            }

            // 开关样式
            .toggle-switch {
                position: relative;
                width: 44px;
                height: 24px;

                input {
                    opacity: 0;
                    width: 0;
                    height: 0;

                    &:checked + label {
                        &:before {
                            transform: translateX(20px);
                        }
                    }
                }

                label {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: #ccc;
                    border-radius: 24px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:before {
                        position: absolute;
                        content: '';
                        height: 20px;
                        width: 20px;
                        left: 2px;
                        bottom: 2px;
                        background-color: white;
                        border-radius: 50%;
                        transition: all 0.3s;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                    }
                }
            }

            // 滑块样式
            .slider-container {
                width: 140px;

                .delay-slider,
                .volume-slider {
                    -webkit-appearance: none;
                    width: 100%;
                    height: 4px;
                    border-radius: 2px;
                    background: #ddd;
                    outline: none;

                    &::-webkit-slider-thumb {
                        -webkit-appearance: none;
                        appearance: none;
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        cursor: pointer;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                    }

                    &::-moz-range-thumb {
                        width: 16px;
                        height: 16px;
                        border-radius: 50%;
                        cursor: pointer;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
                        border: none;
                    }
                }
            }
        }
    }

    .panel-footer {
        padding: 40px 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        justify-content: center;

        .reset-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            background: rgba(22, 119, 255, 0.1);
            color: #1677ff;
            border: none;
            border-radius: 20px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;

            i {
                margin-right: 4px;
                font-size: 12px;
            }

            &:active {
                background: rgba(22, 119, 255, 0.2);
                transform: scale(0.98);
            }
        }
    }
}

// 遮罩层样式
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    z-index: 1001;
    animation: fade-in 0.3s ease;
}

// 过渡动画
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.slide-up-enter-from,
.slide-up-leave-to {
    transform: translateY(100%);
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

// 音色选择样式
.voice-type-setting {
    .voice-selector {
        cursor: pointer;

        .selected-voice {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background: rgba(22, 119, 255, 0.08);
            border-radius: 16px;
            color: #1677ff;
            font-size: 13px;
            transition: all 0.2s;

            i {
                margin-left: 4px;
                font-size: 12px;
                transition: transform 0.3s ease;
            }

            &:active {
                background: rgba(22, 119, 255, 0.15);
                transform: scale(0.98);
            }
        }
    }
}

// 音色选择面板
.voice-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    z-index: 1003; // 确保在设置面板之上
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fade-in 0.3s ease;
}

.voice-panel {
    width: 90%;
    max-width: 320px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    animation: fade-scale-in 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.voice-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(to right, #1677ff, #4096ff);
    color: white;

    h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
    }

    .close-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        cursor: pointer;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s;

        &:active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0.95);
        }
    }
}

.voice-panel-content {
    max-height: 60vh;
    overflow-y: auto;

    .voice-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s;

        &:not(:last-child) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .voice-item-content {
            display: flex;
            flex-direction: column;

            .voice-name {
                font-size: 15px;
                color: #333;
                margin-bottom: 4px;
            }

            .voice-desc {
                font-size: 12px;
                color: #999;
            }
        }

        i {
            color: #1677ff;
            font-size: 16px;
        }

        &:active {
            background: rgba(22, 119, 255, 0.05);
        }

        &.active {
            background: rgba(22, 119, 255, 0.08);

            .voice-name {
                color: #1677ff;
                font-weight: 500;
            }
        }
    }
}

// 弹出动画
@keyframes fade-scale-in {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
