<template>
    <div class="smart-clock-container">
        <Loading :config="loadingConfig" @close="handleLoadingClose" />

        <iframe
            ref="clockIframe"
            v-if="iframeUrl"
            :src="iframeUrl"
            width="100%"
            height="100%"
            frameborder="0"
            @load="handleIframeLoad"
        ></iframe>

        <!-- 添加语音输入面板及遮罩层 -->
        <div v-if="iframeStore.voiceInputVisible" class="voice-input-overlay" @click="handleOverlayClick">
            <div class="voice-input-wrapper" @click.stop>
                <VoiceInputPanel
                    :auto-transcribe="iframeStore.voiceInputOptions.autoTranscribe"
                    :max-duration="iframeStore.voiceInputOptions.maxDuration"
                    @transcript-complete="handleTranscriptComplete"
                    @recording-complete="handleRecordingComplete"
                    @close="handleVoiceInputClose"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useAppStore } from '@/stores/app';
import { useIframeService } from '@/services';
import Loading from '@/components/Loading.vue';
import VoiceInputPanel from '@/components/VoiceInput/panel.vue';
import * as dd from 'dingtalk-jsapi';
import { getBusinessUrl, BUSINESS_TYPES } from '@/utils/urls';
import { useIframeStore } from '@/stores/iframe';

const title = import.meta.env.VITE_SMART_CLOCK_TITLE || '智慧打卡';
dd.biz.navigation.setTitle({
    title: title
});

const appStore = useAppStore();
const iframeService = useIframeService();
const iframeStore = useIframeStore();

const clockIframe = ref(null);

// iframe相关
const iframeUrl = ref('');

// Loading配置
const loadingConfig = ref({
    visible: true,
    status: 'loading',
    message: '加载中...'
});

// 更新loading状态
const updateLoadingConfig = (status, message, visible = true) => {
    loadingConfig.value.status = status;
    loadingConfig.value.message = message;
    loadingConfig.value.visible = visible;
};

// 初始化
const init = async () => {
    try {
        const ticketResponse = await appStore.getTicket();
        if (ticketResponse?.returncode !== 0 || !ticketResponse?.result) {
            throw new Error(`获取钉钉票据失败: ${JSON.stringify(ticketResponse)}`);
        }

        iframeUrl.value = getBusinessUrl(BUSINESS_TYPES.SMART_CLOCK, {
            dingdingTicket: ticketResponse.result
        });

        // 立即设置消息监听器，而不是等待load事件
        // 使用nextTick确保DOM已更新
        await nextTick();
        if (clockIframe.value) {
            cleanupMessageListener = iframeService.setupMessageListener(clockIframe.value.contentWindow);
        }
    } catch (error) {
        console.error('初始化失败:', error);
        updateLoadingConfig('failed', '初始化失败，请稍后再试', false);
    }
};

// 清理函数
let cleanupMessageListener = null;

const handleIframeLoad = () => {
    updateLoadingConfig('loaded', '初始化完成', false);

    // 消息监听器已在init函数中设置，这里不需要重复设置
    // 如果之前没有设置成功，这里作为备用方案
    if (!cleanupMessageListener && clockIframe.value?.contentWindow) {
        cleanupMessageListener = iframeService.setupMessageListener(clockIframe.value.contentWindow);
    }

    // 手动设置iframe为准备就绪状态，处理可能缓存的消息
    if (clockIframe.value?.contentWindow) {
        iframeService.setIframeReady(clockIframe.value.contentWindow);
    }
};

// 处理转写完成事件
const handleTranscriptComplete = content => {
    const options = iframeStore.voiceInputOptions;

    try {
        if (options.requestId) {
            // 使用service内部的iframeWindow引用，无需手动获取
            iframeService.sendResponseToIframe({
                requestId: options.requestId,
                success: true,
                data: {
                    content,
                    type: 'text',
                    timestamp: Date.now()
                }
            });
        }
    } catch (error) {
        console.error('处理转写完成事件失败:', error);
        // 即使发送失败也要确保清空语音输入状态，防止阻塞
    } finally {
        // 确保清空语音输入状态
        iframeStore.clearVoiceInput();
    }
};

// 处理录音完成事件
const handleRecordingComplete = recordingData => {
    const options = iframeStore.voiceInputOptions;

    try {
        if (options.requestId) {
            // 使用service内部的iframeWindow引用，无需手动获取
            iframeService.sendResponseToIframe({
                requestId: options.requestId,
                success: true,
                data: {
                    ...recordingData,
                    type: 'voice',
                    timestamp: Date.now()
                }
            });
        }
    } catch (error) {
        console.error('处理录音完成事件失败:', error);
        // 即使发送失败也要确保清空语音输入状态，防止阻塞
    } finally {
        // 确保清空语音输入状态
        iframeStore.clearVoiceInput();
    }
};

// 处理语音输入关闭事件
const handleVoiceInputClose = () => {
    const options = iframeStore.voiceInputOptions;

    try {
        if (options.requestId) {
            // 使用service内部的iframeWindow引用，无需手动获取
            iframeService.sendResponseToIframe({
                requestId: options.requestId,
                success: false,
                error: '用户取消了录音'
            });
        }
    } catch (error) {
        console.error('处理关闭事件失败:', error);
        // 即使发送失败也要确保清空语音输入状态，防止阻塞
    } finally {
        // 确保清空语音输入状态
        iframeStore.clearVoiceInput();
    }
};

// 处理遮罩层点击事件
const handleOverlayClick = () => {
    const options = iframeStore.voiceInputOptions;

    try {
        if (options.requestId) {
            // 使用service内部的iframeWindow引用，无需手动获取
            iframeService.sendResponseToIframe({
                requestId: options.requestId,
                success: false,
                error: '用户取消了录音'
            });
        }
    } catch (error) {
        console.error('处理遮罩层点击事件失败:', error);
        // 即使发送失败也要确保清空语音输入状态，防止阻塞
    } finally {
        // 确保清空语音输入状态
        iframeStore.clearVoiceInput();
    }
};

// 处理Loading关闭事件
const handleLoadingClose = () => {
    // 隐藏loading弹窗
    loadingConfig.value.visible = false;
    // 如果是初始化失败的情况，可以考虑重试或者显示错误页面
    if (loadingConfig.value.status === 'failed') {
        console.log('用户手动关闭了错误提示');
        // 可以在这里添加重试逻辑或者其他处理
    }
};

onMounted(async () => {
    updateLoadingConfig('loading', '加载中，请稍候...');
    await init();
});

onBeforeUnmount(() => {
    // 清理事件监听
    if (cleanupMessageListener) {
        cleanupMessageListener();
    }

    // 确保清空语音输入状态
    iframeStore.clearVoiceInput();

    // 重置iframe服务状态
    iframeService.reset();
});
</script>

<style lang="scss" scoped>
.smart-clock-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.title {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin: 10px 0 12px;
    text-align: center;
}

.iframe-container {
    width: 100%;
    margin: 0 auto;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.clock-iframe {
    width: 100%;
    height: 85vh;
    border: none;
    background-color: #fff;
}

/* 遮罩层样式 */
.voice-input-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    backdrop-filter: blur(5px);
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.voice-input-wrapper {
    width: 85%;
    max-width: 400px;
    position: relative;
    border-radius: 12px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>
