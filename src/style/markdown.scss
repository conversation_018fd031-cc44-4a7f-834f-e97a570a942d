.markdown-body {
    background-color: transparent !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    word-break: break-word !important;
    color: $label-primary !important;

    a {
        color: $system-blue !important;
        text-decoration: none !important;
        font-weight: 600 !important;
        margin: 0 2px !important;
        border-bottom: 1px solid transparent !important;
        transition: border-color 0.2s ease !important;

        &:hover {
            text-decoration: none !important;
            border-bottom-color: $system-blue !important;
        }
    }

    img {
        margin: 16px 0;
        width: initial !important;
        max-width: 100% !important;
        border-radius: 10px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.25s ease;

        &:hover {
            opacity: 0.95;
            transform: scale(0.99);
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
        }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        margin-top: 24px !important;
        margin-bottom: 16px !important;
        font-weight: 600 !important;
        line-height: 1.25 !important;
    }

    h1 {
        font-size: 1.6em !important;
        padding-bottom: 0.3em !important;
        border-bottom: 1px solid $separator-color-opaque !important;
    }

    h2 {
        font-size: 1.3em !important;
        padding-bottom: 0.3em !important;
        border-bottom: 1px solid $separator-color-opaque !important;
    }

    h3 {
        font-size: 1.15em !important;
    }

    h4 {
        font-size: 1em !important;
    }

    h5,
    h6 {
        font-size: 0.85em !important;
    }

    pre {
        background-color: $system-grouped-background-tertiary !important;
        border-radius: 8px !important;
        padding: 16px !important;
        overflow-x: auto !important;
        margin: 16px 0 !important;
        position: relative !important;

        &::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 4px !important;
            background: linear-gradient(90deg, $system-blue, rgba($system-blue, 0.5)) !important;
            border-radius: 4px 4px 0 0 !important;
            opacity: 0.7 !important;
        }

        code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
            font-size: 13px !important;
            line-height: 1.45 !important;
            tab-size: 2 !important;
        }
    }

    code:not(pre code) {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace !important;
        font-size: 13px !important;
        padding: 0.2em 0.4em !important;
        margin: 0 !important;
        background-color: rgba($system-grouped-background-tertiary, 0.5) !important;
        border-radius: 3px !important;
    }

    p {
        margin: 12px 0 !important;
    }

    blockquote {
        margin: 16px 0 !important;
        padding: 0 16px !important;
        color: rgba($label-primary, 0.8) !important;
        border-left: 4px solid rgba($system-blue, 0.4) !important;

        p {
            margin: 8px 0 !important;
        }

        blockquote {
            border-left-color: rgba($system-blue, 0.2) !important;
        }
    }

    // 列表项内的段落
    li p {
        margin: 4px 0 !important;
    }

    // 紧凑列表样式（连续的列表项）
    li + li {
        margin-top: 4px !important;
    }

    // 任务列表样式
    li[class*='task-list-item'] {
        list-style: none !important;
        padding-left: 0 !important;

        &::before {
            display: none !important;
        }

        input[type='checkbox'] {
            margin-right: 8px !important;
            margin-left: -20px !important;
            accent-color: $system-blue !important;
            transform: scale(1.1) !important;
        }
    }

    hr {
        height: 1px !important;
        margin: 24px 0 !important;
        background-color: $separator-color-opaque !important;
        border: none !important;
    }

    strong {
        font-weight: 700 !important;
        color: $system-blue !important;
        background: linear-gradient(transparent 70%, rgba($system-blue, 0.15) 70%) !important;
        padding: 0 2px !important;
        border-radius: 2px !important;
    }

    em {
        font-style: italic !important;
    }

    del {
        text-decoration: line-through !important;
    }

    .table-wrapper {
        overflow-x: auto !important;
        width: 100% !important;
        margin: 16px 0 !important;
        -webkit-overflow-scrolling: touch !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    }

    table {
        border-collapse: collapse !important;
        width: auto !important;
        min-width: 100% !important;
        margin: 0 !important;
        overflow-x: visible !important;
        white-space: nowrap !important;
        -webkit-overflow-scrolling: touch !important;
        border-radius: 8px !important;
        overflow: hidden !important;

        thead {
            background-color: $system-grouped-background-secondary !important;
            position: sticky !important;
            top: 0 !important;
            z-index: 1 !important;
        }

        th,
        td {
            border: 1px solid $separator-color-opaque !important;
            padding: 10px 14px !important;
            text-align: left !important;
            min-width: 80px !important;
            font-size: 13px !important;
        }

        th {
            background-color: $system-grouped-background-secondary !important;
            font-weight: 600 !important;
            color: $label-primary !important;
        }

        tbody tr:nth-child(even) {
            background-color: $system-grouped-background-tertiary !important;
        }

        tbody tr:hover {
            background-color: rgba($system-blue, 0.05) !important;
            transition: background-color 0.15s ease !important;
        }
    }

    kbd {
        display: inline-block !important;
        padding: 3px 5px !important;
        font-size: 11px !important;
        line-height: 10px !important;
        color: $label-primary !important;
        vertical-align: middle !important;
        background-color: $system-grouped-background-tertiary !important;
        border: 1px solid $separator-color-opaque !important;
        border-radius: 3px !important;
        box-shadow: inset 0 -1px 0 $separator-color-opaque !important;
    }

    sup,
    sub {
        font-size: 75% !important;
        line-height: 0 !important;
        position: relative !important;
        vertical-align: baseline !important;
    }

    sup {
        top: -0.5em !important;
    }

    sub {
        bottom: -0.25em !important;
    }

    dl {
        margin: 16px 0 !important;
        padding: 0 !important;
    }

    dt {
        font-weight: 600 !important;
        margin-top: 16px !important;
    }

    dd {
        margin-left: 20px !important;
        margin-bottom: 16px !important;
    }

    figure {
        margin: 16px 0 !important;
        text-align: center !important;

        img {
            margin: 0 auto 8px !important;
        }

        figcaption {
            font-size: 12px !important;
            color: rgba($label-primary, 0.7) !important;
            text-align: center !important;
        }
    }

    .highlight-line {
        background-color: rgba($system-blue, 0.1) !important;
        display: block !important;
        margin: 0 -16px !important;
        padding: 0 16px !important;
    }

    // 换行符间距
    br {
        content: '' !important;
        display: block !important;
        margin: 12px 0 !important;
    }
}
