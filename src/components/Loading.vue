<template>
    <Dialog
        :visible="config.visible"
        header=" "
        :closable="false"
        modal
        :dismissableMask="config.status !== 'loading'"
        blockScroll
        :pt="{
            header: {
                class: 'custom-dialog__header',
            },
        }"
    >
        <div class="productDetail-dialog__content">
            <i v-if="config.status === 'loaded'" class="pi pi-check-circle"></i>
            <i v-if="config.status === 'failed'" class="pi pi-exclamation-circle"></i>
            <i v-if="config.status === 'loading'" class="pi pi-spin pi-spinner"></i>
            <p>{{ config.message }}</p>
        </div>
    </Dialog>
</template>
<script setup>
const props = defineProps({
    config: {
        type: Object,
        default: () => ({
            visible: false,
            status: 'loading',
            message: '加载中...'
        })
    }
});
</script>
<style lang="scss">
.productDetail-dialog__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    p {
        margin-top: 15px;
    }

    .pi {
        font-size: 1.25rem;
    }
}

.custom-dialog__header {
    padding: 10px !important;
}

.custom-dialog__header2 {
    padding: 15px !important;
}
</style>
