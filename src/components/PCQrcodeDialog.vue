<template>
    <Dialog
        v-model:visible="dialogVisible"
        modal
        :closable="showCloseButton"
        :style="{ width: '90%', maxWidth: '400px' }"
        class="pc-dialog"
    >
        <template #header>
            <div class="dialog-header">
                <Button
                    v-if="showBackButton"
                    icon="pi pi-arrow-left"
                    text
                    @click="handleGoBack"
                    class="back-button"
                />
                <h3 class="dialog-title">请使用钉钉移动端访问</h3>
            </div>
        </template>
        <div class="dialog-content">
            <p class="dialog-desc">
                当前功能<span class="highlight-text">仅支持钉钉移动端</span
                >使用，请用<span class="highlight-text">钉钉APP</span
                >扫描下方二维码继续操作
            </p>
            <div class="qrcode-wrapper">
                <qrcode-vue :value="currentUrl" :size="200" level="H" />
            </div>
        </div>
    </Dialog>
</template>

<script setup>
import { computed } from 'vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import QrcodeVue from 'qrcode.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    /**
     * 是否显示返回按钮
     */
    showBackButton: {
        type: Boolean,
        default: true
    },
    /**
     * 是否显示关闭按钮
     */
    showCloseButton: {
        type: Boolean,
        default: true
    }
});

const emit = defineEmits(['update:visible']);

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 获取当前页面完整URL
const currentUrl = computed(() => window.location.href);

/**
 * 处理返回上一页操作
 * @returns {void}
 */
const handleGoBack = () => {
    window.history.back();
};
</script>

<style lang="scss" scoped>
.pc-dialog {
    :deep(.p-dialog-header) {
        padding: 20px 24px 0;
    }

    .dialog-header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }

    .back-button {
        position: absolute;
        left: 0;
        color: #666;

        &:focus {
            box-shadow: none;
        }
    }

    .dialog-title {
        margin: 0;
        font-size: 18px;
        color: #1f1f39;
        text-align: center;
    }

    :deep(.p-dialog-content) {
        padding: 20px 24px 24px;
    }

    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .dialog-desc {
        font-size: 15px;
        color: #666;
        line-height: 1.6;
        text-align: center;
        margin-bottom: 20px;

        .highlight-text {
            color: #1890ff;
            font-weight: 600;
        }
    }

    .qrcode-wrapper {
        background: #fff;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}
</style>
