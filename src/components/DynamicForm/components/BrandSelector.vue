<template>
    <div class="brand-selector">
        <!-- 头部 -->
        <div class="brand-header">
            <button class="back-btn" @click="handleClose">
                <i class="pi pi-chevron-left"></i>
            </button>
            <h3 class="brand-title">选择品牌</h3>
            <button class="close-btn" @click="handleClose">
                <i class="pi pi-times"></i>
            </button>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="pi pi-search search-icon"></i>
                <input
                    v-model="searchKeyword"
                    type="text"
                    placeholder="搜索品牌名称"
                    class="search-input"
                    @input="handleSearch"
                />
                <i v-if="searchKeyword" class="pi pi-times clear-icon" @click="clearSearch"></i>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
            <i class="pi pi-spinner pi-spin loading-icon"></i>
            <span>正在加载品牌数据...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-state">
            <i class="pi pi-exclamation-triangle error-icon"></i>
            <span>{{ error }}</span>
            <button @click="loadBrandData" class="retry-button">重试</button>
        </div>

        <!-- 品牌列表 -->
        <div v-else-if="filteredBrands.length > 0" class="brand-list">
            <!-- 显示分组 -->
            <template v-if="!searchKeyword">
                <div v-for="(group, letter) in groupedBrands" :key="letter" class="brand-group">
                    <div class="group-header">{{ letter }}</div>
                    <div class="brand-items">
                        <div
                            v-for="brand in group"
                            :key="brand.id"
                            class="brand-item"
                            @click="selectBrand(brand)"
                        >
                            <div class="brand-logo">
                                <img
                                    v-if="brand.logo"
                                    :src="brand.logo"
                                    :alt="brand.name"
                                    @error="handleImageError($event)"
                                />
                                <div v-else class="logo-placeholder">
                                    {{ brand.name.charAt(0) }}
                                </div>
                            </div>
                            <div class="brand-info">
                                <div class="brand-name">{{ brand.name }}</div>
                                <div v-if="brand.country" class="brand-country">
                                    {{ brand.country }}
                                </div>
                            </div>
                            <i class="pi pi-chevron-right select-icon"></i>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索结果 -->
            <template v-else>
                <div class="search-results">
                    <div class="search-header">搜索结果 ({{ filteredBrands.length }})</div>
                    <div class="brand-items">
                        <div
                            v-for="brand in filteredBrands"
                            :key="brand.id"
                            class="brand-item"
                            @click="selectBrand(brand)"
                        >
                            <div class="brand-logo">
                                <img
                                    v-if="brand.logo"
                                    :src="brand.logo"
                                    :alt="brand.name"
                                    @error="handleImageError($event)"
                                />
                                <div v-else class="logo-placeholder">
                                    {{ brand.name.charAt(0) }}
                                </div>
                            </div>
                            <div class="brand-info">
                                <div
                                    class="brand-name"
                                    v-html="highlightSearchTerm(brand.name)"
                                ></div>
                                <div v-if="brand.country" class="brand-country">
                                    {{ brand.country }}
                                </div>
                            </div>
                            <i class="pi pi-chevron-right select-icon"></i>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
            <i class="pi pi-search empty-icon"></i>
            <div class="empty-message">
                <span v-if="searchKeyword">未找到匹配的品牌</span>
                <span v-else>暂无品牌数据</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useSmartDataStore } from '@/stores/smartData';

const props = defineProps({
    // 组件配置
    config: {
        type: Object,
        default: () => ({})
    }
});

// 发出的事件
const emit = defineEmits(['select', 'close']);

// 响应式状态
const smartDataStore = useSmartDataStore();
const loading = ref(false);
const error = ref('');
const searchKeyword = ref('');
const brands = ref([]);

/**
 * 过滤后的品牌列表
 */
const filteredBrands = computed(() => {
    if (!searchKeyword.value) {
        return brands.value;
    }

    const keyword = searchKeyword.value.toLowerCase();
    return brands.value.filter(
        brand =>
            brand.name.toLowerCase().includes(keyword) ||
            (brand.country && brand.country.toLowerCase().includes(keyword))
    );
});

/**
 * 按首字母分组的品牌
 */
const groupedBrands = computed(() => {
    const groups = {};

    brands.value.forEach(brand => {
        const firstLetter = (brand.firstletter || brand.name.charAt(0)).toUpperCase();
        if (!groups[firstLetter]) {
            groups[firstLetter] = [];
        }
        groups[firstLetter].push(brand);
    });

    // 排序分组
    const sortedGroups = {};
    Object.keys(groups)
        .sort()
        .forEach(key => {
            // 对每个分组内的品牌按名称排序
            sortedGroups[key] = groups[key].sort((a, b) => a.name.localeCompare(b.name));
        });

    return sortedGroups;
});

/**
 * 加载品牌数据
 */
const loadBrandData = async () => {
    loading.value = true;
    error.value = '';

    try {
        const { data_source } = props.config;
        if (!data_source) {
            throw new Error('缺少数据源配置');
        }

        const brandData = await smartDataStore.getBrandList(data_source.url, {});
        brands.value = brandData;
    } catch (err) {
        console.error('加载品牌数据失败:', err);
        error.value = err.message || '加载品牌数据失败，请重试';
        brands.value = [];
    } finally {
        loading.value = false;
    }
};

/**
 * 清除搜索
 */
const clearSearch = () => {
    searchKeyword.value = '';
};

/**
 * 选择品牌
 * @param {Object} brand - 选择的品牌对象
 */
const selectBrand = brand => {
    const result = {
        value: brand.id,
        label: brand.name,
        lastLabel: brand.name,
        data: brand
    };

    emit('select', result);
    emit('close');
};

/**
 * 处理关闭
 */
const handleClose = () => {
    emit('close');
};

/**
 * 处理图片加载错误
 * @param {Event} event - 错误事件
 */
const handleImageError = event => {
    // 隐藏失败的图片，显示占位符
    event.target.style.display = 'none';
    event.target.nextElementSibling?.classList.remove('hidden');
};

/**
 * 高亮搜索关键词
 * @param {string} text - 原始文本
 * @returns {string} 高亮后的 HTML
 */
const highlightSearchTerm = text => {
    if (!searchKeyword.value) {
        return text;
    }

    const keyword = searchKeyword.value;
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
};

// 生命周期钩子
onMounted(() => {
    if (props.config && props.config.data_source) {
        loadBrandData();
    }
});
</script>

<style lang="scss" scoped>
.brand-selector {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #ffffff 0%, #fafafa 100%);
    overflow: hidden;

    .brand-header {
        display: flex;
        align-items: center;
        padding: 12px 14px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(180deg, #ffffff 0%, #fafafa 100%);
        min-height: 48px;

        .back-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                background-color: rgba(255, 102, 0, 0.08);
                transform: scale(1.05);
            }

            i {
                font-size: 12px;
                color: $label-primary;
                transition: color 0.2s ease;
            }
        }

        .brand-title {
            flex: 1;
            text-align: center;
            font-size: 15px;
            font-weight: 600;
            color: $label-primary;
            margin: 0;
            letter-spacing: 0.2px;
        }

        .close-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                background-color: rgba(255, 102, 0, 0.08);
                transform: scale(1.05);
            }

            i {
                font-size: 12px;
                color: $label-primary;
                transition: color 0.2s ease;
            }
        }
    }
}

.search-container {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(255, 102, 0, 0.08);
    background: linear-gradient(135deg, #fff9f5 0%, #ffffff 100%);
    position: relative;

    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        &:focus-within .search-icon {
            color: #ff6600;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            color: #999;
            font-size: 12px;
            z-index: 1;
            transition: color 0.2s ease;
        }

        .search-input {
            width: 100%;
            padding: 10px 32px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            font-size: 13px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
            color: #333;
            outline: none;
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

            &:focus {
                border-color: rgba(255, 102, 0, 0.4);
            }

            &::placeholder {
                color: $label-secondary;
                font-size: 12px;
            }
        }

        .clear-icon {
            position: absolute;
            right: 10px;
            color: #999;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeInScale 0.2s ease-out;

            &:hover {
                color: #ff6600;
                background: rgba(255, 102, 0, 0.1);
                transform: scale(1.1);
            }
        }
    }
}

.loading-state,
.error-state,
.empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;

    .loading-icon,
    .error-icon,
    .empty-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .loading-icon {
        color: #ff6600;
    }

    .error-icon {
        color: #f56565;
    }

    .empty-icon {
        color: #a0a0a0;
    }

    span {
        font-size: 13px;
        color: #666;
        line-height: 1.3;
        font-weight: 400;
    }

    .empty-message {
        font-size: 13px;
        color: #666;
        font-weight: 400;
    }

    .retry-button {
        margin-top: 12px;
        padding: 6px 12px;
        background-color: #ff6600;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background-color: #e55a00;
            transform: translateY(-1px);
        }
    }
}

.brand-list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.brand-group {
    .group-header {
        position: sticky;
        top: 0;
        padding: 4px 12px;
        background: #ff6600;
        border-bottom: 1px solid rgba(255, 102, 0, 0.08);
        font-size: 14px;
        color: #fff;
        text-transform: uppercase;
        z-index: 1;
    }
}

.search-results {
    .search-header {
        padding: 8px 12px;
        background: linear-gradient(135deg, #fff5f0 0%, #fafafa 100%);
        border-bottom: 1px solid rgba(255, 102, 0, 0.1);
        font-size: 11px;
        font-weight: 600;
        color: #ff6600;
        letter-spacing: 0.3px;
    }
}

.brand-items {
    .brand-item {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);
        border-left: 2px solid transparent;
        cursor: pointer;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &:hover {
            background: linear-gradient(90deg, #fff8f3 0%, #ffffff 100%);
            border-left-color: rgba(255, 102, 0, 0.6);
            transform: translateX(2px);
        }

        &:active {
            background: linear-gradient(90deg, #fff5f0 0%, #ffffff 100%);
            transform: translateX(1px) scale(0.99);
            transition-duration: 0.1s;
        }

        .brand-logo {
            width: 30px;
            height: 30px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .logo-placeholder {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #fff5f0 0%, #ffede5 100%);
                color: #ff6600;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                border: 1px solid rgba(255, 102, 0, 0.2);
            }
        }

        .brand-info {
            flex: 1;
            min-width: 0;

            .brand-name {
                font-size: 13px;
                font-weight: 500;
                color: #333;
                line-height: 1.2;
                margin-bottom: 1px;

                :deep(mark) {
                    background: linear-gradient(135deg, #fff3e6 0%, #ffe6cc 100%);
                    color: #ff6600;
                    padding: 1px 3px;
                    border-radius: 3px;
                    font-weight: 600;
                    box-shadow: 0 1px 2px rgba(255, 102, 0, 0.1);
                }
            }

            .brand-country {
                font-size: 11px;
                color: #999;
                line-height: 1.1;
                font-weight: 400;
            }
        }

        .select-icon {
            color: #ccc;
            font-size: 10px;
            margin-left: 6px;
            transition: all 0.2s ease;
            transform: scale(0.9);
        }

        &:hover .select-icon {
            color: #ff6600;
            transform: scale(1) translateX(2px);
        }

        // 品牌项目进入动画
        &:nth-child(n) {
            animation: fadeInUp 0.4s ease-out both;
        }

        @for $i from 1 through 20 {
            &:nth-child(#{$i}) {
                animation-delay: #{($i - 1) * 0.02s};
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(6px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
