<template>
    <div
        class="bottom-selector"
        :class="{ 'bottom-selector--visible': visible }"
        @click="handleBackdropClick"
    >
        <div class="bottom-content" @click.stop>
            <!-- 业务组件插槽 -->
            <slot :config="config" :onSelect="handleSelect" :onClose="handleClose"></slot>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    // 显示状态
    modelValue: {
        type: Boolean,
        default: false
    },
    // 配置信息
    config: {
        type: Object,
        default: () => ({})
    }
});

// 发出的事件
const emit = defineEmits(['update:modelValue', 'select', 'close']);

// 计算属性
const visible = computed(() => props.modelValue);

/**
 * 处理背景点击关闭
 */
const handleBackdropClick = () => {
    handleClose();
};

/**
 * 处理关闭
 */
const handleClose = () => {
    emit('update:modelValue', false);
    emit('close');
};

/**
 * 处理选择
 * @param {Object} data - 选中的数据
 */
const handleSelect = data => {
    emit('select', data);
    handleClose();
};
</script>

<style lang="scss" scoped>
.bottom-selector {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.1s ease-in;

    &--visible {
        opacity: 1;
        pointer-events: auto;

        .bottom-content {
            transform: translateY(0) !important;
        }
    }

    .bottom-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 60vh;
        background-color: $system-background-primary;
        transform: translateY(100%);
        transition: transform 0.1s ease-in;
        display: flex;
        flex-direction: column;
        border-radius: 16px 16px 0 0;
    }
}
</style>
