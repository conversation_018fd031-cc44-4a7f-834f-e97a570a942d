<template>
    <div class="cascade-selector">
        <!-- 头部 -->
        <div class="cascade-header">
            <button class="back-btn" @click="handleClose">
                <i class="pi pi-chevron-left"></i>
            </button>
            <h3 class="cascade-title">{{ props.config.title || '请选择' }}</h3>
            <button class="close-btn" @click="handleClose">
                <i class="pi pi-times"></i>
            </button>
        </div>

        <!-- 滚动内容区域 -->
        <div class="cascade-content">
            <!-- 热门城市 -->
            <div class="hot-cities">
                <div class="hot-cities-title">
                    <i class="pi pi-star-fill hot-icon"></i>
                    热门城市
                </div>
                <div class="hot-cities-grid">
                    <div
                        v-for="city in hotCities"
                        :key="city.value"
                        class="hot-city-item"
                        @click="handleHotCitySelect(city)"
                    >
                        <span class="city-name">{{ city.label }}</span>
                        <div class="city-shine"></div>
                    </div>
                </div>
            </div>

            <!-- 手风琴列表 -->
            <div class="cascade-accordion">
                <div v-if="isLoading && treeData.length === 0" class="loading-state">
                    <i class="pi pi-spin pi-spinner loading-icon"></i>
                    <span>加载中...</span>
                </div>

                <div v-else-if="hasError" class="error-state">
                    <i class="pi pi-exclamation-triangle error-icon"></i>
                    <span>{{ errorMessage }}</span>
                    <button class="retry-btn" @click="loadRootLevel">重试</button>
                </div>

                <div v-else-if="treeData.length === 0" class="empty-state">
                    <i class="pi pi-inbox empty-icon"></i>
                    <span>暂无数据</span>
                </div>

                <div v-else class="accordion-list">
                    <template v-for="item in treeData" :key="item.value">
                        <div class="accordion-item" :class="{ 'is-expanded': item.expanded }">
                            <div
                                class="accordion-header"
                                :class="{ 'has-children': item.hasChildren }"
                                :style="{ paddingLeft: '12px' }"
                                @click="handleItemClick(item, 0)"
                            >
                                <div class="accordion-content">
                                    <i
                                        v-if="item.hasChildren"
                                        class="pi accordion-icon"
                                        :class="
                                            item.isLoading
                                                ? 'pi-spin pi-spinner'
                                                : item.expanded
                                                ? 'pi-chevron-down'
                                                : 'pi-chevron-right'
                                        "
                                    ></i>
                                    <span class="accordion-label">{{ item.label }}</span>
                                </div>
                                <i
                                    v-if="!item.hasChildren"
                                    class="pi pi-check accordion-select-icon"
                                ></i>
                            </div>

                            <!-- 递归渲染子级 -->
                            <div
                                v-if="item.expanded && item.children.length > 0"
                                class="accordion-children"
                            >
                                <template v-for="child in item.children" :key="child.value">
                                    <div
                                        class="accordion-item"
                                        :class="{ 'is-expanded': child.expanded }"
                                    >
                                        <div
                                            class="accordion-header"
                                            :class="{ 'has-children': child.hasChildren }"
                                            :style="{ paddingLeft: '28px' }"
                                            @click="handleItemClick(child, 1)"
                                        >
                                            <div class="accordion-content">
                                                <i
                                                    v-if="child.hasChildren"
                                                    class="pi accordion-icon"
                                                    :class="
                                                        child.isLoading
                                                            ? 'pi-spin pi-spinner'
                                                            : child.expanded
                                                            ? 'pi-chevron-down'
                                                            : 'pi-chevron-right'
                                                    "
                                                ></i>
                                                <span class="accordion-label">{{
                                                    child.label
                                                }}</span>
                                            </div>
                                            <i
                                                v-if="!child.hasChildren"
                                                class="pi pi-check accordion-select-icon"
                                            ></i>
                                        </div>

                                        <!-- 第三级 -->
                                        <div
                                            v-if="child.expanded && child.children.length > 0"
                                            class="accordion-children"
                                        >
                                            <template
                                                v-for="grandchild in child.children"
                                                :key="grandchild.value"
                                            >
                                                <div class="accordion-item">
                                                    <div
                                                        class="accordion-header"
                                                        :style="{ paddingLeft: '44px' }"
                                                        @click="handleItemClick(grandchild, 2)"
                                                    >
                                                        <div class="accordion-content">
                                                            <span class="accordion-label">{{
                                                                grandchild.label
                                                            }}</span>
                                                        </div>
                                                        <i
                                                            class="pi pi-check accordion-select-icon"
                                                        ></i>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useSmartDataStore } from '@/stores/smartData';

const props = defineProps({
    // 级联配置
    config: {
        type: Object,
        default: () => ({})
    }
});

// 发出的事件
const emit = defineEmits(['select', 'close']);

// Store 实例
const smartDataStore = useSmartDataStore();

// 响应式状态
const treeData = ref([]);
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');

// 热门城市数据（写死）
const hotCities = ref([
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' },
    { label: '广州', value: 'guangzhou' },
    { label: '深圳', value: 'shenzhen' },
    { label: '杭州', value: 'hangzhou' },
    { label: '南京', value: 'nanjing' },
    { label: '苏州', value: 'suzhou' },
    { label: '天津', value: 'tianjin' },
    { label: '武汉', value: 'wuhan' },
    { label: '长沙', value: 'changsha' },
    { label: '重庆', value: 'chongqing' },
    { label: '成都', value: 'chengdu' }
]);

/**
 * 加载根级数据
 */
const loadRootLevel = async () => {
    const levels = props.config.cascade?.levels || [];
    if (levels.length === 0) {
        return;
    }

    const firstLevel = levels[0];
    const dataSource = firstLevel.data_source;
    if (!dataSource) {
        return;
    }

    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    try {
        const url = dataSource.url;
        const rawData = await smartDataStore.getCascadeListLevel1(url, {});

        // 根据配置提取数据
        const labelKey = dataSource.response_mapping?.label_key || 'label';
        const valueKey = dataSource.response_mapping?.value_key || 'value';

        const rawList = Array.isArray(rawData) ? rawData : [];

        treeData.value = rawList.map(item => ({
            label: item[labelKey],
            value: item[valueKey],
            raw: item,
            level: 0,
            expanded: false,
            children: [],
            hasChildren: levels.length > 1,
            isLoading: false
        }));

        console.log('根级数据加载完成:', treeData.value);
    } catch (error) {
        hasError.value = true;
        errorMessage.value = error.message;
        console.error('加载根级数据失败:', error);
    } finally {
        isLoading.value = false;
    }
};

/**
 * 处理节点展开
 * @param {Object} item - 要展开的节点
 */
const handleExpand = async item => {
    if (item.children.length > 0) {
        // 已有子数据，直接切换展开状态
        item.expanded = !item.expanded;
        return;
    }

    // 需要加载子数据
    const levels = props.config.cascade?.levels || [];
    const nextLevel = levels[item.level + 1];

    if (!nextLevel) {
        // 没有下一级了
        return;
    }

    const dataSource = nextLevel.data_source;
    if (!dataSource) {
        return;
    }

    item.isLoading = true;

    try {
        const url = dataSource.url;

        // 构建查询参数
        let queryParams = {};
        if (dataSource.params_mapping) {
            Object.entries(dataSource.params_mapping).forEach(([key, mapping]) => {
                if (mapping === 'parent_value') {
                    queryParams[key] = item.value;
                }
            });
        }

        const rawData = await smartDataStore.getCascadeListLevel2(url, queryParams);

        // 根据配置提取数据
        const labelKey = dataSource.response_mapping?.label_key || 'label';
        const valueKey = dataSource.response_mapping?.value_key || 'value';

        const rawList = Array.isArray(rawData) ? rawData : [];

        item.children = rawList.map(childItem => ({
            label: childItem[labelKey],
            value: childItem[valueKey],
            raw: childItem,
            level: item.level + 1,
            expanded: false,
            children: [],
            hasChildren: item.level + 2 < levels.length,
            isLoading: false,
            parent: item
        }));

        item.expanded = true;
        console.log(`节点 ${item.label} 的子数据加载完成:`, item.children);
    } catch (error) {
        console.error(`加载节点 ${item.label} 的子数据失败:`, error);
        // 可以在这里添加错误提示
    } finally {
        item.isLoading = false;
    }
};

/**
 * 处理项目点击
 * @param {Object} item - 点击的项目
 * @param {number} level - 当前级别
 */
const handleItemClick = (item, level) => {
    if (item.hasChildren) {
        // 有子级，展开或收起
        handleExpand(item);
    } else {
        // 没有子级，选择该项目
        const path = buildPath(item);
        handleSelect(item, path);
    }
};

/**
 * 处理选择
 * @param {Object} selectedItem - 选中的项目
 * @param {Array} path - 选择路径（用于生成显示标签）
 */
const handleSelect = (selectedItem, path) => {
    // 传递完整路径标签、最底层标签和实际值
    emit('select', {
        label: path.map(item => item.label).join(' - '), // 完整路径：如"北京市 - 朝阳区"
        lastLabel: selectedItem.label, // 最底层标签：如"朝阳区"
        value: selectedItem.value // 实际值
    });
};

/**
 * 处理关闭
 */
const handleClose = () => {
    emit('close');
};

/**
 * 处理热门城市选择
 * @param {Object} city - 选中的城市
 */
const handleHotCitySelect = city => {
    emit('select', {
        label: city.label,
        lastLabel: city.label,
        value: city.value
    });
};

/**
 * 构建选择路径
 * @param {Object} item - 当前项目
 */
const buildPath = item => {
    const path = [];
    let current = item;

    while (current) {
        path.unshift({
            label: current.label,
            value: current.value,
            level: current.level
        });
        current = current.parent;
    }

    return path;
};

// 生命周期钩子
onMounted(() => {
    if (props.config.cascade?.enabled) {
        loadRootLevel();
    }
});
</script>

<style lang="scss" scoped>
.cascade-selector {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: $system-background-primary;
    overflow-y: auto;

    .cascade-header {
        display: flex;
        align-items: center;
        padding: 12px 14px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        background: linear-gradient(180deg, #ffffff 0%, #fafafa 100%);
        min-height: 48px;

        .back-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                background-color: rgba(255, 102, 0, 0.08);
                transform: scale(1.05);
            }

            i {
                font-size: 12px;
                color: $label-primary;
                transition: color 0.2s ease;
            }
        }

        .cascade-title {
            flex: 1;
            text-align: center;
            font-size: 15px;
            font-weight: 600;
            color: $label-primary;
            margin: 0;
            letter-spacing: 0.2px;
        }

        .close-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
                background-color: rgba(255, 102, 0, 0.08);
                transform: scale(1.05);
            }

            i {
                font-size: 12px;
                color: $label-primary;
                transition: color 0.2s ease;
            }
        }
    }

    .cascade-content {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;

        .hot-cities {
            padding: 14px 12px 16px;
            background: linear-gradient(135deg, #fff9f5 0%, #ffffff 100%);
            border-bottom: 1px solid rgba(255, 102, 0, 0.08);
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, transparent, #ff6600, transparent);
                background-size: 200% 100%;
                animation: shimmer 3s ease-in-out infinite;
            }

            .hot-cities-title {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                color: #666;
                margin-bottom: 12px;
                font-weight: 500;
                letter-spacing: 0.3px;

                .hot-icon {
                    font-size: 11px;
                    color: #ff6600;
                    animation: pulse 2.5s ease-in-out infinite;
                }
            }

            .hot-cities-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;

                .hot-city-item {
                    position: relative;
                    padding: 8px 4px;
                    text-align: center;
                    font-size: 12px;
                    font-weight: 500;
                    color: #333;
                    background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
                    border: 1px solid rgba(0, 0, 0, 0.06);
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
                    overflow: hidden;
                    min-height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .city-name {
                        position: relative;
                        z-index: 2;
                        transition: color 0.25s ease;
                        line-height: 1.2;
                    }

                    .city-shine {
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: linear-gradient(
                            45deg,
                            transparent,
                            rgba(255, 255, 255, 0.4),
                            transparent
                        );
                        transform: rotate(45deg);
                        transition: transform 0.5s ease;
                        opacity: 0;
                    }

                    &:hover {
                        transform: translateY(-1px) scale(1.01);
                        background: linear-gradient(135deg, #fff5f0 0%, #ffede5 100%);
                        border-color: rgba(255, 102, 0, 0.3);
                        box-shadow: 0 4px 12px rgba(255, 102, 0, 0.12),
                            0 2px 6px rgba(0, 0, 0, 0.06);

                        .city-name {
                            color: #ff6600;
                            font-weight: 600;
                        }

                        .city-shine {
                            opacity: 1;
                            transform: rotate(45deg) translate(50%, 50%);
                        }
                    }

                    &:active {
                        transform: translateY(0) scale(0.99);
                        transition-duration: 0.1s;
                    }

                    // 为每个城市添加延迟动画
                    @for $i from 1 through 12 {
                        &:nth-child(#{$i}) {
                            animation: fadeInUp 0.5s ease-out #{($i - 1) * 0.03s} both;
                        }
                    }
                }
            }
        }

        .cascade-accordion {
            .loading-state,
            .error-state,
            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 32px 16px;
                text-align: center;

                i {
                    font-size: 24px;
                    margin-bottom: 8px;
                }

                span {
                    font-size: 13px;
                    color: #666;
                    margin-bottom: 12px;
                    font-weight: 400;
                }
            }

            .loading-state {
                .loading-icon {
                    color: #ff6600;
                }
            }

            .error-state {
                .error-icon {
                    color: #f56565;
                }

                .retry-btn {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 6px;
                    background-color: #ff6600;
                    color: white;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                        background-color: #e55a00;
                        transform: translateY(-1px);
                    }
                }
            }

            .empty-state {
                .empty-icon {
                    color: #a0a0a0;
                }
            }
        }
    }
}

// 手风琴项目样式
.accordion-item {
    .accordion-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 38px;
        cursor: pointer;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);
        border-left: 2px solid transparent;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
            background: linear-gradient(90deg, #fff8f3 0%, #ffffff 100%);
            border-left-color: rgba(255, 102, 0, 0.3);
        }

        &.has-children {
            .accordion-content {
                .accordion-label {
                    font-weight: 500;
                }
            }
        }

        .accordion-content {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;

            .accordion-icon {
                font-size: 10px;
                color: #999;
                transition: all 0.2s ease;
                width: 14px;
                text-align: center;
            }

            .accordion-label {
                font-size: 13px;
                color: #333;
                line-height: 1.3;
                font-weight: 400;
            }
        }

        // 第一级 - 精致的大字体
        &[style*='padding-left: 12px'] {
            .accordion-content .accordion-label {
                font-size: 14px;
                font-weight: 600;
                color: #222;
            }
        }

        // 第二级 - 中等字体
        &[style*='padding-left: 28px'] {
            .accordion-content .accordion-label {
                font-size: 13px;
                font-weight: 500;
                color: #444;
            }
        }

        // 第三级 - 小字体
        &[style*='padding-left: 44px'] {
            .accordion-content .accordion-label {
                font-size: 12px;
                font-weight: 400;
                color: #555;
            }
        }

        .accordion-select-icon {
            font-size: 10px;
            color: #ff6600;
            margin-right: 12px;
            opacity: 0;
            transition: all 0.2s ease;
            transform: scale(0.8);
        }

        &:hover .accordion-select-icon {
            opacity: 1;
            transform: scale(1);
        }
    }

    .accordion-children {
        background: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);
    }

    &.is-expanded {
        .accordion-header {
            background: linear-gradient(90deg, #fff5f0 0%, #ffffff 100%);
            border-left-color: #ff6600;

            .accordion-content .accordion-icon {
                color: #ff6600;
                transform: rotate(90deg);
            }
        }
    }
}

// 动画定义
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(6px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
