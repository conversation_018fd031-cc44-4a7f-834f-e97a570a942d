<template>
    <div
        class="inline-option"
        :class="{
            'inline-option--selected': hasValue,
            'inline-option--disabled': disabled,
            'inline-option--error': hasError
        }"
        @click="handleClick"
        :data-value="serializedValue"
        :data-id="id"
    >
        <span class="option-text">
            {{ displayText }}
        </span>
        <i v-if="showIcon" class="option-icon" :class="iconClass"></i>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    // 唯一标识ID
    id: {
        type: String,
        required: true
    },
    // 占位符文本
    placeholder: {
        type: String,
        default: '请选择'
    },
    // 当前值
    value: {
        type: [String, Object, Number],
        default: ''
    },
    // 双向绑定值
    modelValue: {
        type: [String, Object, Number],
        default: ''
    },
    // 动作配置
    action: {
        type: Object,
        required: true
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 是否必填
    required: {
        type: Boolean,
        default: true
    },
    // 是否显示图标
    showIcon: {
        type: Boolean,
        default: true
    },
    // 错误状态
    error: {
        type: String,
        default: ''
    }
});

// 发出的事件
const emit = defineEmits(['update:value', 'update:modelValue', 'action', 'click']);

// 计算属性
const currentValue = computed(() => {
    return props.value || props.modelValue;
});

const hasValue = computed(() => {
    return !!currentValue.value;
});

const hasError = computed(() => {
    return !!props.error;
});

const displayText = computed(() => {
    if (currentValue.value) {
        // 如果值是对象，尝试获取label字段
        if (typeof currentValue.value === 'object' && currentValue.value.label) {
            return currentValue.value.label;
        }
        // 否则直接返回值
        return String(currentValue.value);
    }
    return props.placeholder;
});

// 序列化值，用于 data-value 属性
const serializedValue = computed(() => {
    if (!currentValue.value) {
        return '';
    }

    try {
        if (typeof currentValue.value === 'object') {
            return JSON.stringify(currentValue.value);
        }
        return String(currentValue.value);
    } catch (e) {
        console.error('序列化值失败:', e);
        return '';
    }
});

const iconClass = computed(() => {
    const baseClass = 'pi';

    switch (props.action.type) {
        case 'OPEN_SIDEBAR':
            return `${baseClass} pi-chevron-right`;
        case 'OPEN_MODAL':
            return `${baseClass} pi-calendar`;
        default:
            return `${baseClass} pi-angle-down`;
    }
});

/**
 * 处理点击事件
 */
const handleClick = () => {
    if (props.disabled) {
        return;
    }

    // 发出动作事件
    emit('action', {
        action: props.action,
        context: {
            id: props.id,
            currentValue: currentValue.value,
            placeholder: props.placeholder
        }
    });

    // 发出点击事件
    emit('click', {
        id: props.id,
        action: props.action
    });
};

/**
 * 更新值
 * @param {any} newValue - 新值
 */
const updateValue = newValue => {
    emit('update:value', {
        id: props.id,
        value: newValue
    });
    emit('update:modelValue', newValue);
};

// 暴露给父组件的方法
defineExpose({
    updateValue,
    currentValue: currentValue.value,
    hasValue: hasValue.value
});
</script>

<style lang="scss" scoped>
.inline-option {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    border: 1px solid $system-blue;
    border-radius: 16px;
    background-color: rgba($system-blue, 0.05);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    min-height: 28px;

    &:hover:not(&--disabled) {
        background-color: rgba($system-blue, 0.15);
        border-color: $system-blue;
        box-shadow: 0 2px 8px rgba($system-blue, 0.15);
    }

    .option-text {
        font-size: 13px;
        line-height: 1.2;
        color: $system-blue;
        transition: color 0.2s ease;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 800;
    }

    .option-icon {
        font-size: 12px;
        color: $system-blue;
        transition: all 0.2s ease;
    }

    &--disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: $system-gray6;
        border-color: $fill-color-tertiary;

        &:hover {
            transform: none;
            box-shadow: none;
        }

        .option-text {
            color: $label-secondary;
            font-weight: normal;
        }

        .option-icon {
            color: $label-secondary;
        }
    }

    &--error {
        border-color: $system-red;
        background-color: rgba($system-red, 0.05);

        .option-text {
            color: $system-red;
        }

        .option-icon {
            color: $system-red;
        }
    }
}
</style>
