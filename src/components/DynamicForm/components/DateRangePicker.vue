<template>
    <div class="time-picker">
        <!-- 快速选择按钮 -->
        <div class="quick-select">
            <div class="section-header">
                <i class="pi pi-clock section-icon"></i>
                <h4 class="section-title">快速选择</h4>
            </div>
            <div class="quick-buttons">
                <button
                    v-for="quick in quickTimeOptions"
                    :key="quick.key"
                    class="quick-btn"
                    :class="{ 'quick-btn--active': selectedQuickOption === quick.key }"
                    @click="selectQuickTime(quick)"
                >
                    {{ quick.label }}
                </button>
            </div>
        </div>

        <!-- 分割线 -->
        <div class="section-divider">
            <span class="divider-text">或</span>
        </div>

        <!-- 自定义月份选择 -->
        <div class="custom-date-section">
            <div class="section-header">
                <i class="pi pi-calendar section-icon"></i>
                <h4 class="section-title">选择月份</h4>
            </div>
            <div class="month-input">
                <Calendar
                    id="month-picker"
                    v-model="selectedMonth"
                    view="month"
                    dateFormat="yy年mm月"
                    :show-icon="true"
                    :manualInput="false"
                    :maxDate="maxSelectableDate"
                    placeholder="请选择月份"
                    class="month-picker"
                    @date-select="handleMonthSelect"
                />
            </div>
        </div>

        <!-- 错误提示 -->
        <div v-if="timeError" class="error-message">
            <i class="pi pi-exclamation-triangle"></i>
            <span>{{ timeError }}</span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import Calendar from 'primevue/calendar';

const props = defineProps({
    // 当前选中值
    selectedValue: {
        type: [String, Object],
        default: ''
    }
});

// 发出的事件
const emit = defineEmits(['update:time-selection', 'validation-change', 'immediate-select']);

// 响应式状态
const selectedMonth = ref(null);
const timeError = ref('');
const selectedQuickOption = ref('');

// 快速时间选择选项
const quickTimeOptions = [
    {
        key: 'recent3Months',
        label: '近3个月'
    },
    {
        key: 'recentHalfYear',
        label: '近半年'
    },
    {
        key: 'lastWeek',
        label: '上周'
    },
    {
        key: 'thisWeek',
        label: '本周'
    }
];

// 计算属性
const isValid = computed(() => {
    // 检查是否有选择
    const hasSelection = selectedQuickOption.value || selectedMonth.value;

    // 检查选择的月份是否超过最大允许月份
    const isMonthValid = !selectedMonth.value || selectedMonth.value <= maxSelectableDate.value;

    return hasSelection && !timeError.value && isMonthValid;
});

/**
 * 计算可选择的最大月份
 * 根据当前日期决定限制：
 * - 当前日期在16号之前：最新只能选择上上个月
 * - 当前日期在16号之后：最新只能选择上个月
 */
const maxSelectableDate = computed(() => {
    const now = new Date();
    const currentDay = now.getDate();

    if (currentDay < 16) {
        // 16号之前，最新只能选择上上个月
        return new Date(now.getFullYear(), now.getMonth() - 2, 1);
    } else {
        // 16号之后，最新只能选择上个月
        return new Date(now.getFullYear(), now.getMonth() - 1, 1);
    }
});

/**
 * 验证时间选择
 */
const validateTimeSelection = () => {
    timeError.value = '';

    // 检查选择的月份是否超过最大允许月份
    if (selectedMonth.value && selectedMonth.value > maxSelectableDate.value) {
        const currentDay = new Date().getDate();
        const limitText = currentDay < 16 ? '上上个月' : '上个月';
        timeError.value = `选择的月份不能超过${limitText}`;
    }

    // 发出验证状态变化事件
    emit('validation-change', isValid.value);

    // 如果验证通过，发出时间选择更新事件
    if (isValid.value) {
        emitTimeSelection();
    }
};

/**
 * 选择快速时间
 * @param {Object} quickOption - 快速选择选项
 */
const selectQuickTime = quickOption => {
    selectedQuickOption.value = quickOption.key;
    selectedMonth.value = null; // 清除月份选择

    // 立即发出选择事件
    const timeSelection = {
        label: quickOption.label,
        value: quickOption.label,
        type: 'quick'
    };
    emit('update:time-selection', timeSelection);

    // 触发验证
    validateTimeSelection();

    // 发出立即选择事件
    emit('immediate-select', timeSelection);
};

/**
 * 处理月份选择
 */
const handleMonthSelect = () => {
    selectedQuickOption.value = ''; // 清除快速选择

    // 立即发出选择事件
    if (selectedMonth.value) {
        const monthLabel = formatMonthLabel(selectedMonth.value);
        const monthValue = `${selectedMonth.value.getFullYear()}-${String(
            selectedMonth.value.getMonth() + 1
        ).padStart(2, '0')}`;
        const timeSelection = {
            label: monthLabel,
            value: monthValue,
            type: 'custom'
        };
        emit('update:time-selection', timeSelection);

        // 触发验证
        validateTimeSelection();

        // 发出立即选择事件
        emit('immediate-select', timeSelection);
    }
};

/**
 * 格式化月份标签
 * @param {Date} date - 月份日期对象
 * @returns {string} 格式化后的标签
 */
const formatMonthLabel = date => {
    if (!date) {
        return '';
    }
    return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月`;
};

/**
 * 发出时间选择数据
 */
const emitTimeSelection = () => {
    let timeSelection = null;

    if (selectedQuickOption.value) {
        // 快速选择
        const quickOption = quickTimeOptions.find(
            option => option.key === selectedQuickOption.value
        );
        timeSelection = {
            label: quickOption.label,
            value: quickOption.label,
            type: 'quick'
        };
    } else if (selectedMonth.value) {
        // 自定义月份选择
        const monthLabel = formatMonthLabel(selectedMonth.value);
        const monthValue = `${selectedMonth.value.getFullYear()}-${String(
            selectedMonth.value.getMonth() + 1
        ).padStart(2, '0')}`;
        timeSelection = {
            label: monthLabel,
            value: monthValue,
            type: 'custom'
        };
    }

    if (timeSelection) {
        emit('update:time-selection', timeSelection);
    }
};

/**
 * 检查当前选择是否匹配某个快速选择选项
 */
const checkQuickOptionMatch = () => {
    if (typeof props.selectedValue === 'object' && props.selectedValue.value) {
        const quickOption = quickTimeOptions.find(
            option => option.label === props.selectedValue.value
        );
        if (quickOption) {
            selectedQuickOption.value = quickOption.key;
            return true;
        }
    } else if (typeof props.selectedValue === 'string') {
        const quickOption = quickTimeOptions.find(option => option.label === props.selectedValue);
        if (quickOption) {
            selectedQuickOption.value = quickOption.key;
            return true;
        }
    }
    return false;
};

/**
 * 初始化数据
 */
const initializeData = () => {
    console.log(props.selectedValue, 'props.selectedValue');

    // 重置状态
    selectedQuickOption.value = '';
    selectedMonth.value = null;

    // 如果有传入的选中值，优先使用
    if (props.selectedValue) {
        // 先检查是否是快速选择
        if (checkQuickOptionMatch()) {
            validateTimeSelection();
            return;
        }

        // 检查是否是自定义月份选择
        let monthValue = null;
        if (typeof props.selectedValue === 'object' && props.selectedValue.value) {
            monthValue = props.selectedValue.value;
        } else if (typeof props.selectedValue === 'string') {
            monthValue = props.selectedValue;
        }

        // 尝试解析月份值 (格式: YYYY-MM)
        if (monthValue && typeof monthValue === 'string' && monthValue.match(/^\d{4}-\d{2}$/)) {
            const [year, month] = monthValue.split('-');
            selectedMonth.value = new Date(parseInt(year), parseInt(month) - 1);
            validateTimeSelection();
            return;
        }
    }
};

// 组件挂载时初始化数据
onMounted(() => {
    initializeData();
});

// 监听选中值变化
watch(
    () => props.selectedValue,
    () => {
        initializeData();
    },
    { deep: true }
);
</script>

<style lang="scss" scoped>
.time-picker {
    .quick-select {
        padding: 16px;

        .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;

            .section-icon {
                font-size: 18px;
                color: $system-blue;
            }

            .section-title {
                font-size: 16px;
                font-weight: 600;
                color: $label-primary;
                margin: 0;
            }
        }

        .quick-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .quick-btn {
                padding: 10px 16px;
                border: 1px solid $separator-color-opaque;
                border-radius: 6px;
                background-color: $system-background-primary;
                color: $label-primary;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: $system-blue;
                }

                &--active {
                    border-color: $system-blue;
                    background-color: $system-blue;
                    color: white;
                }
            }
        }
    }

    .section-divider {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: $separator-color-opaque;
        }

        .divider-text {
            background-color: $system-background-primary;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
            color: $label-secondary;
            position: relative;
            z-index: 1;
        }
    }

    .custom-date-section {
        padding: 16px;
        margin-bottom: 16px;

        .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;

            .section-icon {
                font-size: 18px;
                color: $label-secondary;
            }

            .section-title {
                font-size: 16px;
                font-weight: 600;
                color: $label-primary;
                margin: 0;
            }
        }

        .month-input {
            :deep(.month-picker) {
                width: 100% !important;
            }

            // 全局修正 PrimeVue Calendar 弹出层 z-index
            :global(.p-datepicker) {
                z-index: 10000 !important;
            }
        }
    }

    .error-message {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background-color: rgba($system-red, 0.08);
        border: 1px solid rgba($system-red, 0.2);
        border-radius: 6px;
        color: $system-red;
        font-size: 14px;

        i {
            font-size: 16px;
        }
    }
}
</style>
