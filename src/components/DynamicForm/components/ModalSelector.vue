<template>
    <div
        class="modal-selector"
        :class="{ 'modal-selector--visible': visible }"
        @click="handleBackdropClick"
    >
        <div class="modal-content" @click.stop>
            <!-- 头部 -->
            <div class="modal-header">
                <h3 class="modal-title">{{ config.title || '请选择' }}</h3>
                <button class="close-btn" @click="handleClose">
                    <i class="pi pi-times"></i>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="modal-body">
                <!-- 时间选择器 -->
                <DateRangePicker
                    v-if="config.modal_type === 'date_range_picker'"
                    :selected-value="selectedValue"
                    @update:time-selection="handleTimeSelectionUpdate"
                    @validation-change="handleValidationChange"
                    @immediate-select="handleImmediateSelect"
                />

                <!-- 其他类型的选择器可以在这里扩展 -->
                <div v-else class="unsupported-type">
                    <i class="pi pi-exclamation-circle"></i>
                    <span>暂不支持的选择器类型: {{ config.modal_type }}</span>
                </div>
            </div>

            <!-- 底部操作按钮 -->
            <div class="modal-footer">
                <button class="modal-btn modal-btn--secondary" @click="handleClose">取消</button>
                <button
                    class="modal-btn modal-btn--primary"
                    :disabled="!canConfirm"
                    @click="handleConfirm"
                >
                    确定
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import DateRangePicker from './DateRangePicker.vue';

const props = defineProps({
    // 显示状态
    modelValue: {
        type: Boolean,
        default: false
    },
    // 配置信息
    config: {
        type: Object,
        default: () => ({})
    },
    // 当前选中值
    selectedValue: {
        type: [String, Object],
        default: ''
    }
});

// 响应式状态
const timeSelectionData = ref(null);
const isTimeSelectionValid = ref(false);

// 发出的事件
const emit = defineEmits(['update:modelValue', 'select', 'close']);

// 计算属性
const visible = computed(() => props.modelValue);

const canConfirm = computed(() => {
    if (props.config.modal_type === 'date_range_picker') {
        return isTimeSelectionValid.value && timeSelectionData.value;
    }
    return true;
});

/**
 * 处理时间选择更新
 */
const handleTimeSelectionUpdate = timeSelection => {
    timeSelectionData.value = timeSelection;
};

/**
 * 处理验证状态变化
 */
const handleValidationChange = isValid => {
    isTimeSelectionValid.value = isValid;
};

/**
 * 处理立即选择（快速选择或月份选择后直接关闭模态框）
 */
const handleImmediateSelect = timeSelection => {
    // 更新时间选择数据
    timeSelectionData.value = timeSelection;
    isTimeSelectionValid.value = true;

    // 立即发出选择事件并关闭模态框
    emit('select', timeSelection);
    handleClose();
};

/**
 * 处理背景点击关闭
 */
const handleBackdropClick = () => {
    handleClose();
};

/**
 * 处理关闭
 */
const handleClose = () => {
    emit('update:modelValue', false);
    emit('close');

    // 重置状态
    resetState();
};

/**
 * 处理确认
 */
const handleConfirm = () => {
    if (!canConfirm.value) {
        return;
    }

    let selectedValue = '';

    if (props.config.modal_type === 'date_range_picker') {
        selectedValue = timeSelectionData.value;
    }

    emit('select', selectedValue);
    handleClose();
};

/**
 * 重置状态
 */
const resetState = () => {
    timeSelectionData.value = null;
    isTimeSelectionValid.value = false;
};
</script>

<style lang="scss" scoped>
.modal-selector {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &--visible {
        opacity: 1;
        visibility: visible;

        .modal-content {
            transform: scale(1);
        }
    }

    .modal-content {
        background-color: $system-background-primary;
        border-radius: 12px;
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05);
        max-width: 420px;
        width: 100%;
        max-height: 75vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        transform: scale(0.95);
        transition: transform 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid $fill-color-primary;

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: $label-primary;
            margin: 0;
        }

        .close-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: $fill-color-primary;
            }

            i {
                font-size: 12px;
                color: $label-secondary;
            }
        }
    }

    .modal-body {
        flex: 1;
        overflow-y: auto;

        .unsupported-type {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px 20px;
            text-align: center;
            color: $label-secondary;

            i {
                font-size: 36px;
                margin-bottom: 12px;
                color: $label-tertiary;
            }

            span {
                font-size: 13px;
            }
        }
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 16px 20px;
        border-top: 1px solid $fill-color-primary;
        background-color: $system-background-secondary;

        .modal-btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid transparent;
            min-width: 60px;

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            &--secondary {
                background-color: $system-gray6;
                color: $label-primary;
                border: 1px solid $fill-color-tertiary;

                &:hover:not(:disabled) {
                    background-color: $system-gray5;
                    transform: translateY(-1px);
                }
            }

            &--primary {
                background: linear-gradient(135deg, $system-blue, $system-teal);
                color: white;
                box-shadow: 0 2px 8px rgba($system-blue, 0.2);

                &:hover:not(:disabled) {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba($system-blue, 0.25);
                }

                &:active:not(:disabled) {
                    transform: scale(0.98);
                }
            }
        }
    }
}
</style>
