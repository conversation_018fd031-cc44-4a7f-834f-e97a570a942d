<template>
    <span
        class="text-content"
        :class="{
            'text-content--highlighted': highlighted,
            'text-content--emphasis': emphasis
        }"
    >
        {{ content }}
    </span>
</template>

<script setup>
const props = defineProps({
    // 文本内容
    content: {
        type: String,
        default: ''
    },
    // 是否高亮显示
    highlighted: {
        type: Boolean,
        default: false
    },
    // 是否强调显示
    emphasis: {
        type: Boolean,
        default: false
    }
});
</script>

<style lang="scss" scoped>
.text-content {
    color: $label-primary;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;

    &--highlighted {
        background-color: rgba($system-yellow, 0.2);
        padding: 2px 4px;
        border-radius: 4px;
        color: $system-orange;
        font-weight: 500;
    }

    &--emphasis {
        font-weight: 600;
        color: $system-blue;
    }
}
</style>
