<template>
    <div
        class="dynamic-form"
        contenteditable="true"
        ref="formRef"
        @input="handleUserInput"
        @paste="handlePaste"
        @keydown="handleKeyDown"
    >
        <div class="form-content">
            <!-- 空状态提示 -->
            <div v-if="formConfig.length === 0" class="empty-state">
                <p>暂无表单配置</p>
            </div>

            <template v-for="(item, index) in formConfig" :key="`${item.type}-${index}`">
                <!-- 文本组件 -->
                <TextContent
                    v-if="item.type === 'text'"
                    v-bind="getComponentProps(item)"
                    class="form-item"
                />

                <!-- 内联选项组件 -->
                <InlineOption
                    v-else-if="item.type === 'inline_option'"
                    v-bind="getComponentProps(item)"
                    @update:value="handleValueUpdate"
                    @action="handleAction"
                    class="form-item"
                    contenteditable="false"
                />
            </template>
        </div>

        <!-- 使用 teleport 将侧边栏组件移到 body 下 -->
        <teleport to="body">
            <SidebarSelector
                v-model="showSidebar"
                :config="sidebarConfig"
                @select="handleSidebarSelect"
            >
                <!-- 根据 sidebar_type 渲染对应的业务组件 -->
                <CascadeSelector
                    v-if="sidebarConfig.sidebar_type === 'cascade_selector'"
                    :config="sidebarConfig"
                    @select="handleSidebarSelect"
                    @close="() => (showSidebar = false)"
                />

                <!-- 品牌选择器 -->
                <BrandSelector
                    v-else-if="sidebarConfig.sidebar_type === 'brand_selector'"
                    :config="sidebarConfig"
                    @select="handleSidebarSelect"
                    @close="() => (showSidebar = false)"
                />

                <!-- 默认提示，当没有匹配的组件类型时显示 -->
                <div v-else class="unsupported-selector">
                    <div class="error-message">
                        <i class="pi pi-exclamation-triangle"></i>
                        <span>不支持的选择器类型: {{ sidebarConfig.sidebar_type }}</span>
                    </div>
                </div>
            </SidebarSelector>
        </teleport>

        <!-- 使用 teleport 将模态框组件移到 body 下 -->
        <teleport to="body">
            <ModalSelector
                v-model="showModal"
                :config="modalConfig"
                :selected-value="modalSelectedValue"
                @select="handleModalSelect"
            />
        </teleport>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import TextContent from './components/TextContent.vue';
import InlineOption from './components/InlineOption.vue';
import SidebarSelector from './components/SidebarSelector.vue';
import ModalSelector from './components/ModalSelector.vue';
import CascadeSelector from './components/CascadeSelector.vue';
import BrandSelector from './components/BrandSelector.vue';

const props = defineProps({
    // 表单配置数据
    config: {
        type: Array,
        default: () => []
    }
});

// 响应式状态
const formRef = ref(null);
const formConfig = ref([]);
const formValues = ref({});
const showSidebar = ref(false);
const showModal = ref(false);
const sidebarConfig = ref({});
const modalConfig = ref({});
const modalSelectedValue = ref(''); // 存储模态框当前选中的值
const currentActionContext = ref(null);
const userInputContent = ref(''); // 存储用户输入的自定义内容

// 发出的事件
const emit = defineEmits(['valueChange', 'actionTriggered', 'contentChange', 'sendMessage']);

/**
 * 处理用户输入事件
 * @param {Event} event - 输入事件对象
 */
const handleUserInput = event => {
    // 延迟执行，确保 DOM 已更新
    setTimeout(() => {
        // 通知父组件内容已变化
        emit('contentChange', {
            content: generatePrompt(),
            rawContent: formRef.value.innerHTML
        });
    }, 0);
};

/**
 * 处理粘贴事件，确保粘贴的是纯文本
 * @param {Event} event - 粘贴事件对象
 */
const handlePaste = event => {
    // 阻止默认粘贴行为
    event.preventDefault();

    // 获取纯文本内容
    const text = event.clipboardData.getData('text/plain');

    // 使用现代API插入文本
    const selection = window.getSelection();
    if (selection.rangeCount) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        const textNode = document.createTextNode(text);
        range.insertNode(textNode);

        // 将光标移动到插入文本的末尾
        range.setStartAfter(textNode);
        range.setEndAfter(textNode);
        selection.removeAllRanges();
        selection.addRange(range);
    }
};

/**
 * 获取组件属性
 * @param {Object} item - 配置项
 * @returns {Object} 组件属性
 */
const getComponentProps = item => {
    const baseProps = { ...item };

    if (item.type === 'inline_option') {
        baseProps.value = formValues.value[item.id] || '';
        baseProps.modelValue = formValues.value[item.id] || '';
    }

    return baseProps;
};

/**
 * 处理值更新
 * @param {Object} payload - 更新的值信息
 */
const handleValueUpdate = payload => {
    const { id, value } = payload;
    formValues.value[id] = value;

    emit('valueChange', { id, value, allValues: formValues.value });
};

/**
 * 处理动作事件
 * @param {Object} payload - 动作信息
 */
const handleAction = payload => {
    const { action, context } = payload;

    currentActionContext.value = context;

    emit('actionTriggered', payload);

    switch (action.type) {
        case 'OPEN_SIDEBAR':
            sidebarConfig.value = action.sidebar_config;
            showSidebar.value = true;
            break;

        case 'OPEN_MODAL':
            modalConfig.value = action.modal_config;
            // 获取当前选中的值
            if (context && context.id) {
                modalSelectedValue.value = formValues.value[context.id] || '';
            } else {
                modalSelectedValue.value = '';
            }
            showModal.value = true;
            break;

        default:
            console.warn(`未知的动作类型: ${action.type}`);
    }
};

/**
 * 处理侧边栏选择
 * @param {Object} selectedValue - 选择的值
 */
const handleSidebarSelect = selectedValue => {
    if (currentActionContext.value) {
        handleValueUpdate({
            id: currentActionContext.value.id,
            value: selectedValue.lastLabel
        });
    }
    showSidebar.value = false;
    currentActionContext.value = null;
};

/**
 * 处理模态框选择
 * @param {Object} selectedValue - 选择的值
 */
const handleModalSelect = selectedValue => {
    if (currentActionContext.value) {
        handleValueUpdate({
            id: currentActionContext.value.id,
            value: selectedValue
        });
    }
    showModal.value = false;
    modalSelectedValue.value = '';
    currentActionContext.value = null;
};

/**
 * 初始化表单配置
 */
const initializeForm = () => {
    if (!props.config || !Array.isArray(props.config)) {
        formConfig.value = [];
        formValues.value = {};
        return;
    }

    formConfig.value = [...props.config];

    // 初始化表单值
    formValues.value = {};
    props.config.forEach(item => {
        if (item.type === 'inline_option') {
            formValues.value[item.id] = item.defaultValue || '';
        }
    });
};

/**
 * 生成提示词文本
 * @returns {string} 拼接后的提示词
 */
const generatePrompt = () => {
    // 如果 formRef 不存在，返回空字符串
    if (!formRef.value) {
        console.warn('生成提示词失败：formRef 不存在');
        return '';
    }

    try {
        // 从 DOM 中读取实际内容
        const formElement = formRef.value;
        let prompt = '';

        // 遍历所有子节点
        const processNode = node => {
            // 如果是文本节点，直接添加文本内容
            if (node.nodeType === Node.TEXT_NODE) {
                // 添加文本内容，但忽略纯空白节点
                const text = node.textContent.trim();
                if (text) {
                    prompt += text; // 添加空格以确保词语间有分隔
                }
                return;
            }

            // 如果是元素节点
            if (node.nodeType === Node.ELEMENT_NODE) {
                // 忽略空状态提示
                if (node.classList.contains('empty-state')) {
                    return;
                }

                // 检查是否是内联选项组件
                if (node.classList.contains('inline-option')) {
                    // 内联选项组件有一个 data-value 属性存储实际值
                    const selectedValue = node.getAttribute('data-value');
                    if (
                        selectedValue &&
                        selectedValue !== 'undefined' &&
                        selectedValue !== 'null'
                    ) {
                        try {
                            // 尝试解析 JSON 值
                            const parsedValue = JSON.parse(selectedValue);
                            prompt +=
                                (parsedValue.label ||
                                    parsedValue.text ||
                                    parsedValue.value ||
                                    String(parsedValue));
                        } catch (e) {
                            // 如果不是 JSON，直接使用字符串值
                            prompt += selectedValue;
                        }
                    } else {
                        // 如果没有值，使用显示的文本
                        const text = node.textContent.trim();
                        if (text && text !== '请选择') {
                            // 避免添加默认占位符
                            prompt += text;
                        }
                    }
                    return;
                }

                // 处理文本内容组件
                if (node.classList.contains('text-content')) {
                    const text = node.textContent.trim();
                    if (text) {
                        prompt += text;
                    }
                    return;
                }

                // 递归处理子节点
                for (const childNode of node.childNodes) {
                    processNode(childNode);
                }
            }
        };

        // 处理表单内容区域
        const formContent = formElement.querySelector('.form-content');
        if (formContent) {
            for (const childNode of formContent.childNodes) {
                processNode(childNode);
            }
        } else {
            // 如果找不到 .form-content，则处理整个表单
            for (const childNode of formElement.childNodes) {
                processNode(childNode);
            }
        }

        // 清理结果：删除多余空格，修复标点符号前的空格
        let cleanedPrompt = prompt
            .trim()
            .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
            .replace(/\s+([,.!?;:])/g, '$1') // 删除标点符号前的空格
            .replace(/\s+$/g, ''); // 删除末尾的空格

        // 当表单内容为空时，提供一个默认提示
        if (!cleanedPrompt) {
            // 检查是否有配置但没有选择值
            if (formConfig.value && formConfig.value.length > 0) {
                return '请在表单中选择或输入内容';
            }
            return '请添加表单配置或直接输入内容';
        }

        return cleanedPrompt;
    } catch (error) {
        console.error('生成提示词时发生错误:', error);

        // 发生错误时，尝试使用备用方法获取文本
        try {
            const plainText = formRef.value.textContent.trim().replace(/\s+/g, ' ');
            console.warn('使用备用方法获取提示词:', plainText);
            return plainText || '表单内容解析失败，请重试';
        } catch (backupError) {
            console.error('备用方法也失败:', backupError);
            return '表单内容解析失败，请重试';
        }
    }
};

/**
 * 处理键盘按下事件
 * @param {KeyboardEvent} event - 键盘事件对象
 */
const handleKeyDown = event => {
    // 处理回车键
    if (event.key === 'Enter') {
        // 用户正在使用输入法输入，不做任何处理
        if (event.isComposing) {
            return;
        }

        if (event.shiftKey) {
            // Shift + Enter: 插入换行，保持默认行为
            return;
        } else {
            // 单独的 Enter: 发送消息
            event.preventDefault();

            // 获取当前内容
            const currentContent = generatePrompt();

            // 如果有内容，则发送消息
            if (currentContent && currentContent.trim()) {
                emit('sendMessage', currentContent.trim());
            }
        }
    }
};

// 监听 props.config 的变化
watch(
    () => props.config,
    newConfig => {
        if (newConfig && Array.isArray(newConfig) && newConfig.length > 0) {
            nextTick(() => {
                initializeForm();
            });
        }
    },
    { immediate: true, deep: true }
);

// 生命周期钩子
onMounted(() => {
    initializeForm();
});

// 暴露给父组件的方法
defineExpose({
    getFormValues: () => formValues.value,
    generatePrompt,
    // 测试方法，用于验证 generatePrompt 在各种情况下是否正常工作
    testGeneratePrompt: () => {
        console.log('=== 测试 generatePrompt 方法 ===');

        // 测试情况1: 正常情况下的提示词
        console.log('1. 正常情况下的提示词:', generatePrompt());

        // 测试情况2: 模拟用户删除了部分内容
        if (formRef.value) {
            const formContent = formRef.value.querySelector('.form-content');
            if (formContent && formContent.children.length > 0) {
                // 临时保存第一个子元素
                const firstChild = formContent.children[0];
                // 移除第一个子元素
                formContent.removeChild(firstChild);
                console.log('2. 删除部分内容后的提示词:', generatePrompt());
                // 恢复第一个子元素
                formContent.insertBefore(firstChild, formContent.firstChild);
            }
        }

        // 测试情况3: 模拟用户添加了自定义内容
        if (formRef.value) {
            const formContent = formRef.value.querySelector('.form-content');
            if (formContent) {
                // 创建一个新的文本节点
                const customText = document.createTextNode('用户自定义内容');
                // 添加到表单内容中
                formContent.appendChild(customText);
                console.log('3. 添加自定义内容后的提示词:', generatePrompt());
                // 移除添加的文本节点
                formContent.removeChild(customText);
            }
        }

        // 测试情况4: 模拟用户删除了所有内容
        if (formRef.value) {
            const formContent = formRef.value.querySelector('.form-content');
            if (formContent) {
                // 保存原始内容
                const originalContent = formContent.innerHTML;
                // 清空内容
                formContent.innerHTML = '';
                console.log('4. 删除所有内容后的提示词:', generatePrompt());
                // 恢复原始内容
                formContent.innerHTML = originalContent;
            }
        }

        // 测试情况5: 模拟用户修改了内联选项的值
        if (formRef.value) {
            const inlineOption = formRef.value.querySelector('.inline-option');
            if (inlineOption) {
                // 保存原始值
                const originalValue = inlineOption.getAttribute('data-value');
                // 修改值
                inlineOption.setAttribute(
                    'data-value',
                    JSON.stringify({ label: '测试修改的值', value: 'test' })
                );
                console.log('5. 修改内联选项值后的提示词:', generatePrompt());
                // 恢复原始值
                if (originalValue) {
                    inlineOption.setAttribute('data-value', originalValue);
                } else {
                    inlineOption.removeAttribute('data-value');
                }
            }
        }

        // 测试情况6: 模拟错误情况
        if (formRef.value) {
            const formContent = formRef.value.querySelector('.form-content');
            if (formContent) {
                // 保存原始内容
                const originalContent = formContent.innerHTML;
                // 插入错误的HTML结构
                formContent.innerHTML =
                    '<div class="invalid-element" data-broken>错误的HTML结构</div>';
                console.log('6. 错误HTML结构下的提示词:', generatePrompt());
                // 恢复原始内容
                formContent.innerHTML = originalContent;
            }
        }

        console.log('=== 测试结束 ===');
        return '测试完成，请查看控制台输出';
    }
});
</script>

<style lang="scss" scoped>
.dynamic-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    background-color: $system-background-primary;
    border-radius: 12px;
    border: 1px solid $fill-color-quaternary;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    caret-color: $system-blue;

    // 增强聚焦效果
    &:focus-within {
        border-color: $system-blue;
    }

    .form-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 4px;
        line-height: 1.5;
        font-size: 14px;
    }

    .form-item {
        display: inline-flex;
        align-items: center;
    }

    .empty-state {
        text-align: center;
        padding: 20px;
        color: $label-secondary;
        font-size: 14px;
        background-color: $system-background-secondary;
        border-radius: 8px;
        border: 1px dashed $fill-color-tertiary;

        p {
            margin: 0;
        }
    }
}

// 不支持的选择器样式
.unsupported-selector {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $system-background-primary;

    .error-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 40px 20px;
        text-align: center;

        i {
            font-size: 32px;
            color: $system-orange;
        }

        span {
            font-size: 14px;
            color: $label-secondary;
            line-height: 1.4;
        }
    }
}
</style>
