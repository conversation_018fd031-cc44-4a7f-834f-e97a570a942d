<template>
    <div class="markdown-editor">
        <div class="markdown-editor__edit">
            <textarea
                class="markdown-editor__textarea"
                :value="modelValue"
                @input="updateContent"
                placeholder="请输入Markdown内容..."
            ></textarea>
        </div>
        <div class="markdown-editor__preview">
            <MarkdownViewer :content="modelValue" />
        </div>
    </div>
</template>

<script setup>
import MarkdownViewer from '../common/MarkdownViewer.vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: '请输入Markdown内容...'
    }
});

const emit = defineEmits(['update:modelValue']);

const updateContent = (event) => {
    emit('update:modelValue', event.target.value);
};
</script>

<style lang="scss" scoped>
.markdown-editor {
    display: flex;
    gap: 20px;
    height: 100%;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Open Sans', 'Helvetica Neue', sans-serif;

    &__edit,
    &__preview {
        flex: 1;
        height: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.16);
        }
    }

    &__edit {
        position: relative;
        display: flex;
        flex-direction: column;
    }

    &__textarea {
        flex: 1;
        width: 100%;
        height: 100%;
        padding: 20px;
        border: none;
        resize: none;
        font-family:
            SFMono-Regular,
            Consolas,
            Liberation Mono,
            Menlo,
            monospace;
        font-size: 14px;
        line-height: 1.6;
        color: #24292e;
        background-color: #fff;
        border-radius: 8px;
        box-sizing: border-box;
        outline: none;
        transition: all 0.3s ease;

        &:focus {
            box-shadow: inset 0 0 0 2px rgba(3, 102, 214, 0.2);
        }

        &::placeholder {
            color: #a0aec0;
        }
    }

    &__preview {
        overflow: auto;

        :deep(.markdown-viewer) {
            height: 100%;
            padding: 0;
            margin: 0;
            box-shadow: none;
        }

        :deep(.markdown-viewer__content) {
            height: 100%;
            box-shadow: none;
            border-radius: 0;
            padding: 20px;
            margin: 0;
            overflow: auto;
        }

        :deep(.markdown-viewer__toc) {
            display: none; // 隐藏目录，节省空间
        }
    }
}

// 移动端适配
@media (max-width: 768px) {
    .markdown-editor {
        flex-direction: column;
        gap: 16px;
        padding: 12px;

        &__edit,
        &__preview {
            width: 100%;
            height: auto;
            min-height: 300px;
        }

        &__textarea {
            padding: 16px;
            min-height: 300px;
        }

        &__preview {
            :deep(.markdown-viewer__content) {
                padding: 16px;
                min-height: 300px;
            }
        }
    }
}
</style>
