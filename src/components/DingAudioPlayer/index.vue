<template>
    <div class="audio-wrapper">
        <div
            class="audio-bubble"
            :data-theme="theme"
            @click="handlePlay"
            :class="{ 'is-playing': isPlaying }"
        >
            <div class="audio-controls">
                <div class="speaker-icon" :class="{ 'is-playing': isPlaying }">
                    <img v-if="!isPlaying" :src="playButtonSrc" alt="播放" class="control-icon" />
                    <img v-else :src="pauseButtonSrc" alt="暂停" class="control-icon" />
                </div>
                <div v-if="audioDuration" class="duration">
                    {{ formatTime(audioDuration) }}
                </div>
            </div>
            <div v-if="audioText" class="audio-text">{{ audioText }}</div>
        </div>

        <!-- PC端提示弹窗 -->
        <Loading :config="pcTipConfig" />
    </div>
</template>

<script>
import * as dd from 'dingtalk-jsapi'; // 直接导入
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import audioManager from '@/utils/audioManager';
import Loading from '@/components/Loading.vue';
import { isDingTalkPC } from '@/utils/index.js';

dayjs.extend(duration);

export default {
    name: 'DingAudioPlayer',
    components: {
        Loading
    },
    props: {
        audioId: {
            type: String,
            required: true
        },
        audioText: {
            type: String,
            default: ''
        },
        audioDuration: {
            type: Number,
            default: 0
        },
        theme: {
            type: String,
            default: 'default',
            validator: value => ['default', 'green'].includes(value)
        },
        playButtonSrc: {
            type: String,
            default: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/play3.png'
        },
        pauseButtonSrc: {
            type: String,
            default: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/pause3.png'
        }
    },
    data() {
        return {
            isPlaying: false,
            localAudioId: null,
            unsubscribe: null,
            uniqueAudioId: null,
            // PC端提示配置
            pcTipConfig: {
                visible: false,
                status: 'failed',
                message: '当前仅支持手机播放～'
            }
        };
    },
    created() {
        // 为每个组件实例创建唯一ID
        this.uniqueAudioId = `ding-audio-${this.audioId}`;

        // 订阅停止事件
        this.unsubscribe = audioManager.onStopRequest(this.uniqueAudioId, () => {
            if (this.isPlaying && this.localAudioId) {
                this.stopAudio();
            }
        });
    },
    mounted() {
        // 组件挂载时的初始化逻辑
    },
    methods: {
        /**
         * 下载音频文件
         * @returns {Promise<string>} 返回本地音频ID
         */
        downloadAudio() {
            return new Promise((resolve, reject) => {
                dd.device.audio.download({
                    mediaId: this.audioId,
                    onSuccess: res => {
                        resolve(res.localAudioId);
                    },
                    onFail: err => {
                        reject(new Error(JSON.stringify(err)));
                    }
                });
            });
        },

        /**
         * 设置音频播放结束的监听器
         * @emits play-end 播放结束时触发
         */
        setupPlayEndListener() {
            dd.device.audio.onPlayEnd({
                onSuccess: () => {
                    this.isPlaying = false;
                    audioManager.registerStop(this.uniqueAudioId);
                    this.$emit('play-end');
                },
                onFail: err => {
                    console.error('监听播放结束失败:', err);
                    this.isPlaying = false;
                }
            });
        },

        /**
         * 停止音频播放
         */
        async stopAudio() {
            try {
                await dd.device.audio.stop({
                    localAudioId: this.localAudioId,
                    onSuccess: () => {
                        this.isPlaying = false;
                        audioManager.registerStop(this.uniqueAudioId);
                    },
                    onFail: err => {
                        this.isPlaying = false;
                        throw new Error(JSON.stringify(err));
                    }
                });
            } catch (error) {
                console.error('停止音频失败:', error);
                this.isPlaying = false;
            }
        },

        /**
         * 显示PC端提示弹窗
         */
        showPCTip() {
            this.pcTipConfig.visible = true;
            // 3秒后自动关闭
            setTimeout(() => {
                this.pcTipConfig.visible = false;
            }, 2000);
        },

        async handlePlay() {
            try {
                // 检测是否为PC端，如果是则显示提示
                if (isDingTalkPC()) {
                    this.showPCTip();
                    return;
                }

                if (!this.isPlaying) {
                    // 注册播放
                    audioManager.registerPlay(this.uniqueAudioId);

                    // 如果没有本地音频ID，先下载
                    if (!this.localAudioId) {
                        this.localAudioId = await this.downloadAudio();
                    }

                    await dd.device.audio.play({
                        localAudioId: this.localAudioId,
                        onSuccess: () => {
                            this.isPlaying = true;
                            this.$emit('play-start');
                            // 调用新的监听器设置方法
                            this.setupPlayEndListener();
                        },
                        onFail: err => {
                            this.isPlaying = false;
                            throw new Error(JSON.stringify(err));
                        }
                    });
                } else {
                    await this.stopAudio();
                }
            } catch (error) {
                console.error('音频操作失败:', error);
                this.$toast?.add({
                    severity: 'error',
                    summary: '操作失败',
                    detail: error.message || '音频操作出现错误',
                    life: 3000
                });
                this.isPlaying = false;
            }
        },

        /**
         * 格式化音频时长
         * @param {number} seconds - 音频时长(秒)
         * @returns {string} 格式化后的时间字符串 (mm:ss)
         */
        formatTime(seconds) {
            if (!seconds) {
                return '0"';
            }
            return Math.round(seconds) + '"';
        }
    },
    beforeUnmount() {
        // 取消订阅停止事件
        if (this.unsubscribe) {
            this.unsubscribe();
        }

        // 如果组件销毁时音频正在播放，则停止
        if (this.isPlaying && this.localAudioId) {
            dd.device.audio.offPlayEnd(); // 移除播放结束监听
            dd.device.audio.stop({ localAudioId: this.localAudioId });
            audioManager.registerStop(this.uniqueAudioId);
        }
    }
};
</script>

<style lang="scss" scoped>
.audio-wrapper {
    width: 80%;

    .audio-bubble {
        position: relative;
        background-color: #fff;
        border-radius: 6px;
        padding: 8px 12px;
        margin-left: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        max-width: 100%;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
            opacity: 0.9;
        }

        &::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 16px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-right: 6px solid #fff;
            transition: all 0.2s ease;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 8px;

            .speaker-icon {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                margin-left: 2px;
                position: relative;
                transition: all 0.3s ease;

                .control-icon {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }

            .duration {
                font-size: 14px;
                color: #333;
                font-weight: 600;
                transition: color 0.2s ease;
            }
        }

        .audio-text {
            padding-top: 6px;
            border-top: 1px solid #f5f5f5;
            margin-top: 6px;
            font-size: 14px;
            color: #333;
            font-weight: 600;
            line-height: 1.4;
            word-break: break-all;
            white-space: pre-wrap;
            transition: color 0.2s ease, border-color 0.2s ease;
        }

        &[data-theme='green'] {
            background-color: #91ed61;
            margin-left: auto;

            &::before {
                left: auto;
                right: -6px;
                border-right: none;
                border-left: 6px solid #91ed61;
            }

            .duration {
                color: #333;
            }

            .audio-text {
                color: #333;
                border-top-color: rgba(255, 255, 255, 0.2);
            }
        }
    }
}
</style>
