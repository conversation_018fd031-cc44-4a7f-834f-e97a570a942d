<template>
    <div
        class="recording-popup"
        :class="{ 'is-active': isActive }"
        @touchmove="handleTouchMove"
        @touchstart="handleTouchStart"
    >
        <div class="popup-content">
            <!-- 波浪动画和时间显示 -->
            <div class="wave-container">
                <div class="wave-animation"></div>
                <div class="time-display">
                    <span v-if="!isHoveringLock">{{
                        formatTime(recordDuration)
                    }}</span>
                    <span v-else class="lock-tip">滑动到此处锁定录音</span>
                </div>
            </div>

            <!-- 锁定按钮 -->
            <div
                class="lock-button"
                :class="{ 'is-hover': isHoveringLock }"
                ref="lockButton"
            >
                <i class="pi pi-lock"></i>
            </div>

            <!-- 关闭按钮 -->
            <div
                class="close-button"
                :class="{ 'is-hover': isHoveringClose }"
                @click="handleCloseClick"
            >
                <i class="pi pi-times"></i>
            </div>

            <!-- 录音按钮（展示用） -->
            <div class="record-button">
                <i class="pi pi-microphone"></i>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';

const props = defineProps({
    isActive: {
        type: Boolean,
        default: false
    },
    recordDuration: {
        type: Number,
        default: 0
    }
});

const emit = defineEmits(['update:isActive', 'close', 'lock']);

const isHoveringLock = ref(false);
const isHoveringClose = ref(false);
const lockButton = ref(null);
let isLocked = false;

// 触摸事件处理
function handleLockClick(event) {
    console.log('Lock clicked');
    event.stopPropagation();
    event.preventDefault();
    emit('lock');
}

function handleCloseClick(event) {
    console.log('Close clicked');
    event.stopPropagation();
    event.preventDefault();
    emit('close');
}

// 处理触摸移动
function handleTouchMove(event) {
    console.log('Touch move detected');

    if (isLocked) {return;}

    const touch = event.touches[0];
    const lockButtonEl = lockButton.value;

    if (lockButtonEl) {
        const rect = lockButtonEl.getBoundingClientRect();
        const isInLockArea =
            touch.clientX >= rect.left &&
            touch.clientX <= rect.right &&
            touch.clientY >= rect.top &&
            touch.clientY <= rect.bottom;

        console.log('Touch move detected, in lock area:', isInLockArea);
        isHoveringLock.value = isInLockArea;

        if (isInLockArea) {
            isLocked = true;
            console.log('Lock triggered');
            emit('lock');
        }
    }
}

// 处理触摸开始
function handleTouchStart(event) {
    isLocked = false;
    isHoveringLock.value = false;
}

// 监听组件的可见性变化
watch(
    () => props.isActive,
    (newVal) => {
        console.log('Popup visibility changed:', newVal);
        if (newVal) {
            nextTick(() => {
                console.log('Popup is now visible and ready for events');
                isLocked = false;
                isHoveringLock.value = false;
            });
        }
    }
);

function formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    return `${seconds}秒`;
}
</script>

<style lang="scss" scoped>
.recording-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 200px;
    background: rgba(255, 255, 255, 0.98);
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    z-index: 1000;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
    border-radius: 16px 16px 0 0;
    touch-action: none; // 防止页面滚动

    &.is-active {
        transform: translateY(0);
    }

    .popup-content {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 20px;
    }

    .wave-container {
        position: relative;
        height: 60px;
        overflow: hidden;
        border-radius: 30px;
        background: #f0f7ff;
        margin-bottom: 20px;

        .wave-animation {
            position: absolute;
            width: 200%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(70, 128, 255, 0.2),
                transparent
            );
            animation: waveMove 2s linear infinite;
        }

        .time-display {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #3670f7;
            font-weight: 500;

            .lock-tip {
                color: #ff9f43;
                font-size: 16px;
            }
        }
    }

    .lock-button,
    .close-button {
        position: absolute;
        width: 60px; // 增加点击区域
        height: 60px; // 增加点击区域
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
        user-select: none;

        &::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
        }

        i {
            position: relative;
            z-index: 1;
        }
    }

    .lock-button {
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: #f0f7ff;

        i {
            font-size: 24px;
            color: #3670f7;
        }

        &.is-hover,
        &:active {
            background: #3670f7;
            i {
                color: #fff;
            }
        }
    }

    .close-button {
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        background: #fff0f0;

        i {
            font-size: 24px;
            color: #ff4d4f;
        }

        &.is-hover,
        &:active {
            background: #ff4d4f;
            i {
                color: #fff;
            }
        }
    }

    .record-button {
        position: absolute;
        left: 50%;
        bottom: 20px;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4680ff 0%, #366ae6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(54, 112, 247, 0.2);

        i {
            font-size: 24px;
            color: #fff;
        }
    }
}

@keyframes waveMove {
    0% {
        transform: translateX(-50%);
    }
    100% {
        transform: translateX(0);
    }
}
</style>
