<template>
    <div class="ai-background">
        <div class="particles-container">
            <div
                v-for="n in particleCount"
                :key="`particle-${n}`"
                class="particle"
                :class="`particle-${n}`"
                :style="`--delay: ${Math.random() * 5}s; --size: ${Math.random() * 6 + 2}px`"
            ></div>
        </div>
        <div class="neural-network">
            <div
                v-for="n in networkLineCount"
                :key="`network-${n}`"
                class="network-line"
                :style="`--angle: ${n * (360 / networkLineCount)}deg; --delay: ${n * 0.2}s`"
            ></div>
        </div>
    </div>
</template>

<script setup>
defineProps({
    // 粒子数量
    particleCount: {
        type: Number,
        default: 20
    },
    // 神经网络线条数量
    networkLineCount: {
        type: Number,
        default: 6
    }
});
</script>

<style lang="scss" scoped>
@use 'sass:math';

.ai-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
    opacity: 0.4;
    pointer-events: none;

    // 粒子效果
    .particles-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .particle {
            position: absolute;
            border-radius: 50%;
            background: $system-blue;
            opacity: 0.4;
            filter: blur(1px);
            width: var(--size, 3px);
            height: var(--size, 3px);
            animation: floatParticle 15s infinite linear;
            animation-delay: var(--delay, 0s);

            @for $i from 1 through 20 {
                &.particle-#{$i} {
                    top: math.random(100) * 1%;
                    left: math.random(100) * 1%;
                    animation-duration: #{10 + math.random(10)}s;
                }
            }
        }
    }

    // 神经网络效果
    .neural-network {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .network-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 150%;
            height: 1px;
            background: linear-gradient(
                90deg,
                transparent,
                rgba($system-blue, 0.2),
                rgba($system-indigo, 0.3),
                rgba($system-blue, 0.2),
                transparent
            );
            transform-origin: 0 0;
            transform: rotate(var(--angle, 0deg));
            animation: pulseLine 4s infinite ease-in-out;
            animation-delay: var(--delay, 0s);
        }
    }
}

@keyframes floatParticle {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(10px, 10px);
    }
    50% {
        transform: translate(5px, 20px);
    }
    75% {
        transform: translate(-5px, 10px);
    }
    100% {
        transform: translate(0, 0);
    }
}

@keyframes pulseLine {
    0%,
    100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.8;
    }
}
</style>
