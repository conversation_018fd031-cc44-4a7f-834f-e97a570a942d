<template>
    <div
        class="fade-in-section"
        :class="{
            'animate-in': isVisible,
            'from-bottom': direction === 'bottom',
            'from-left': direction === 'left',
            'from-right': direction === 'right',
        }"
    >
        <slot></slot>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
    // 动画延迟时间(ms)
    delay: {
        type: Number,
        default: 0
    },
    // 动画方向：bottom, left, right
    direction: {
        type: String,
        default: 'bottom',
        validator: (value) => ['bottom', 'left', 'right'].includes(value)
    },
    // 是否自动开始动画
    autoStart: {
        type: Boolean,
        default: true
    }
});

const isVisible = ref(false);

/**
 * 开始动画
 */
const startAnimation = () => {
    setTimeout(() => {
        isVisible.value = true;
    }, props.delay);
};

/**
 * 重置动画
 */
const resetAnimation = () => {
    isVisible.value = false;
};

// 暴露方法供父组件调用
defineExpose({
    startAnimation,
    resetAnimation
});

onMounted(() => {
    if (props.autoStart) {
        startAnimation();
    }
});
</script>

<style lang="scss" scoped>
.fade-in-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

    &.from-left {
        transform: translateX(-20px);
    }

    &.from-right {
        transform: translateX(20px);
    }

    &.animate-in {
        opacity: 1;
        transform: translate(0);
    }
}
</style>
