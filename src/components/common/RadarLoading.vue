<template>
    <div class="loading-container">
        <div class="radar-base">
            <div class="radar-screen"></div>
            <div class="radar-grid"></div>
            <div class="radar-sweep"></div>
            <div class="radar-center"></div>
            <div class="radar-data-circles">
                <div class="data-circle circle-1"></div>
                <div class="data-circle circle-2"></div>
            </div>
            <div class="radar-blips">
                <div class="blip blip-1"></div>
                <div class="blip blip-2"></div>
                <div class="blip blip-3"></div>
                <div class="blip blip-4"></div>
                <div class="blip blip-5"></div>
            </div>
            <div class="radar-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
            </div>
            <div class="radar-scan-lines"></div>
            <div class="radar-glitch"></div>
        </div>
        <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
</template>

<script setup>
/**
 * 雷达扫描加载组件
 * @param {string} text - 加载提示文本，可选
 */
defineProps({
    text: {
        type: String,
        default: ''
    }
});
</script>

<style lang="scss" scoped>
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px 20px;
    animation: fadeIn 0.6s ease;
    background: linear-gradient(180deg, #f3f6ff 0%, #f9fbff 100%);
}

.radar-base {
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(
        135deg,
        rgba(225, 235, 255, 0.9) 0%,
        rgba(245, 248, 255, 0.85) 50%,
        rgba(235, 241, 255, 0.9) 100%
    );
    box-shadow:
        0 0 30px rgba(30, 80, 200, 0.1),
        inset 0 0 30px rgba(255, 255, 255, 0.6);
    overflow: hidden;
    transform: perspective(600px) rotateX(10deg);
}

.radar-screen {
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.92);
    box-shadow:
        inset 0 0 20px rgba(30, 80, 200, 0.08),
        0 0 20px rgba(30, 80, 200, 0.08);
}

.radar-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(90deg, rgba(45, 110, 230, 0.06) 1px, transparent 1px),
        linear-gradient(0deg, rgba(45, 110, 230, 0.06) 1px, transparent 1px);
    border-radius: 50%;
    opacity: 0.6;
}

.radar-sweep {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(
        rgba(45, 110, 230, 0) 0deg,
        rgba(45, 110, 230, 0.12) 30deg,
        rgba(45, 110, 230, 0.2) 60deg,
        rgba(45, 110, 230, 0.12) 90deg,
        transparent 120deg
    );
    animation: sweepRotate 3s linear infinite;
    mix-blend-mode: multiply;
}

.radar-center {
    position: absolute;
    width: 16px;
    height: 16px;
    background: radial-gradient(
        circle,
        #2563eb 0%,
        rgba(37, 99, 235, 0.5) 70%,
        transparent 100%
    );
    box-shadow: 0 0 12px rgba(37, 99, 235, 0.3);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: centerPulse 2s ease-in-out infinite;
}

.radar-data-circles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.data-circle {
    position: absolute;
    border-radius: 50%;
    border: 1px dashed rgba(120, 190, 230, 0.4);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: dataCircleExpand 4s ease-out infinite;
}

.circle-1 {
    animation-delay: 0s;
}

.circle-2 {
    animation-delay: 2s;
}

.radar-blips {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.blip {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #78bee6;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow:
        0 0 10px rgba(120, 190, 230, 0.8),
        0 0 20px rgba(120, 190, 230, 0.4);
    animation: blipPulse 3s ease-in-out infinite;
}

.blip-1 {
    top: 30%;
    left: 70%;
    animation-delay: 0.5s;
}

.blip-2 {
    top: 65%;
    left: 35%;
    animation-delay: 1.2s;
}

.blip-3 {
    top: 40%;
    left: 25%;
    animation-delay: 2s;
}

.blip-4 {
    top: 20%;
    left: 45%;
    animation-delay: 0.8s;
}

.blip-5 {
    top: 75%;
    left: 65%;
    animation-delay: 1.5s;
    background: #d8b478;
    box-shadow:
        0 0 10px rgba(216, 180, 120, 0.8),
        0 0 20px rgba(216, 180, 120, 0.4);
}

.radar-rings {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.ring {
    position: absolute;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ringPulse 4s ease-in-out infinite;
}

.ring-1 {
    width: 40%;
    height: 40%;
    border: 2px solid rgba(120, 190, 230, 0.4);
    animation-delay: 0s;
}

.ring-2 {
    width: 70%;
    height: 70%;
    border: 1.5px solid rgba(120, 190, 230, 0.3);
    animation-delay: 1s;
}

.ring-3 {
    width: 90%;
    height: 90%;
    border: 1px solid rgba(120, 190, 230, 0.2);
    animation-delay: 2s;
}

.radar-scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        rgba(120, 190, 230, 0.05) 50%,
        transparent 100%
    );
    background-size: 100% 4px;
    border-radius: 50%;
    opacity: 0.5;
    animation: scanLines 10s linear infinite;
    pointer-events: none;
}

.radar-glitch {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(120, 190, 230, 0.05);
    border-radius: 50%;
    opacity: 0;
    animation: glitchEffect 10s linear infinite;
    pointer-events: none;
}

.loading-text {
    margin-top: 24px;
    font-size: 15px;
    font-weight: 500;
    color: #2d3748;
    animation: textPulse 2s ease-in-out infinite;
}

@keyframes sweepRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes blipPulse {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 1;
    }
    75% {
        opacity: 0.8;
    }
}

@keyframes centerPulse {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes ringPulse {
    0%,
    100% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
    }
}

@keyframes dataCircleExpand {
    0% {
        width: 0%;
        height: 0%;
        opacity: 1;
    }
    70% {
        opacity: 0.5;
    }
    100% {
        width: 100%;
        height: 100%;
        opacity: 0;
    }
}

@keyframes scanLines {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 0 100%;
    }
}

@keyframes glitchEffect {
    0%,
    90%,
    100% {
        opacity: 0;
    }
    90.5%,
    91.5%,
    92.5% {
        opacity: 0.3;
    }
    91%,
    92%,
    93% {
        opacity: 0;
    }
}

@keyframes textPulse {
    0%,
    100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>
