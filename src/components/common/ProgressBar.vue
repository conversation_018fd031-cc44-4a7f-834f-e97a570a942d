<template>
    <div class="progress-bar" :style="{ width: `${progress}%` }"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
    // 是否自动开始动画
    autoStart: {
        type: Boolean,
        default: true
    },
    // 动画持续时间(ms)
    duration: {
        type: Number,
        default: 2000
    },
    // 进度条颜色，支持渐变色或单色
    color: {
        type: String,
        default: 'linear-gradient(90deg, #4318ff, #00b5d8)'
    }
});

const progress = ref(0);
let progressInterval = null;

/**
 * 开始进度条动画
 */
const startProgress = () => {
    progress.value = 0;
    progressInterval = setInterval(() => {
        if (progress.value < 100) {
            progress.value += 2;
        } else {
            clearInterval(progressInterval);
        }
    }, props.duration / 100);
};

/**
 * 重置进度条
 */
const resetProgress = () => {
    clearInterval(progressInterval);
    progress.value = 0;
};

// 暴露方法供父组件调用
defineExpose({
    startProgress,
    resetProgress
});

onMounted(() => {
    if (props.autoStart) {
        startProgress();
    }
});
</script>

<style lang="scss" scoped>
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 2px;
    background: v-bind('color');
    transition: width 0.2s ease;
    z-index: 100;
}
</style>
