<template>
    <div class="wave-container" :class="direction">
        <div
            v-for="i in waveCount"
            :key="i"
            class="wave"
            :style="{
                animationDelay: `${(i - 1) * 0.1}s`,
                height: `${baseHeight + Math.random() * 4}px`,
            }"
        ></div>
    </div>
</template>

<script setup>
const props = defineProps({
    direction: {
        type: String,
        default: 'left',
        validator: (value) => ['left', 'right'].includes(value)
    },
    waveCount: {
        type: Number,
        default: 5
    },
    baseHeight: {
        type: Number,
        default: 8
    }
});
</script>

<style lang="scss" scoped>
.wave-container {
    position: relative;
    width: 80px;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 5px;

    &.left {
        justify-content: flex-end;
    }

    &.right {
        justify-content: flex-start;
    }

    .wave {
        width: 4px;
        border-radius: 2px;
        background: linear-gradient(180deg, #8b75ff 0%, #6b4bff 100%);
        animation: waveAnimation 1.2s ease-in-out infinite;
        opacity: 0.7;

        &:nth-child(odd) {
            animation-duration: 1s;
        }

        &:nth-child(even) {
            animation-duration: 1.4s;
        }
    }
}

@keyframes waveAnimation {
    0%,
    100% {
        transform: scaleY(0.5);
        opacity: 0.3;
    }
    50% {
        transform: scaleY(1);
        opacity: 1;
    }
}
</style>
