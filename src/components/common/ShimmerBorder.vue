<template>
    <div class="shimmer-border" :class="classes" :style="borderStyle">
        <slot></slot>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    /**
     * 是否展示加载动画
     */
    loading: {
        type: Boolean,
        default: true
    },
    /**
     * 自定义样式
     */
    customStyle: {
        type: Object,
        default: () => ({})
    },
    /**
     * 边框圆角
     */
    borderRadius: {
        type: String,
        default: '12px'
    },
    /**
     * 主要颜色
     */
    primaryColor: {
        type: String,
        default: 'rgba(10, 122, 255, 0.6)' // 使用系统蓝色（$system-blue）作为基础
    },
    /**
     * 次要颜色
     */
    secondaryColor: {
        type: String,
        default: 'rgba(88, 86, 214, 0.6)' // 使用系统靛蓝色（$system-indigo）作为基础
    },
    /**
     * 光效位置类型: 'circular' | 'linear'
     */
    effectType: {
        type: String,
        default: 'circular',
        validator: (value) => ['circular', 'linear'].includes(value)
    }
});

const classes = computed(() => ({
    'shimmer-border--loading': props.loading,
    'shimmer-border--circular': props.effectType === 'circular',
    'shimmer-border--linear': props.effectType === 'linear'
}));

const borderStyle = computed(() => ({
    borderRadius: props.borderRadius,
    ...props.customStyle,
    '--primary-color': props.primaryColor,
    '--secondary-color': props.secondaryColor
}));
</script>

<style lang="scss" scoped>
.shimmer-border {
    position: relative;
    width: 100%;
    height: 100%;
    pointer-events: none;
    box-sizing: border-box;
    overflow: hidden;

    &--loading {
        &.shimmer-border--circular {
            background: radial-gradient(
                circle at center,
                var(--primary-color, rgba($system-blue, 0.15)) 0%,
                transparent 70%
            );
            animation: pulse-glow 2s infinite ease-in-out;

            &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                border: 2px solid transparent;
                border-top-color: var(--primary-color, rgba($system-blue, 0.6));
                border-bottom-color: var(--secondary-color, rgba($system-indigo, 0.6));
                transform: translate(-50%, -50%);
                animation: circle-rotate 3s infinite linear;
            }
        }

        &.shimmer-border--linear {
            &::before {
                content: '';
                position: absolute;
                width: 150%;
                height: 50px;
                background: linear-gradient(
                    90deg,
                    transparent,
                    var(--primary-color, rgba($system-blue, 0.2)),
                    var(--secondary-color, rgba($system-indigo, 0.3)),
                    var(--primary-color, rgba($system-blue, 0.2)),
                    transparent
                );
                transform: rotate(-25deg) translateY(-50%);
                animation: light-sweep 3s infinite ease-in-out;
                filter: blur(10px);
            }
        }
    }
}

@keyframes pulse-glow {
    0% {
        opacity: 0.2;
        box-shadow:
            inset 0 0 20px var(--primary-color, rgba($system-blue, 0.2)),
            0 0 0 0 var(--primary-color, rgba($system-blue, 0.2));
    }
    50% {
        opacity: 0.6;
        box-shadow:
            inset 0 0 60px var(--primary-color, rgba($system-blue, 0.4)),
            0 0 30px 10px var(--primary-color, rgba($system-blue, 0.3));
    }
    100% {
        opacity: 0.2;
        box-shadow:
            inset 0 0 20px var(--primary-color, rgba($system-blue, 0.2)),
            0 0 0 0 var(--primary-color, rgba($system-blue, 0.2));
    }
}

@keyframes circle-rotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes light-sweep {
    0% {
        transform: rotate(-25deg) translateY(-120%);
    }
    50% {
        transform: rotate(-25deg) translateY(220%);
    }
    100% {
        transform: rotate(-25deg) translateY(-120%);
    }
}
</style>
