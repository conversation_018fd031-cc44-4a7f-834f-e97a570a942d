<template>
    <div class="app-header" v-if="visible">
        <!-- 左侧区域：返回按钮 -->
        <div class="header-left">
            <div v-if="showBack" class="back-button" @click="handleBack">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/arrowleft.png"
                    alt=""
                    srcset=""
                />
            </div>
            <div v-if="showTitle" class="header-title">{{ title }}</div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

/**
 * 通用Header组件
 * @property {boolean} visible - 是否显示header
 * @property {boolean} showBack - 是否显示返回按钮
 * @property {boolean} showExit - 是否显示退出按钮
 * @property {boolean} showTitle - 是否显示标题
 * @property {string} title - 标题文本
 * @property {Function} onBack - 自定义返回事件处理函数
 * @property {Function} onExit - 自定义退出事件处理函数
 */
const props = defineProps({
    visible: {
        type: Boolean,
        default: true
    },
    showBack: {
        type: Boolean,
        default: true
    },
    showExit: {
        type: Boolean,
        default: false
    },
    showTitle: {
        type: Boolean,
        default: true
    },
    title: {
        type: String,
        default: '返回'
    }
});

const router = useRouter();

/**
 * 处理返回按钮点击事件
 */
const handleBack = () => {
    router.back();
};
</script>

<style lang="scss" scoped>
.app-header {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: transparent;
    position: relative;
    z-index: 10;
    padding-left: 4px;

    .header-left {
        display: flex;
        align-items: center;

        .back-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            cursor: pointer;

            img {
                width: 18px;
                height: 18px;
            }
        }

        .header-title {
            font-size: 16px;
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .header-right {
        display: flex;
        align-items: center;

        .exit-button {
            display: flex;
            align-items: center;
            padding: 0 8px;
            height: 32px;
            cursor: pointer;

            .pi {
                font-size: 16px;
                margin-right: 4px;
            }

            span {
                font-size: 14px;
            }
        }
    }
}
</style>
