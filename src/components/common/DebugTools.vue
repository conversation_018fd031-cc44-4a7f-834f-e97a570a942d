<template>
    <div class="debug-tools" v-if="isVisible">
        <div class="debug-panel">
            <div class="debug-header">
                <span>调试工具</span>
                <Button icon="pi pi-times" text rounded aria-label="关闭" @click="hide" />
            </div>
            <div class="debug-content">
                <div class="debug-item">
                    <span>VConsole</span>
                    <ToggleButton
                        v-model="vConsoleEnabled"
                        onLabel="开启"
                        offLabel="关闭"
                        @change="toggleVConsole"
                    />
                </div>
                <div class="debug-item">
                    <span>环境信息</span>
                    <Button label="查看" size="small" @click="showEnvInfo" />
                </div>
                <div class="debug-item">
                    <span>清除缓存</span>
                    <Button label="清除" severity="danger" size="small" @click="clearCache" />
                </div>
            </div>
            <div class="debug-info">
                <div><strong>环境:</strong> {{ currentEnv }}</div>
                <div><strong>平台:</strong> {{ platformInfo }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { toggleVConsole, shouldEnableVConsole } from '@/utils/debugService';
import { detectDingTalkPlatform, DINGTALK_PLATFORM, isDingTalk } from '@/utils/index';
import Button from 'primevue/button';
import ToggleButton from 'primevue/togglebutton';
import { useToast } from 'primevue/usetoast';

const toast = useToast();
const isVisible = ref(false);
const vConsoleEnabled = ref(shouldEnableVConsole());

// 计算当前环境
const currentEnv = computed(() => {
    const mode = import.meta.env.MODE;
    switch (mode) {
    case 'development':
        return '开发环境';
    case 'test':
        return '测试环境';
    case 'production':
        return '生产环境';
    default:
        return mode;
    }
});

// 计算平台信息
const platformInfo = computed(() => {
    if (!isDingTalk()) {
        return '非钉钉环境';
    }

    const platform = detectDingTalkPlatform();
    switch (platform) {
    case DINGTALK_PLATFORM.IOS:
        return '钉钉 iOS';
    case DINGTALK_PLATFORM.ANDROID:
        return '钉钉 Android';
    case DINGTALK_PLATFORM.PC:
        return '钉钉 PC';
    default:
        return '未知钉钉平台';
    }
});

// 显示调试面板
const show = () => {
    isVisible.value = true;
};

// 隐藏调试面板
const hide = () => {
    isVisible.value = false;
};

// // 切换 VConsole
// const handleToggleVConsole = () => {
//     const isEnabled = toggleVConsole();
//     vConsoleEnabled.value = isEnabled;

//     toast.add({
//         severity: 'info',
//         summary: 'VConsole',
//         detail: isEnabled ? 'VConsole 已启用' : 'VConsole 已禁用',
//         life: 2000
//     });
// };

// 显示环境信息
const showEnvInfo = () => {
    const envInfo = {
        环境: currentEnv.value,
        平台: platformInfo.value,
        版本: import.meta.env.VITE_APP_VERSION || '未知',
        API地址: import.meta.env.VITE_API_BASE_URL || '未知',
        用户代理: navigator.userAgent,
        屏幕尺寸: `${window.innerWidth}x${window.innerHeight}`,
        设备像素比: window.devicePixelRatio
    };

    console.table(envInfo);

    toast.add({
        severity: 'info',
        summary: '环境信息',
        detail: '环境信息已输出到控制台',
        life: 2000
    });
};

// 清除缓存
const clearCache = () => {
    try {
        localStorage.clear();
        sessionStorage.clear();

        toast.add({
            severity: 'success',
            summary: '清除成功',
            detail: '本地缓存已清除',
            life: 2000
        });
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: '清除失败',
            detail: error.message || '清除缓存时发生错误',
            life: 3000
        });
    }
};

// 暴露方法给父组件
defineExpose({
    show,
    hide
});

onMounted(() => {
    // 添加触发调试面板的手势
    // 在开发和测试环境中，连续点击屏幕5次可以打开调试面板
    // if (import.meta.env.MODE !== 'production') {
    //     let tapCount = 0;
    //     let lastTapTime = 0;

    //     document.addEventListener('click', (event) => {
    //         const currentTime = new Date().getTime();
    //         const tapInterval = currentTime - lastTapTime;

    //         if (tapInterval < 500 && tapInterval > 0) {
    //             tapCount++;

    //             if (tapCount >= 5) {
    //                 show();
    //                 tapCount = 0;
    //             }
    //         } else {
    //             tapCount = 1;
    //         }

    //         lastTapTime = currentTime;
    //     });
    // }
});
</script>

<style lang="scss" scoped>
.debug-tools {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    .debug-panel {
        width: 90%;
        max-width: 350px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        overflow: hidden;

        .debug-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eee;

            span {
                font-weight: bold;
                font-size: 16px;
            }
        }

        .debug-content {
            padding: 16px;

            .debug-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                span {
                    font-size: 14px;
                }
            }
        }

        .debug-info {
            padding: 12px 16px;
            background-color: #f9f9f9;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;

            div {
                margin-bottom: 4px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
