<template>
    <div class="markdown-viewer">
        <div class="markdown-viewer__toc" v-if="showToc">
            <div class="toc-title">目录</div>
            <div class="toc-content" v-html="toc"></div>
        </div>
        <div class="markdown-viewer__content markdown-body" v-html="parsedContent"></div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import 'github-markdown-css/github-markdown.css';

const props = defineProps({
    // Markdown内容
    content: {
        type: String,
        required: true,
        default: ''
    },
    // 是否显示目录
    showToc: {
        type: Boolean,
        default: true
    },
    // 是否启用代码高亮
    enableHighlight: {
        type: Boolean,
        default: true
    }
});

// 创建markdown-it实例
const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: props.enableHighlight ? highlightCode : null
});

// 代码高亮函数
function highlightCode(str, lang) {
    if (lang && hljs.getLanguage(lang)) {
        try {
            return `<pre class="hljs"><code>${hljs.highlight(str, { language: lang, ignoreIllegals: true }).value}</code></pre>`;
        } catch (e) {
            console.error('代码高亮出错:', e);
        }
    }
    return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
}

// 解析后的Markdown内容
const parsedContent = computed(() => {
    if (!props.content) {return '';}
    let htmlContent = md.render(props.content);

    // 在DOM环境中处理HTML以添加特殊类
    if (typeof document !== 'undefined') {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // 查找包含"对接优势"的h3标签并添加类
        tempDiv.querySelectorAll('h3').forEach((h3) => {
            if (h3.textContent.includes('对接优势')) {
                h3.classList.add('docking-advantage');
            }

            // 为技术亮点标题添加类
            if (h3.textContent.includes('技术亮点')) {
                h3.classList.add('tech-highlight');
            }
        });

        htmlContent = tempDiv.innerHTML;
    }

    return htmlContent;
});

// 生成目录
const toc = ref('');

// 在组件挂载后生成目录
onMounted(() => {
    if (props.showToc) {
        setTimeout(generateToc, 100);
    }

    // 添加平滑滚动效果
    document.querySelectorAll('.markdown-viewer__content a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});

// 生成目录函数
function generateToc() {
    const headings = document.querySelectorAll(
        '.markdown-viewer__content h1, .markdown-viewer__content h2, .markdown-viewer__content h3'
    );

    if (headings.length === 0) {
        toc.value = '<div class="empty-toc">没有找到标题</div>';
        return;
    }

    let tocHtml = '<ul class="toc-list">';

    headings.forEach((heading, index) => {
        // 为标题添加ID，以便锚点链接
        const headingId = `heading-${index}`;
        heading.id = headingId;

        const level = parseInt(heading.tagName.substring(1));
        const indent = (level - 1) * 20; // 根据标题级别缩进
        const text = heading.textContent;

        // 根据标题级别添加不同的图标
        let icon = '';
        if (level === 1) {
            icon = '<i class="toc-icon toc-icon-h1">📑</i>';
        } else if (level === 2) {
            icon = '<i class="toc-icon toc-icon-h2">📌</i>';
        } else {
            icon = '<i class="toc-icon toc-icon-h3">📎</i>';
        }

        tocHtml += `
      <li class="toc-item" style="padding-left: ${indent}px;">
        <a href="#${headingId}" class="toc-link">${icon} ${text}</a>
      </li>
    `;
    });

    tocHtml += '</ul>';
    toc.value = tocHtml;
}
</script>

<style lang="scss">
.markdown-viewer {
    display: flex;
    gap: 20px;
    margin: 0 auto;
    max-width: 1200px;
    padding: 20px;
    font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Open Sans', 'Helvetica Neue', sans-serif;

    &__toc {
        position: sticky;
        top: 20px;
        width: 250px;
        max-height: calc(100vh - 40px);
        overflow-y: auto;
        padding: 16px;
        border-radius: 8px;
        background-color: #f8f9fa;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        @media (max-width: 768px) {
            display: none;
        }

        .toc-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e1e4e8;
            color: #333;
        }

        .toc-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .toc-icon {
            display: inline-block;
            margin-right: 6px;
            font-style: normal;
            vertical-align: middle;
        }

        .toc-item {
            margin: 8px 0;
            line-height: 1.3;
        }

        .toc-link {
            color: #0366d6;
            text-decoration: none;
            display: block;
            padding: 4px 0;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
                background-color: #e1e4e8;
                padding-left: 4px;
            }
        }
    }

    &__content {
        flex: 1;
        min-width: 0;
        background-color: #fff;
        border-radius: 8px;
        padding: 32px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.16);
        }

        // 自定义GitHub风格的Markdown样式
        &.markdown-body {
            h1,
            h2,
            h3,
            h4,
            h5,
            h6 {
                margin-top: 24px;
                margin-bottom: 16px;
                font-weight: 600;
                line-height: 1.25;
                padding-bottom: 0.3em;
                border-bottom: 1px solid #eaecef;
            }

            h1 {
                font-size: 2em;
                color: #24292e;
            }

            h2 {
                font-size: 1.5em;
                color: #005cc5;
                background: linear-gradient(90deg, #f8f9fa, transparent);
                padding: 8px 8px 8px 16px;
                border-radius: 4px;
                margin-top: 32px;
                border-bottom: 2px solid #0366d6;
            }

            h3 {
                font-size: 1.25em;
                color: #d73a49;
                padding-left: 8px;
                border-left: 3px solid #d73a49;
            }

            p {
                margin-top: 0;
                margin-bottom: 16px;
                line-height: 1.6;
            }

            ul,
            ol {
                padding-left: 2em;
                margin-top: 0;
                margin-bottom: 16px;
            }

            li {
                margin-bottom: 0.25em;
            }

            // 代码块样式
            pre {
                background-color: #f6f8fa;
                border-radius: 6px;
                padding: 16px;
                overflow: auto;
                margin-bottom: 16px;
                box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05);
            }

            code {
                font-family:
                    SFMono-Regular,
                    Consolas,
                    Liberation Mono,
                    Menlo,
                    monospace;
                padding: 0.2em 0.4em;
                margin: 0;
                font-size: 85%;
                background-color: rgba(27, 31, 35, 0.05);
                border-radius: 3px;
            }

            pre > code {
                padding: 0;
                margin: 0;
                background-color: transparent;
                border: 0;
            }

            // 技术亮点卡片样式
            h3.tech-highlight + ul {
                background-color: #f8fdff;
                border-left: 4px solid #2196f3;
                padding: 12px 12px 12px 28px;
                border-radius: 0 4px 4px 0;
                margin-bottom: 24px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
                transition: all 0.2s ease;

                &:hover {
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
                    transform: translateY(-2px);
                }

                li {
                    margin-bottom: 8px;
                    line-height: 1.5;

                    strong {
                        color: #0366d6;
                    }
                }
            }

            // 对接优势卡片样式
            h3.docking-advantage + ul {
                background-color: #f9fff8;
                border-left: 4px solid #4caf50;
                padding: 12px 12px 12px 28px;
                border-radius: 0 4px 4px 0;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
                transition: all 0.2s ease;

                &:hover {
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
                    transform: translateY(-2px);
                }

                li {
                    margin-bottom: 8px;

                    strong {
                        color: #2e7d32;
                    }
                }
            }

            // 综合技术亮点部分特殊样式
            h2:last-of-type + ol {
                background-color: #fff8f0;
                padding: 16px 16px 16px 48px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
                margin-bottom: 32px;

                li {
                    margin-bottom: 16px;

                    strong {
                        color: #e65100;
                    }

                    ul {
                        margin-top: 8px;
                    }
                }
            }

            // 表格样式
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
                border-radius: 4px;
                overflow: hidden;

                th,
                td {
                    padding: 12px 16px;
                    border: 1px solid #e1e4e8;
                }

                th {
                    background-color: #f1f3f5;
                    font-weight: 600;
                    color: #333;
                }

                tr:nth-child(even) {
                    background-color: #f8f9fa;
                }

                tr:hover {
                    background-color: #f0f4f8;
                }
            }

            // 链接样式
            a {
                color: #0366d6;
                text-decoration: none;
                transition: color 0.2s ease;

                &:hover {
                    color: #0056b3;
                    text-decoration: underline;
                }
            }

            // 引用样式
            blockquote {
                padding: 0.8em 1em;
                color: #6a737d;
                border-left: 0.25em solid #dfe2e5;
                margin: 0 0 16px 0;
                background-color: #f8f9fa;
                border-radius: 0 4px 4px 0;

                > :first-child {
                    margin-top: 0;
                }

                > :last-child {
                    margin-bottom: 0;
                }
            }

            // 部分样式美化
            section {
                margin-bottom: 32px;
                padding-bottom: 16px;
            }

            // 强调内容样式
            em {
                color: #6f42c1;
                font-style: italic;
            }

            // 加粗内容样式
            strong {
                font-weight: 600;
                color: #24292e;
            }

            // 图片样式
            img {
                max-width: 100%;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            // 水平线样式
            hr {
                height: 1px;
                background-color: #e1e4e8;
                border: none;
                margin: 24px 0;
            }
        }
    }
}

// 媒体查询调整移动设备显示
@media (max-width: 768px) {
    .markdown-viewer {
        flex-direction: column;
        padding: 12px;

        &__content {
            padding: 16px;
        }
    }
}

// 打印样式优化
@media print {
    .markdown-viewer {
        padding: 0;

        &__toc {
            display: none;
        }

        &__content {
            box-shadow: none;
            padding: 0;

            pre {
                white-space: pre-wrap;
                word-wrap: break-word;
            }
        }
    }
}
</style>
