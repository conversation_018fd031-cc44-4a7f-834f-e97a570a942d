<template>
    <div class="ai-loading-state">
        <div class="loading-container">
            <div class="loading-reactor">
                <div class="reactor-core"></div>
                <div class="reactor-ring"></div>
                <div
                    v-for="n in 8"
                    :key="`core-line-${n}`"
                    class="reactor-line"
                    :style="`transform: rotate(${n * 45}deg)`"
                ></div>
            </div>
            <div class="loading-text">
                <span class="text-primary">{{ text }}</span>
                <span class="text-dots">{{ dots }}</span>
                <div class="ai-progress">
                    <div class="progress-track"></div>
                    <div class="progress-fill" :style="`width: ${progress}%`"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
    // 是否自动启动动画
    autoStart: {
        type: Boolean,
        default: true
    },
    // 加载进度（0-100）
    initialProgress: {
        type: Number,
        default: 0
    },
    // 初始加载文本
    initialText: {
        type: String,
        default: '正在初始化...'
    }
});

const text = ref(props.initialText);
const dots = ref('');
const progress = ref(props.initialProgress);
const dotsCount = ref(0);
const loadingInterval = ref(null);

// 更新加载动画
const updateAnimation = () => {
    if (loadingInterval.value) {return;}

    loadingInterval.value = setInterval(() => {
        // 更新加载文本动画
        dotsCount.value = (dotsCount.value + 1) % 4;
        dots.value = '.'.repeat(dotsCount.value);

        // 如果没有外部控制进度，自动增加进度
        if (props.initialProgress === 0) {
            if (progress.value < 95) {
                progress.value += Math.random() * 5 + 1;
                if (progress.value > 95) {progress.value = 95;}
            }

            // 根据进度更新加载文本
            if (progress.value < 30) {
                text.value = '正在初始化AI训练环境';
            } else if (progress.value < 60) {
                text.value = '加载训练模型数据';
            } else if (progress.value < 90) {
                text.value = '准备AI训练场景';
            } else {
                text.value = '完成最终准备';
            }
        }
    }, 300);
};

// 清理动画定时器
const clearAnimation = () => {
    if (loadingInterval.value) {
        clearInterval(loadingInterval.value);
        loadingInterval.value = null;
    }
};

// 完成加载
const completeLoading = () => {
    progress.value = 100;
    text.value = '准备完成';
    setTimeout(clearAnimation, 300);
};

defineExpose({
    updateAnimation,
    clearAnimation,
    completeLoading,
    progress,
    text
});

onMounted(() => {
    if (props.autoStart) {
        updateAnimation();
    }
});

onBeforeUnmount(() => {
    clearAnimation();
});
</script>

<style lang="scss" scoped>
.ai-loading-state {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 24px;
        padding: 36px;
        background: rgba($system-background-primary, 0.75);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.15);
        width: 280px;
        animation: fadeIn 0.3s ease;

        .loading-reactor {
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;

            .reactor-core {
                width: 30px;
                height: 30px;
                background: $system-blue;
                border-radius: 50%;
                box-shadow: 0 0 30px rgba($system-blue, 0.8);
                animation: pulseCore 1.5s ease-in-out infinite alternate;
            }

            .reactor-ring {
                position: absolute;
                width: 60px;
                height: 60px;
                border: 2px solid rgba($system-indigo, 0.8);
                border-radius: 50%;
                animation: rotateRing 4s linear infinite;
            }

            .reactor-line {
                position: absolute;
                width: 100%;
                height: 1px;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba($system-blue, 0.8),
                    transparent
                );
                transform-origin: center;
                z-index: -1;
                opacity: 0.6;
                animation: pulseLine 2s infinite ease;
            }
        }

        .loading-text {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            width: 100%;

            .text-primary {
                font-size: 14px;
                font-weight: 600;
                color: $label-primary;
            }

            .text-dots {
                font-size: 14px;
                font-weight: 600;
                color: $system-blue;
                height: 20px;
                min-width: 20px;
                text-align: center;
            }

            .ai-progress {
                width: 100%;
                height: 4px;
                background: rgba($system-background-secondary, 0.4);
                border-radius: 2px;
                overflow: hidden;
                margin-top: 8px;

                .progress-track {
                    width: 100%;
                    height: 100%;
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(
                            to right,
                            transparent,
                            rgba(255, 255, 255, 0.3),
                            transparent
                        );
                        animation: shimmer 2s infinite;
                    }
                }

                .progress-fill {
                    width: 0%;
                    height: 100%;
                    background: linear-gradient(
                        to right,
                        $system-blue,
                        $system-indigo,
                        $system-teal
                    );
                    border-radius: 2px;
                    transition: width 0.3s ease;
                }
            }
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulseCore {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes pulseLine {
    0%,
    100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes rotateRing {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 响应式调整
@media (max-width: 768px) {
    .ai-loading-state {
        .loading-container {
            width: 250px;
            padding: 30px;
        }
    }
}
</style>
