<template>
    <div class="audio-wrapper">
        <audio
            ref="audioElement"
            :src="audioUrl"
            class="audio-player"
            preload="metadata"
            type="audio/wav"
        ></audio>
        <div
            class="audio-bubble"
            :data-theme="theme"
            @click="handlePlay"
            :class="{ 'is-playing': isPlaying }"
        >
            <div class="audio-controls">
                <div class="speaker-icon" :class="{ 'is-playing': isPlaying }">
                    <img v-if="!isPlaying" :src="playButtonSrc" alt="播放" class="control-icon" />
                    <img v-else :src="pauseButtonSrc" alt="暂停" class="control-icon" />
                </div>
                <div class="duration" v-if="Number(duration) > 0">
                    {{ formatTime(duration) }}
                </div>
            </div>
            <div v-if="text" class="audio-text">{{ text }}</div>
        </div>
    </div>
</template>

<script>
import audioManager from '@/utils/audioManager';

export default {
    name: 'AudioPlayer',
    props: {
        audioUrl: {
            type: String,
            required: true
        },
        text: {
            type: String,
            default: ''
        },
        theme: {
            type: String,
            default: 'default',
            validator: (value) => ['default', 'green'].includes(value)
        },
        playButtonSrc: {
            type: String,
            default: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/play3.png'
        },
        pauseButtonSrc: {
            type: String,
            default: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/pause3.png'
        }
    },
    data() {
        return {
            isPlaying: false,
            duration: 0,
            audioId: null,
            unsubscribe: null
        };
    },
    watch: {
        audioUrl: {
            handler(newUrl) {
                if (newUrl) {
                    this.audioId = `audio-${newUrl}`;
                    this.$nextTick(() => {
                        this.initAudio();
                    });
                }
            },
            immediate: true
        }
    },
    methods: {
        initAudio() {
            const audio = this.$refs.audioElement;
            if (!audio) {
                return;
            }

            audio.addEventListener('loadedmetadata', () => {
                this.duration = audio.duration;
            });
            audio.addEventListener('ended', this.handleEnded);

            this.unsubscribe = audioManager.onStopRequest(this.audioId, () => {
                if (this.isPlaying) {
                    audio.pause();
                    this.isPlaying = false;
                }
            });
        },

        async handlePlay() {
            const audio = this.$refs.audioElement;
            if (!audio) {
                return;
            }

            try {
                if (!this.isPlaying) {
                    audioManager.registerPlay(this.audioId);

                    audio.currentTime = 0;
                    await audio.play();
                    this.isPlaying = true;
                    this.$emit('play-start');
                } else {
                    audio.pause();
                    this.isPlaying = false;
                    audioManager.registerStop(this.audioId);
                }
            } catch (error) {
                console.error('音频播放失败:', error);
                // this.$toast?.add({
                //     severity: 'info',
                //     summary: '自动播放失败',
                //     detail: '请手动播放',
                //     life: 3000,
                // });
            }
        },

        handleEnded() {
            this.isPlaying = false;
            audioManager.registerStop(this.audioId);
            this.$emit('play-end');
        },

        formatTime(seconds) {
            if (!seconds) {
                return '0"';
            }
            return Math.round(seconds) + '"';
        }
    },
    mounted() {
        this.initAudio();
    },
    beforeUnmount() {
        const audio = this.$refs.audioElement;
        if (audio) {
            audio.removeEventListener('ended', this.handleEnded);
            // 移除 loadedmetadata 事件监听器（匿名函数无法移除，这里仅作清理标记）
        }

        if (this.unsubscribe) {
            this.unsubscribe();
        }

        if (this.isPlaying) {
            audioManager.registerStop(this.audioId);
        }
    }
};
</script>

<style lang="scss" scoped>
.audio-wrapper {
    width: 80%;

    .audio-player {
        display: none;
    }

    .audio-bubble {
        position: relative;
        background-color: #fff;
        border-radius: 6px;
        padding: 8px 12px;
        margin-left: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        max-width: 100%;
        cursor: pointer;
        transition: all 0.2s ease;

        &:active {
            opacity: 0.9;
        }

        &::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 16px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-right: 6px solid #fff;
            transition: all 0.2s ease;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 8px;

            .speaker-icon {
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                margin-left: 2px;
                position: relative;
                transition: all 0.3s ease;

                .control-icon {
                    width: 24px;
                    height: 24px;
                    transition: all 0.3s ease;
                }
            }

            .duration {
                font-size: 14px;
                color: #333;
                font-weight: 600;
                transition: color 0.2s ease;
            }
        }

        .audio-text {
            padding-top: 6px;
            border-top: 1px solid #f5f5f5;
            margin-top: 6px;
            font-size: 14px;
            color: #333;
            font-weight: 600;
            line-height: 1.4;
            word-break: break-all;
            white-space: pre-wrap;
            transition:
                color 0.2s ease,
                border-color 0.2s ease;
        }

        &[data-theme='green'] {
            background-color: #91ed61;
            margin-left: auto;
            margin-right: 10px;

            &::before {
                left: auto;
                right: -6px;
                border-right: none;
                border-left: 6px solid #91ed61;
            }

            .speaker-icon {
                .control-icon {
                    color: #333;
                }
            }

            .duration {
                color: #333;
            }

            .audio-text {
                color: #333;
                border-top-color: rgba(255, 255, 255, 0.2);
            }
        }
    }
}
</style>
