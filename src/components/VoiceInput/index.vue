<template>
    <div class="voice-input-container">
        <!-- 添加 Loading 组件 -->
        <Loading :config="loadingConfig" />

        <!-- 录音状态 -->
        <div class="recording-status" v-show="isRecording">
            <div class="time-display" v-if="isRecordingStarted">
                <WaveAnimation v-if="!isMaxDurationReached" direction="left" :waveCount="10" />

                <div class="max-duration-tip" v-if="isMaxDurationReached">
                    已达到最大录制时长 {{ formatDuration(maxRecordTime) }}
                </div>

                <span
                    v-if="!isHoveringLock && !isHoveringClose && !isMaxDurationReached"
                    class="time-text"
                    :class="{
                        'countdown-warning': isCountingDown && !showConfirmIcon,
                        'final-duration': showConfirmIcon
                    }"
                >
                    {{ getTimeDisplay }}
                </span>
                <span v-else-if="isHoveringLock" class="lock-tip"> 松开锁定录音模式 </span>
                <span v-else-if="isHoveringClose" class="close-tip"> 松开取消录音 </span>

                <WaveAnimation v-if="!isMaxDurationReached" direction="right" :waveCount="10" />
            </div>

            <!-- 新增：录音初始化状态显示 -->
            <div class="initializing-status" v-else>
                <span class="initializing-text">录音准备中</span>
            </div>

            <div class="button-group">
                <div class="button-wrapper">
                    <div
                        class="close-button"
                        :class="{ 'is-hover': isHoveringClose }"
                        @click="handleCloseRecording"
                    >
                        <i class="pi pi-times"></i>
                    </div>
                    <span class="button-text">取消录音</span>
                </div>

                <div class="button-wrapper" v-if="!isLocked">
                    <div
                        class="lock-button"
                        :class="{ 'is-hover': isHoveringLock }"
                        ref="lockButton"
                    >
                        <i class="pi pi-lock"></i>
                    </div>
                    <span class="button-text">上滑锁定</span>
                </div>
            </div>

            <!-- 添加底部录音状态图标 -->
            <div class="recording-icon-status" v-show="!isLocked">
                <div class="recording-icon">
                    <img
                        src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/microphone.png"
                        alt="录音"
                    />
                </div>
            </div>

            <!-- 新增：锁定模式下的结束录音按钮 -->
            <div class="recording-icon-status" v-show="isLocked">
                <template v-if="!showConfirmIcon && !isStoppingRecord">
                    <div class="button-wrapper">
                        <div class="finish-button" @click="handleShowConfirm">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/stopRecord2.png"
                                alt="结束录音"
                                class="finish-icon"
                            />
                        </div>
                        <span class="button-text">点击结束</span>
                    </div>
                </template>
                <template v-else-if="isStoppingRecord">
                    <div class="button-wrapper">
                        <div class="loading-button">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/recordFinish.png"
                                alt="处理中"
                                class="loading-icon"
                            />
                        </div>
                        <span class="button-text">处理中</span>
                    </div>
                </template>
                <template v-else>
                    <div class="button-wrapper">
                        <div class="confirm-button" @click="handleUseRecording">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/choose.png"
                                alt="使用"
                                class="confirm-icon"
                            />
                        </div>
                        <span class="button-text">确认使用</span>
                    </div>
                </template>
            </div>

            <!-- 录音面板底部文案 -->
            <div class="recording-footer-text">
                <img
                    src="https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-chat2.png"
                    alt="logo"
                />
                powered by 家家精灵 - 之家钉钉智能体平台
            </div>
        </div>

        <!-- 默认状态 -->
        <div class="voice-button-wrapper" v-show="!isRecording">
            <div class="voice-button" :class="{ recording: isRecording }">
                <img
                    :src="
                        isRecording
                            ? 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/theme.png'
                            : 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/microphone.png'
                    "
                    :alt="isRecording ? '正在录音' : '开始录音'"
                />
                <span class="voice-status">{{ isRecording ? '松开发送' : '按住回答' }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted, computed } from 'vue';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import WaveAnimation from '@/components/common/Wave.vue';
import Loading from '@/components/Loading.vue'; // 导入 Loading 组件
import audioManager from '@/utils/audioManager';
import * as dd from 'dingtalk-jsapi'; // 直接导入

// 注册 duration 插件
dayjs.extend(duration);

// 注入钉钉实例
const emit = defineEmits(['recording-complete']);

// 响应式状态
const isRecording = ref(false);
const recordTimer = ref(null);
const recordStartTime = ref(0);
const recordDuration = ref(0);
const maxRecordTime = 60000; // 最大录音时长 20秒
const minRecordTime = 1000; // 最小录音时长 1s
const isHoveringLock = ref(false);
const isHoveringClose = ref(false);
const lockButton = ref(null);
const isLocked = ref(false);
const touchStartY = ref(0);
const touchStartX = ref(0);
const showConfirmIcon = ref(false);
const currentRecording = ref(null);
const touchStartTime = ref(0);
const TOUCH_TIME_THRESHOLD = 300; // 设置触摸时间阈值，单位毫秒
const isStoppingRecord = ref(false);
const COUNTDOWN_THRESHOLD = 10000; // 10秒开始倒计时

// 新增：录音进程锁定状态，防止重复调用
const isRecordingProcessLocked = ref(false);
// 新增：最小按压时间才能开始录音（毫秒）
const MIN_PRESS_TIME_TO_START = 0;
// 新增：上次录音结束时间
const lastRecordEndTime = ref(0);
// 新增：录音冷却时间（毫秒）
const RECORD_COOLDOWN_TIME = 500;

// 新增：跟踪事件绑定状态
const isEventsBound = ref(false);

// 新增：录音是否真正开始的状态
const isRecordingStarted = ref(false);

// 添加一个新的状态来跟踪按钮是否被按下
const isButtonPressed = ref(false);

// 添加最大时长到达状态
const isMaxDurationReached = computed(() => {
    return recordDuration.value >= maxRecordTime;
});

// 添加加载状态配置 - 从 reactive 改为 ref
const loadingConfig = ref({
    visible: false,
    status: 'loading', // loading, loaded, failed
    message: ''
});

// 显示加载状态
const showLoading = (message, status = 'loading') => {
    loadingConfig.value.visible = true;
    loadingConfig.value.status = status;
    loadingConfig.value.message = message;
};

// 隐藏加载状态
const hideLoading = () => {
    loadingConfig.value.visible = false;
};

// 提取格式化时间的函数
const formatDuration = ms => {
    const durationObj = dayjs.duration(ms);
    return durationObj.format('m:ss');
};

// 修改：格式化时间显示的计算属性
const getTimeDisplay = computed(() => {
    // 当处于锁定状态且显示确认图标时，显示最终录音时长
    if (isLocked.value && showConfirmIcon.value) {
        return formatDuration(recordDuration.value);
    }

    // 当倒计时时显示剩余时间
    if (isCountingDown.value) {
        const remainingTime = maxRecordTime - recordDuration.value;
        // 显示为 还剩 20s
        return `还剩 ${formatDuration(remainingTime)}`;
    }

    // 默认显示当前录音时长
    return formatDuration(recordDuration.value);
});

// 修改：事件绑定函数
const bindTouchEvents = () => {
    // 如果已经绑定过，则不重复绑定
    if (isEventsBound.value) {
        return;
    }

    const container = document.querySelector('.voice-input-container');
    if (container) {
        container.addEventListener('touchstart', handleTouchStart);
        container.addEventListener('touchmove', handleTouchMove);
        container.addEventListener('touchend', handleTouchEnd);
        isEventsBound.value = true;
    }
};

// 修改：解绑事件函数
const unbindTouchEvents = () => {
    const container = document.querySelector('.voice-input-container');
    if (container) {
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('touchend', handleTouchEnd);
        isEventsBound.value = false;
    }
};

// 修改：在 onMounted 中绑定事件
onMounted(() => {
    bindTouchEvents();

    // 预加载图片资源
    const preloadImages = [
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/microphone.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/theme.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/stopRecord2.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/recordFinish.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/choose.png'
    ];

    preloadImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
});

// 修改：将计时器逻辑独立出来
const startRecordTimer = () => {
    recordStartTime.value = Date.now();
    recordDuration.value = 0; // 确保在开始新计时器时重置录音时长
    recordTimer.value = setInterval(() => {
        recordDuration.value = Date.now() - recordStartTime.value;
        // 检查是否需要自动结束录音
        if (recordDuration.value >= maxRecordTime) {
            if (isLocked.value) {
                handleShowConfirm();
            } else {
                handleFinishRecording();
            }
            // 触发振动提示
            dd.vibrate({
                duration: 300
            });
        }
    }, 100);
};

// 修改：触摸开始处理函数
const handleTouchStart = event => {
    event.preventDefault();
    const touch = event.touches[0];
    touchStartY.value = touch.clientY;
    touchStartX.value = touch.clientX;
    touchStartTime.value = Date.now();

    // 检查是否在录音冷却期内
    if (Date.now() - lastRecordEndTime.value < RECORD_COOLDOWN_TIME) {
        showLoading('录音冷却中，请重试', 'failed');

        setTimeout(() => {
            hideLoading();
        }, 1500);

        return;
    }

    // 确保在开始新录音前重置之前的状态
    if (isRecording.value) {
        resetRecordingState();
    }

    if (!isRecording.value && !isRecordingProcessLocked.value && isVoiceButtonTouched(event)) {
        // 标记按钮被按下
        isButtonPressed.value = true;

        // 设置一个短暂的延迟，确保用户不是快速点击
        setTimeout(async () => {
            // 如果用户在最小按压时间内松开了，则不启动录音
            if (
                Date.now() - touchStartTime.value < MIN_PRESS_TIME_TO_START ||
                !isButtonPressed.value
            ) {
                showLoading('录音时长过短', 'failed');
                setTimeout(() => {
                    hideLoading();
                }, 1500);
                return;
            }

            // 使用我们的状态变量来检查按钮是否仍被按下
            if (isButtonPressed.value) {
                isRecordingProcessLocked.value = true; // 锁定录音进程
                isRecording.value = true;
                isRecordingStarted.value = false; // 重置录音开始状态
                recordDuration.value = 0; // 确保在开始新录音前重置录音时长

                // 在开始录音前暂停所有正在播放的音频
                audioManager.stopAll();

                try {
                    await dd.device.audio.startRecord({
                        maxDuration: 100,
                        onSuccess: () => {
                            isRecordingStarted.value = true; // 录音真正开始
                            startRecordTimer();
                            isRecordingProcessLocked.value = false; // 录音成功启动后解锁
                        },
                        onFail: err => {
                            // 尝试强制停止当前录音进程
                            dd.device.audio.stopRecord({
                                onSuccess: () => {
                                    console.log('强制停止录音成功');
                                },
                                onFail: () => {
                                    console.error('强制停止录音失败');
                                }
                            });

                            // 特殊处理"录音进程正在运行"的错误
                            if (Number(err.errorCode) === 3) {
                                showLoading('录音进程正在运行，请重试', 'failed');
                            } else {
                                showLoading('无法开始录音，请检查钉钉麦克风权限', 'failed');
                            }
                            setTimeout(() => {
                                hideLoading();
                                resetRecordingState();
                            }, 1000);
                        }
                    });
                } catch (error) {
                    console.error('录音初始化失败:', error);
                    resetRecordingState();
                }
            }
        }, MIN_PRESS_TIME_TO_START);
    }
};

const handleTouchMove = event => {
    if (!isRecording.value) {
        return;
    }

    const touch = event.touches[0];

    // 检查是否触碰到锁定按钮区域
    const lockButtonEl = lockButton.value;
    if (lockButtonEl) {
        const rect = lockButtonEl.getBoundingClientRect();
        const isInLockArea =
            touch.clientX >= rect.left &&
            touch.clientX <= rect.right &&
            touch.clientY >= rect.top &&
            touch.clientY <= rect.bottom;
        isHoveringLock.value = isInLockArea;
    }

    // 检查是否触碰到关闭按钮区域
    const closeButtonRect = document.querySelector('.close-button')?.getBoundingClientRect();
    if (closeButtonRect) {
        const isInCloseArea =
            touch.clientX >= closeButtonRect.left &&
            touch.clientX <= closeButtonRect.right &&
            touch.clientY >= closeButtonRect.top &&
            touch.clientY <= closeButtonRect.bottom;
        isHoveringClose.value = isInCloseArea;
    }
};

// 修改触摸结束处理函数，更新按钮状态
const handleTouchEnd = () => {
    // 标记按钮释放
    isButtonPressed.value = false;

    const touchDuration = Date.now() - touchStartTime.value;

    if (touchDuration < TOUCH_TIME_THRESHOLD) {
        resetRecordingState();
        return;
    }

    if (isHoveringLock.value) {
        isLocked.value = true;
        unbindTouchEvents(); // 锁定时解绑事件
    } else if (isHoveringClose.value) {
        resetRecordingState();
    } else {
        if (recordDuration.value >= minRecordTime) {
            handleFinishRecording();
        } else {
            resetRecordingState();
        }
    }

    isHoveringLock.value = false;
    isHoveringClose.value = false;
};

// 辅助函数：检查是否触碰到语音按钮
const isVoiceButtonTouched = event => {
    const touch = event.touches[0];
    const voiceButton = document.querySelector('.voice-button');
    if (!voiceButton) {
        return false;
    }

    const rect = voiceButton.getBoundingClientRect();
    return (
        touch.clientX >= rect.left &&
        touch.clientX <= rect.right &&
        touch.clientY >= rect.top &&
        touch.clientY <= rect.bottom
    );
};

// 计算倒计时状态
const isCountingDown = computed(() => {
    return recordDuration.value >= maxRecordTime - COUNTDOWN_THRESHOLD;
});

// 修改：生命周期钩子
onBeforeUnmount(() => {
    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }
    if (isRecording.value) {
        dd.device.audio.stopRecord({
            onSuccess: () => {
                isRecording.value = false;
                recordDuration.value = 0;
            }
        });
    }
    // 确保组件销毁时解绑事件
    unbindTouchEvents();
});
// 结束录音
const handleFinishRecording = () => {
    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    try {
        dd.device.audio.stopRecord({
            onSuccess: res => {
                currentRecording.value = res;
                if (isLocked.value) {
                    handleShowConfirm();
                } else {
                    handleUseRecording();
                }
            },
            onFail: err => {
                console.error('录音停止失败:', err);
                showLoading('录音保存失败，请重试', 'failed');
                setTimeout(() => {
                    hideLoading();
                    resetRecordingState();
                }, 1500);
            }
        });
    } catch (error) {
        console.error('停止录音过程中发生异常:', error);
        resetRecordingState();
    }
};

// 显示确认图标
const handleShowConfirm = () => {
    // 暂停计时器
    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    // 设置loading状态
    isStoppingRecord.value = true;

    // 停止录音并保存
    dd.device.audio.stopRecord({
        onSuccess: res => {
            currentRecording.value = res;
            isStoppingRecord.value = false; // 结束loading状态
            showConfirmIcon.value = true;
        },
        onFail: err => {
            console.error('录音停止失败:', err);
            isStoppingRecord.value = false; // 结束loading状态
            showLoading('录音保存失败，请重试', 'failed');
            setTimeout(() => {
                hideLoading();
                resetRecordingState();
            }, 1500);
        }
    });
};

// 使用录音
const handleUseRecording = () => {
    if (currentRecording.value) {
        emit('recording-complete', currentRecording.value);
        resetRecordingState();
    } else {
        showLoading('录音数据丢失，请重试', 'failed');
        setTimeout(() => {
            hideLoading();
            resetRecordingState();
        }, 1500);
    }
};

// 重置录音状态
const resetRecordingState = () => {
    // 如果当前正在录音，需要先停止钉钉录音
    if (isRecording.value && isRecordingStarted.value) {
        completeReset();
        dd.device.audio.stopRecord({
            onSuccess: () => {
                console.log('录音已停止');
                completeReset();
            },
            onFail: err => {
                console.error('停止录音失败:', err);
                completeReset();
            }
        });
    } else {
        completeReset();
    }
};

// 完成重置的辅助函数
const completeReset = () => {
    isRecording.value = false;
    isRecordingStarted.value = false;
    isLocked.value = false;
    recordDuration.value = 0;
    currentRecording.value = null;
    touchStartTime.value = 0;
    showConfirmIcon.value = false;
    isStoppingRecord.value = false;
    isRecordingProcessLocked.value = false; // 重置录音进程锁定状态
    isButtonPressed.value = false; // 重置按钮状态
    lastRecordEndTime.value = Date.now(); // 记录录音结束时间

    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    if (!isEventsBound.value) {
        bindTouchEvents();
    }
};

// 关闭录音
const handleCloseRecording = () => {
    resetRecordingState();
};
</script>

<style lang="scss" scoped>
@use 'sass:color';

.voice-input-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .recording-status {
        padding-top: 10px;
        height: 270px;
        width: 100%;
        background-color: #fff;
        backdrop-filter: blur(10px);
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        transform: translateY(0);
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(
                90deg,
                transparent,
                rgba($system-background-primary, 0.8),
                transparent
            );
        }

        &.v-enter-from,
        &.v-leave-to {
            transform: translateY(100%);
        }

        .time-display {
            position: relative;
            z-index: 1;
            text-align: center;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 5px;

            .time-text {
                position: relative;
                color: $label-primary;
                font-weight: 600;
                min-width: 60px;
                text-align: center;
                font-size: 16px;
                letter-spacing: 0.5px;
                text-shadow: 0 1px 0 rgba($system-background-primary, 0.8);

                &.countdown-warning {
                    color: $system-red;
                    animation: pulseText 1s infinite alternate;
                }

                &.final-duration {
                    color: $system-blue;
                    font-weight: 700;
                }
            }

            .lock-tip,
            .close-tip {
                font-weight: 600;
                font-size: 15px;
                padding: 4px 12px;
                border-radius: 16px;
                letter-spacing: 0.3px;
            }

            .lock-tip {
                color: $system-blue;
                background: rgba($system-blue, 0.08);
            }

            .close-tip {
                color: $system-red;
                background: rgba($system-red, 0.08);
            }

            .max-duration-tip {
                position: absolute;
                top: 0px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba($system-red, 0.1);
                color: $system-red;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                animation: fadeInDown 0.3s ease-out;
                box-shadow: 0 2px 8px rgba($system-red, 0.15);
                backdrop-filter: blur(4px);
            }
        }

        .button-group {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0 60px;
            margin-top: 10px;
        }

        .button-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .button-text {
                font-size: 12px;
                color: $label-secondary;
                text-align: center;
                font-weight: 500;
                letter-spacing: 0.3px;
                margin-top: 2px;
            }
        }

        .lock-button,
        .close-button {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

            i {
                font-size: 22px;
                transition: all 0.3s ease;
            }
        }

        .lock-button {
            background: linear-gradient(135deg, $system-gray7 0%, $system-gray6 100%);
            border: 1px solid rgba($system-blue, 0.1);

            i {
                color: $system-blue;
            }

            &:active,
            &.is-hover {
                background: linear-gradient(
                    135deg,
                    $system-blue 0%,
                    color.mix($system-blue, black, $weight: 90%) 100%
                );
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba($system-blue, 0.25);
                border: 1px solid rgba($system-blue, 0.2);

                i {
                    color: #fff;
                    transform: scale(1.1);
                }
            }
        }

        .close-button {
            background: linear-gradient(135deg, $system-gray7 0%, $system-gray6 100%);
            border: 1px solid rgba($system-red, 0.1);

            i {
                color: $system-red;
            }

            &:active,
            &.is-hover {
                background: linear-gradient(
                    135deg,
                    $system-red 0%,
                    color.mix($system-red, black, $weight: 90%) 100%
                );
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba($system-red, 0.25);
                border: 1px solid rgba($system-red, 0.2);

                i {
                    color: #fff;
                    transform: scale(1.1);
                }
            }
        }

        // 添加底部录音状态图标样式
        .recording-icon-status {
            position: absolute;
            bottom: 35px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            padding: 10px;
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000;

            .recording-icon {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: linear-gradient(
                    135deg,
                    $system-blue 0%,
                    color.mix($system-blue, black, $weight: 90%) 100%
                );
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 6px 16px rgba($system-blue, 0.25), 0 0 0 6px rgba($system-blue, 0.08);
                will-change: transform;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: -10px;
                    left: -10px;
                    right: -10px;
                    bottom: -10px;
                    background: linear-gradient(
                        135deg,
                        rgba($system-blue, 0.3) 0%,
                        rgba(color.mix($system-blue, black, $weight: 90%), 0.3) 100%
                    );
                    border-radius: 50%;
                    z-index: -1;
                    animation: sonarWave 2s infinite;
                }

                img {
                    width: 30px;
                    height: 30px;
                    filter: brightness(0) invert(1);
                    animation: pulse 1.5s infinite;
                }
            }

            // 新增：结束录音按钮样式
            .finish-button,
            .confirm-button,
            .loading-button {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                will-change: transform;
            }

            .loading-button {
                background: linear-gradient(135deg, $system-gray7 0%, $system-gray6 100%);
                box-shadow: 0 6px 16px rgba($label-primary, 0.1),
                    0 0 0 6px rgba($label-primary, 0.03);

                .loading-icon {
                    width: 30px;
                    height: 30px;
                    animation: rotate 1s linear infinite;
                }
            }

            .confirm-button {
                background: linear-gradient(
                    135deg,
                    $system-blue 0%,
                    color.mix($system-blue, black, $weight: 90%) 100%
                );
                box-shadow: 0 6px 16px rgba($system-blue, 0.25), 0 0 0 6px rgba($system-blue, 0.08);
                will-change: transform;
                position: relative;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 50%;
                    height: 100%;
                    background: rgba($system-background-primary, 0.2);
                    transform: skewX(-25deg);
                    animation: shimmer 2s infinite;
                }

                .confirm-icon {
                    width: 30px;
                    height: 30px;
                    filter: brightness(0) invert(1);
                    transform: scale(1);
                    transition: transform 0.3s ease;
                }

                &:active {
                    transform: scale(0.95);

                    .confirm-icon {
                        transform: scale(0.9);
                    }
                }
            }

            .finish-button {
                background: rgba($system-background-primary, 0.9);
                box-shadow: 0 6px 16px rgba($label-primary, 0.1),
                    0 0 0 6px rgba($label-primary, 0.03);
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -8px;
                    left: -8px;
                    right: -8px;
                    bottom: -8px;
                    border-radius: 50%;
                    background: rgba($system-orange, 0.1);
                    animation: pulseRing 2s infinite;
                }

                .finish-icon {
                    width: 50px;
                    height: 50px;
                    animation: pulseIcon 1.5s ease-in-out infinite;
                }
            }
        }

        .recording-footer-text {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            bottom: 8px;
            left: 0;
            right: 0;
            font-size: 12px;
            color: $label-secondary;
            img {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
        }
    }

    .voice-button-wrapper {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        transition: opacity 0.3s ease;

        .voice-button {
            width: 80%;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: $system-blue;
            box-shadow: 0 4px 12px rgba($system-blue, 0.2);
            border-radius: 24px;
            cursor: pointer;
            user-select: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
                transition: all 0.3s ease;
                filter: brightness(0) invert(1);
            }

            .voice-status {
                font-size: 16px;
                color: $system-background-primary;
                font-weight: 500;
            }

            &.recording {
                background: color.mix($system-blue, black, $weight: 95%);

                &::before {
                    border-color: $system-blue;
                }
            }

            &:active {
                transform: scale(0.95);
                background: color.mix($system-blue, black, $weight: 90%);
            }
        }
    }

    .initializing-status {
        text-align: center;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 5px;
        gap: 20px;

        .initializing-text {
            color: $label-secondary;
            font-size: 16px;
            font-weight: 500;
            min-width: 60px;
            text-align: center;
            &::after {
                content: '';
                display: inline-block;
                animation: ellipsis 1.5s infinite;
            }
        }
    }
}

@keyframes recording-breath {
    0% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 0.4;
        transform: scale(1.03);
    }
    100% {
        opacity: 0.8;
        transform: scale(1);
    }
}

@keyframes ripple {
    0% {
        opacity: 1;
        transform: scale(0.8);
    }
    100% {
        opacity: 0;
        transform: scale(1.2);
    }
}

@keyframes recording-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes pulseIcon {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.recording-confirm-dialog {
    .confirm-content {
        padding: 20px;
        text-align: center;

        p {
            margin-bottom: 20px;
            font-size: 16px;
            color: #333;
        }

        .confirm-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
        }
    }
}

// 添加录音状态的呼吸动画
@keyframes pulseRecording {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(0.98);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

// 添加旋转动画
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

// 添加声纳波动画
@keyframes sonarWave {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.4;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.8;
    }
}

@keyframes ellipsis {
    0% {
        content: '';
    }
    25% {
        content: '.';
    }
    50% {
        content: '..';
    }
    75% {
        content: '...';
    }
    100% {
        content: '';
    }
}

// 添加提示动画
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate(-50%, -10px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

// 添加新的动画效果
@keyframes pulseText {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.7;
    }
}

@keyframes pulseRing {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 200%;
    }
}
</style>
