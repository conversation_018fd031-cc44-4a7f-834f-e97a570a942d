<template>
    <div class="voice-input-container">
        <!-- 添加 Loading 组件 -->
        <Loading :config="loadingConfig" />

        <!-- 录音状态 - 默认显示 -->
        <div class="recording-status">
            <div class="time-display" v-if="isRecordingStarted">
                <WaveAnimation v-if="!isMaxDurationReached" direction="left" :waveCount="10" />

                <div class="max-duration-tip" v-if="isMaxDurationReached">
                    已达到最大录制时长 {{ formatDuration(maxRecordTime) }}
                </div>

                <span
                    v-if="!isMaxDurationReached"
                    class="time-text"
                    :class="{
                        'countdown-warning': isCountingDown && !showConfirmIcon,
                        'final-duration': showConfirmIcon,
                    }"
                >
                    {{ getTimeDisplay }}
                </span>

                <WaveAnimation v-if="!isMaxDurationReached" direction="right" :waveCount="10" />
            </div>

            <!-- 录音初始化状态显示 -->
            <div class="initializing-status" v-else>
                <span class="initializing-text">录音准备中</span>
            </div>

            <div class="button-group">
                <div class="button-wrapper">
                    <div class="close-button" @click="handleCloseRecording">
                        <i class="pi pi-times"></i>
                    </div>
                    <span class="button-text">取消录音</span>
                </div>

                <div class="button-wrapper" v-if="isRecording">
                    <div class="transcribe-button" @click="handleTranscribeVoice">
                        <img
                            src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/translateVoice.png"
                            alt="转文字"
                        />
                    </div>
                    <span class="button-text">转文字</span>
                </div>
            </div>

            <!-- 录音状态图标 -->
            <div class="recording-icon-status" v-if="isRecording">
                <template v-if="!showConfirmIcon">
                    <div class="button-wrapper">
                        <div class="finish-button" @click="handleShowConfirm">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/stopRecord2.png"
                                alt="结束录音"
                                class="finish-icon"
                            />
                        </div>
                        <span class="button-text">点击结束</span>
                    </div>
                </template>
                <template v-else>
                    <div class="button-wrapper">
                        <div class="confirm-button" @click="handleUseRecording">
                            <img
                                src="https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/choose.png"
                                alt="使用"
                                class="confirm-icon"
                            />
                        </div>
                        <span class="button-text">确认使用</span>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted, computed } from 'vue';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import WaveAnimation from '@/components/common/Wave.vue';
import Loading from '@/components/Loading.vue';
import audioManager from '@/utils/audioManager';
import * as dd from 'dingtalk-jsapi';

// 注册 duration 插件
dayjs.extend(duration);

// 定义属性
const props = defineProps({
    // 是否自动进行转写
    autoTranscribe: {
        type: Boolean,
        default: false
    },
    // 最大录音时长
    maxDuration: {
        type: Number,
        default: 100
    }
});

// 定义事件
const emit = defineEmits(['recording-complete', 'transcript-complete', 'close']);

// 响应式状态
const isRecording = ref(false);
const recordTimer = ref(null);
const recordStartTime = ref(0);
const recordDuration = ref(0);
const maxRecordTime = props.maxDuration * 1000;
const showConfirmIcon = ref(false);
const currentRecording = ref(null);
const COUNTDOWN_THRESHOLD = 10000; // 10秒开始倒计时

// 录音是否真正开始的状态
const isRecordingStarted = ref(false);

// 添加最大时长到达状态
const isMaxDurationReached = computed(() => {
    return recordDuration.value >= maxRecordTime;
});

// 加载状态配置
const loadingConfig = ref({
    visible: false,
    status: 'loading', // loading, loaded, failed
    message: ''
});

// 显示加载状态
const showLoading = (message, status = 'loading') => {
    loadingConfig.value.visible = true;
    loadingConfig.value.status = status;
    loadingConfig.value.message = message;
};

// 隐藏加载状态
const hideLoading = () => {
    loadingConfig.value.visible = false;
};

// 添加统一的错误处理函数
const handleError = (errorMessage, shouldClose = true) => {
    console.error('语音输入错误:', errorMessage);
    showLoading(errorMessage, 'failed');

    // 设置自动关闭机制，防止阻塞流程
    setTimeout(() => {
        hideLoading();
        resetRecordingState();
        if (shouldClose) {
            // 确保面板能被关闭，不阻塞用户操作
            emit('close');
        }
    }, 2000);
};

// 格式化时间函数
const formatDuration = (ms) => {
    const durationObj = dayjs.duration(ms);
    return durationObj.format('m:ss');
};

// 格式化时间显示的计算属性
const getTimeDisplay = computed(() => {
    // 当倒计时时显示剩余时间
    if (isCountingDown.value) {
        const remainingTime = maxRecordTime - recordDuration.value;
        return `还剩 ${formatDuration(remainingTime)}`;
    }

    // 默认显示当前录音时长
    return formatDuration(recordDuration.value);
});

// 计时器逻辑
const startRecordTimer = () => {
    recordStartTime.value = Date.now();
    recordDuration.value = 0; // 确保在开始新计时器时重置录音时长
    recordTimer.value = setInterval(() => {
        recordDuration.value = Date.now() - recordStartTime.value;
        // 检查是否需要自动结束录音
        if (recordDuration.value >= maxRecordTime) {
            handleShowConfirm();
            // 触发振动提示
            dd.vibrate({
                duration: 300
            });
        }
    }, 100);
};

// 开始录音
const startRecording = async () => {
    if (isRecording.value) {return;}

    isRecording.value = true;
    isRecordingStarted.value = false; // 重置录音开始状态
    recordDuration.value = 0; // 确保在开始新录音前重置录音时长

    // 在开始录音前暂停所有正在播放的音频
    audioManager.stopAll();

    try {
        await dd.device.audio.startRecord({
            maxDuration: props.maxDuration + 2, // 多加2秒，防止录音时间不够
            onSuccess: () => {
                isRecordingStarted.value = true; // 录音真正开始
                startRecordTimer();
            },
            onFail: (err) => {
                console.error('录音启动失败:', err);

                // 尝试强制停止当前录音进程
                dd.device.audio.stopRecord({
                    onSuccess: () => {
                        console.log('强制停止录音成功');
                    },
                    onFail: () => {
                        console.error('强制停止录音失败');
                    }
                });

                // 使用统一错误处理
                if (Number(err.errorCode) === 3) {
                    handleError('录音进程正在运行，请重试');
                } else {
                    handleError('无法开始录音，请检查钉钉麦克风权限');
                }
                resetRecordingState();
            }
        });
    } catch (error) {
        console.error('录音初始化失败:', error);
        handleError('录音初始化失败，请重试');
        resetRecordingState();
    }
};

// 计算倒计时状态
const isCountingDown = computed(() => {
    return recordDuration.value >= maxRecordTime - COUNTDOWN_THRESHOLD;
});

// 生命周期钩子
onBeforeUnmount(() => {
    // 清理计时器
    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    // 停止录音
    if (isRecording.value) {
        dd.device.audio.stopRecord({
            onSuccess: () => {
                console.log('组件卸载时录音已停止');
            },
            onFail: (err) => {
                console.error('组件卸载时停止录音失败:', err);
            }
        });
    }

    // 确保重置所有状态
    completeReset();
});

// 显示确认图标
const handleShowConfirm = () => {
    // 暂停计时器
    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    // 停止录音并保存
    dd.device.audio.stopRecord({
        onSuccess: (res) => {
            currentRecording.value = res;
            showConfirmIcon.value = true;
        },
        onFail: (err) => {
            console.error('录音停止失败:', err);
            handleError('录音保存失败，请重试');
            resetRecordingState();
        }
    });
};

// 处理语音转文字
const handleTranscribeVoice = () => {
    if (!isRecording.value) {return;}

    try {
        // 显示加载状态
        showLoading('转文字中...', 'loading');

        // 如果已经有录音，直接转写
        if (currentRecording.value) {
            dd.device.audio.translateVoice({
                mediaId: currentRecording.value.mediaId,
                duration: currentRecording.value.duration,
                onSuccess: (result) => {
                    hideLoading();
                    // 直接将转写结果传递给父组件
                    emit('transcript-complete', result.content);
                    // 重置录音状态
                    resetRecordingState();
                },
                onFail: (err) => {
                    console.error('语音转文字失败:', err);
                    handleError('语音转文字失败，请重试', false);
                    // 转写失败不自动关闭，给用户选择机会
                }
            });
            return;
        }

        // 如果没有录音，暂停计时器
        if (recordTimer.value) {
            clearInterval(recordTimer.value);
            recordTimer.value = null;
        }

        // 停止录音并转写
        dd.device.audio.stopRecord({
            onSuccess: (res) => {
                if (!res) {
                    handleError('录音数据丢失');
                    return;
                }
                // 调用语音转文字API
                dd.device.audio.translateVoice({
                    mediaId: res.mediaId,
                    duration: res.duration,
                    onSuccess: (result) => {
                        hideLoading();
                        // 直接将转写结果传递给父组件
                        emit('transcript-complete', result.content);
                        // 重置录音状态
                        resetRecordingState();
                    },
                    onFail: (err) => {
                        console.error('语音转文字失败:', err);
                        handleError('语音转文字失败', false);
                    }
                });
            },
            onFail: (err) => {
                console.error('录音停止失败:', err);
                handleError('录音停止失败，请重试');
            }
        });
    } catch (error) {
        console.error('语音转文字失败:', error);
        handleError('语音转文字失败', false);
    }
};

// 《确认使用》按钮
const handleUseRecording = () => {
    if (currentRecording.value) {
        // 根据props判断是否需要自动转写
        if (props.autoTranscribe) {
            showLoading('转文字中...', 'loading');
            // 调用语音转文字API
            dd.device.audio.translateVoice({
                mediaId: currentRecording.value.mediaId,
                duration: currentRecording.value.duration,
                onSuccess: (result) => {
                    hideLoading();
                    // 将转写结果传递给父组件
                    emit('transcript-complete', result.content);
                    resetRecordingState();
                },
                onFail: (err) => {
                    console.error('语音转文字失败:', err);
                    // 转写失败，改为发送原始录音
                    showLoading('转写失败，发送原始录音', 'failed');
                    setTimeout(() => {
                        hideLoading();
                        emit('recording-complete', currentRecording.value);
                        resetRecordingState();
                    }, 1500);
                }
            });
        } else {
            // 不需要转写，直接发送录音数据
            emit('recording-complete', currentRecording.value);
            resetRecordingState();
        }
    } else {
        handleError('录音数据丢失，请重试', 'failed');
        resetRecordingState();
    }
};

// 重置录音状态
const resetRecordingState = () => {
    // 如果当前正在录音，需要先停止钉钉录音
    if (isRecording.value && isRecordingStarted.value) {
        dd.device.audio.stopRecord({
            onSuccess: () => {
                console.log('录音已停止');
                completeReset();
            },
            onFail: (err) => {
                console.error('停止录音失败:', err);
                // 即使停止失败也要完成重置，不能阻塞流程
                completeReset();
            }
        });
    } else {
        completeReset();
    }
};

// 完成重置的辅助函数
const completeReset = () => {
    isRecording.value = false;
    isRecordingStarted.value = false;
    recordDuration.value = 0;
    currentRecording.value = null;
    showConfirmIcon.value = false;

    if (recordTimer.value) {
        clearInterval(recordTimer.value);
        recordTimer.value = null;
    }

    // 确保隐藏loading状态
    hideLoading();
};

// 关闭录音面板
const handleCloseRecording = () => {
    // 确保隐藏loading状态
    hideLoading();
    resetRecordingState();
    emit('close');
};

// 组件挂载时预加载图片资源并自动开始录音
onMounted(() => {
    // 预加载图片资源
    const preloadImages = [
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/microphone.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/theme.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/stopRecord2.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/recordFinish.png',
        'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/choose.png'
    ];

    preloadImages.forEach((src) => {
        const img = new Image();
        img.src = src;
    });

    // 组件挂载后自动开始录音
    startRecording();
});
</script>

<style lang="scss" scoped>
@use 'sass:color';

.voice-input-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;

    .recording-status {
        padding-top: 20px;
        height: 240px;
        width: 100%;
        background: linear-gradient(
            180deg,
            rgba($system-background-secondary, 0.95) 0%,
            rgba($system-background-primary, 0.98) 100%
        );
        backdrop-filter: blur(10px);
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        transform: translateY(0);
        display: flex;
        flex-direction: column;
        border-top: 1px solid rgba($system-blue, 0.12);
        box-shadow: 0 -4px 20px rgba($label-primary, 0.05);
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(
                90deg,
                transparent,
                rgba($system-background-primary, 0.8),
                transparent
            );
        }

        .time-display {
            position: relative;
            z-index: 1;
            text-align: center;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 5px;

            .time-text {
                position: relative;
                color: $label-primary;
                font-weight: 500;
                min-width: 60px;
                text-align: center;
                font-size: 16px;
                letter-spacing: 0.5px;
                text-shadow: 0 1px 0 rgba($system-background-primary, 0.8);

                &.countdown-warning {
                    color: $system-red;
                    animation: pulseText 1s infinite alternate;
                }

                &.final-duration {
                    color: $system-blue;
                    font-weight: 500;
                }
            }

            .max-duration-tip {
                position: absolute;
                top: 0px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba($system-red, 0.1);
                color: $system-red;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 500;
                white-space: nowrap;
                animation: fadeInDown 0.3s ease-out;
                box-shadow: 0 2px 8px rgba($system-red, 0.15);
                backdrop-filter: blur(4px);
            }
        }

        .button-group {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 0 60px;
            margin-top: 20px;
        }

        .button-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .button-text {
                font-size: 12px;
                color: $label-secondary;
                text-align: center;
                font-weight: 500;
                letter-spacing: 0.3px;
                margin-top: 2px;
            }
        }

        .transcribe-button,
        .close-button,
        .start-button {
            width: 52px;
            height: 52px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            user-select: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

            i {
                font-size: 22px;
                transition: all 0.3s ease;
            }
        }

        .start-button {
            background: linear-gradient(
                135deg,
                $system-green 0%,
                color.mix($system-green, black, $weight: 90%) 100%
            );
            border: 1px solid rgba($system-green, 0.1);

            i {
                color: #fff;
            }
        }

        .transcribe-button {
            background: linear-gradient(
                135deg,
                $system-green 0%,
                color.mix($system-green, black, $weight: 90%) 100%
            );
            border: 1px solid rgba($system-green, 0.1);
            img {
                width: 30px;
                height: 30px;
            }
        }

        .close-button {
            background: linear-gradient(135deg, $system-gray7 0%, $system-gray6 100%);
            border: 1px solid rgba($system-red, 0.1);

            i {
                color: $system-red;
            }

            &:active {
                background: linear-gradient(
                    135deg,
                    $system-red 0%,
                    color.mix($system-red, black, $weight: 90%) 100%
                );
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba($system-red, 0.25);
                border: 1px solid rgba($system-red, 0.2);

                i {
                    color: #fff;
                    transform: scale(1.1);
                }
            }
        }

        // 录音状态图标样式
        .recording-icon-status {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            padding: 10px;
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000;

            // 结束录音按钮样式
            .finish-button,
            .confirm-button {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                will-change: transform;
            }

            .confirm-button {
                background: linear-gradient(
                    135deg,
                    $system-blue 0%,
                    color.mix($system-blue, black, $weight: 90%) 100%
                );
                box-shadow:
                    0 6px 16px rgba($system-blue, 0.25),
                    0 0 0 6px rgba($system-blue, 0.08);
                will-change: transform;
                position: relative;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 50%;
                    height: 100%;
                    background: rgba($system-background-primary, 0.2);
                    transform: skewX(-25deg);
                    animation: shimmer 2s infinite;
                }

                .confirm-icon {
                    width: 30px;
                    height: 30px;
                    filter: brightness(0) invert(1);
                    transform: scale(1);
                    transition: transform 0.3s ease;
                }
            }

            .finish-button {
                background: rgba($system-background-primary, 0.9);
                box-shadow:
                    0 6px 16px rgba($label-primary, 0.1),
                    0 0 0 6px rgba($label-primary, 0.03);
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: -8px;
                    left: -8px;
                    right: -8px;
                    bottom: -8px;
                    border-radius: 50%;
                    background: rgba($system-orange, 0.1);
                    animation: pulseRing 2s infinite;
                }

                .finish-icon {
                    width: 50px;
                    height: 50px;
                    animation: pulseIcon 1.5s ease-in-out infinite;
                }
            }
        }
    }

    .initializing-status {
        text-align: center;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 5px;
        gap: 20px;

        .initializing-text {
            color: $label-secondary;
            font-size: 16px;
            font-weight: 500;
            min-width: 60px;
            text-align: center;
            &::after {
                content: '';
                display: inline-block;
                animation: ellipsis 1.5s infinite;
            }
        }
    }
}

@keyframes recording-breath {
    0% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 0.4;
        transform: scale(1.03);
    }
    100% {
        opacity: 0.8;
        transform: scale(1);
    }
}

@keyframes ripple {
    0% {
        opacity: 1;
        transform: scale(0.8);
    }
    100% {
        opacity: 0;
        transform: scale(1.2);
    }
}

// 添加旋转动画
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 添加提示动画
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate(-50%, -10px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

// 添加新的动画效果
@keyframes pulseText {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.7;
    }
}

@keyframes pulseRing {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 200%;
    }
}

@keyframes pulseIcon {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes ellipsis {
    0% {
        content: '';
    }
    25% {
        content: '.';
    }
    50% {
        content: '..';
    }
    75% {
        content: '...';
    }
    100% {
        content: '';
    }
}
</style>
