import { createRouter, createWebHistory } from 'vue-router';
import { getTopLevelPath } from '@/utils/urls';
import { useTrackingStore } from '@/stores/tracking';
import { useAppStore } from '@/stores/app';
import { initializeDingTalkJsApi } from '@/utils/dingTalkService';
import { isDingTalk } from '@/utils/index';

const routes = [
    // 通用路由
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/Login.vue'),
        meta: { requiresAuth: false }
    },
    {
        path: '/noPermission',
        name: 'NoPermission',
        component: () => import('@/views/NoPermission.vue'),
        meta: { requiresAuth: false }
    },
    {
        path: '/error',
        name: 'Error',
        component: () => import('@/views/Error.vue'),
        meta: { requiresAuth: false }
    },

    // crm物料查看页面
    {
        path: '/',
        name: 'Root',
        redirect: '/smart-crm',
        meta: { requiresAuth: true }
    },
    {
        path: '/crm-product/index.html',
        redirect: '/smart-crm',
        meta: { requiresAuth: true }
    },
    {
        path: '/home',
        redirect: '/smart-crm',
        meta: { requiresAuth: true }
    },
    {
        path: '/smart-crm',
        name: 'SmartCRM',
        component: () => import('@/views/smart-crm/index.vue'),
        redirect: '/smart-crm/home',
        meta: { requiresAuth: true },
        children: [
            {
                path: 'home',
                name: 'CRMHome',
                component: () => import('@/views/smart-crm/pages/crm-home.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'detail',
                name: 'CRMDetail',
                component: () => import('@/views/smart-crm/pages/crm-detail.vue'),
                meta: { requiresAuth: true }
            }
            // 根据需要添加更多CRM相关子路由
        ]
    },

    // 智慧签约
    {
        path: '/smart-sign',
        name: 'SmartSign',
        component: () => import('@/views/smart-sign/index.vue'),
        meta: { requiresAuth: true }
    },

    // 智能问数（已废弃）
    {
        path: '/smart-data',
        name: 'SmartData',
        component: () => import('@/views/smart-data/index.vue'),
        meta: { requiresAuth: true }
    },

    // 智阅值班1
    {
        path: '/duty-list',
        name: 'DutyList',
        redirect: '/smart-duty',
        component: () => import('@/views/smart-duty/index.vue'),
        meta: { requiresAuth: true }
    },

    // 智阅值班2
    {
        path: '/smart-duty',
        name: 'SmartDuty',
        component: () => import('@/views/smart-duty/index.vue'),
        meta: { requiresAuth: true }
    },

    // 智能训练 - 根路由
    {
        path: '/smart-trainer',
        name: 'SmartTrainer',
        meta: { requiresAuth: true },
        children: [
            // 🏠 主布局路由（带底部导航的门户页面）
            {
                path: '',
                component: () => import('@/views/smart-trainer/layouts/MainLayout.vue'),
                redirect: '/smart-trainer/main', // 添加重定向到 main
                children: [
                    // 🆕 四维平台主入口
                    {
                        path: 'main',
                        name: 'TrainerMain',
                        component: () => import('@/views/smart-trainer/pages/main/index.vue'),
                        meta: { requiresAuth: true, title: '智能训练平台' }
                    },

                    // 🆕 学习模块首页
                    {
                        path: 'learn/home',
                        name: 'LearnHome',
                        component: () => import('@/views/smart-trainer/pages/learn/pages/home.vue'),
                        meta: { requiresAuth: true, title: '学习中心' }
                    },

                    // 🆕 练习模块首页
                    {
                        path: 'practice/home',
                        name: 'PracticeHome',
                        component: () => import('@/views/smart-trainer/pages/practice/home.vue'),
                        meta: { requiresAuth: true, title: '练习中心' }
                    },

                    // 🆕 考试列表页（门户页面）
                    {
                        path: 'assess/home',
                        name: 'AssessHome',
                        component: () =>
                            import('@/views/smart-trainer/pages/assess/pages/home.vue'),
                        meta: { requiresAuth: true, title: '考试列表' }
                    },

                    // 🆕 个人中心模块
                    {
                        path: 'profile',
                        name: 'TrainerProfile',
                        component: () => import('@/views/smart-trainer/pages/profile/index.vue'),
                        meta: { requiresAuth: true, title: '个人中心' }
                    }
                ]
            },

            // 🔗 子页面路由（不带底部导航的深层页面）
            {
                path: '',
                component: () => import('@/views/smart-trainer/layouts/SubPageLayout.vue'),
                children: [
                    // 🆕 考试详情页
                    {
                        path: 'assess/detail/:examId',
                        name: 'AssessDetail',
                        component: () =>
                            import('@/views/smart-trainer/pages/assess/pages/detail.vue'),
                        meta: { requiresAuth: true, title: '考试详情' }
                    },

                    // 🆕 考试答题页
                    {
                        path: 'assess/quiz/:examId',
                        name: 'AssessQuiz',
                        component: () =>
                            import('@/views/smart-trainer/pages/assess/pages/quiz.vue'),
                        meta: { requiresAuth: true, title: '考试答题' }
                    },

                    // 🆕 考试结果页
                    {
                        path: 'assess/result/:examId',
                        name: 'AssessResult',
                        component: () =>
                            import('@/views/smart-trainer/pages/assess/pages/result.vue'),
                        meta: { requiresAuth: true, title: '考试结果' }
                    },

                    // 🆕 练习排行榜页
                    {
                        path: 'practice/rank',
                        name: 'PracticeRank',
                        component: () => import('@/views/smart-trainer/pages/practice/rank.vue'),
                        meta: { requiresAuth: true, title: '练习排行榜' }
                    },

                    // 🆕 我的练习页
                    {
                        path: 'practice/my',
                        name: 'PracticeMy',
                        component: () => import('@/views/smart-trainer/pages/practice/my.vue'),
                        meta: { requiresAuth: true, title: '我的练习' }
                    },

                    // 🆕 应用模块首页
                    {
                        path: 'apply/home',
                        name: 'ApplyHome',
                        component: () => import('@/views/smart-trainer/pages/apply/pages/home.vue'),
                        meta: { requiresAuth: true, title: '应用中心' }
                    },

                    // 📦 保持现有考试路由（深层页面）
                    {
                        path: 'exam/detail',
                        name: 'ExamDetail',
                        component: () => import('@/views/smart-trainer/pages/exam/detail.vue'),
                        meta: { requiresAuth: true, title: '考试详情' }
                    },
                    {
                        path: 'exam/exam',
                        name: 'Exam',
                        component: () => import('@/views/smart-trainer/pages/exam/exam.vue'),
                        meta: { requiresAuth: true, title: '考试答题' }
                    },
                    {
                        path: 'exam/result',
                        name: 'ExamResult',
                        component: () => import('@/views/smart-trainer/pages/exam/result.vue'),
                        meta: { requiresAuth: true, title: '考试结果' }
                    },
                    {
                        path: 'exam/history',
                        name: 'ExamHistory',
                        component: () => import('@/views/smart-trainer/pages/exam/history.vue'),
                        meta: { requiresAuth: true, title: '考试历史' }
                    }
                ]
            },

            // 🔄 路由重定向和兼容性处理
            {
                path: 'learn',
                redirect: '/smart-trainer/learn/home'
            },
            {
                path: 'practice',
                redirect: '/smart-trainer/practice/home'
            },
            {
                path: 'assess',
                redirect: '/smart-trainer/assess/home'
            },
            {
                path: 'assess/list',
                redirect: '/smart-trainer/assess/home'
            },
            {
                path: 'apply',
                redirect: '/smart-trainer/apply/home'
            }
        ]
    },

    // 智能问答 - 家家精灵主页面
    {
        path: '/smart-chat',
        name: 'SmartChat',
        component: () => import('@/views/smart-chat/index.vue'),
        redirect: '/smart-chat/home',
        meta: { requiresAuth: true },
        children: [
            {
                path: 'home',
                name: 'SmartChatHome',
                component: () => import('@/views/smart-chat/pages/home.vue'),
                meta: { requiresAuth: true }
            },
            {
                path: 'chat-bot',
                name: 'SmartChatBot',
                component: () => import('@/views/smart-chat/pages/chat-bot.vue'),
                meta: { requiresAuth: true }
            }
        ]
    },

    // 智慧执行
    {
        path: '/smart-execution',
        name: 'SmartExecution',
        component: () => import('@/views/smart-execution/index.vue'),
        meta: { requiresAuth: true }
    },

    // 智慧打卡
    {
        path: '/smart-clock',
        name: 'SmartClock',
        component: () => import('@/views/smart-clock/index.vue'),
        meta: { requiresAuth: true }
    },

    // 错误处理演示
    {
        path: '/error-handler-demo',
        name: 'ErrorHandlerDemo',
        component: () => import('@/demo/ErrorHandlerDemo.vue'),
        meta: { requiresAuth: true }
    },

    // 动态表单
    {
        path: '/dynamic-form-demo',
        name: 'DynamicFormDemo',
        component: () => import('@/views/dynamic-form-demo/index.vue'),
        meta: { requiresAuth: true }
    }
];
const router = createRouter({
    history: createWebHistory(),
    routes
});

let lastTopPath = null;

router.afterEach(to => {
    const currentTopPath = getTopLevelPath(to.path);
    // 判断是否需要发送统计
    if (currentTopPath !== lastTopPath) {
        sendPV(currentTopPath, to);
        lastTopPath = currentTopPath;
    }
});

const sendPV = async (path, route) => {
    console.log('🚗router.afterEach:', path, route);

    // 跳过不需要统计的页面
    const skipPaths = ['/login', '/error', '/noPermission'];
    if (skipPaths.includes(path)) {
        return;
    }

    const trackingStore = useTrackingStore();
    const appStore = useAppStore();
    const res = await appStore.checkIfLogin();

    if (!res) {
        console.log('用户未登录，跳过PV统计');
        return;
    }

    // 确保用户已登录再发送PV统计
    if (Number(res.returncode) === 0) {
        try {
            // 发送 jsAPi 鉴权的逻辑也在这里做
            console.log('👌此时页面的路径为：' + window.location.href);
            // 检查当前URL是否已被重定向到登录页面
            const currentUrl = window.location.href;
            if (currentUrl.includes('/login')) {
                console.log('检测到当前页面已重定向到登录页，跳过JSAPI初始化');
                return;
            }

            if (isDingTalk()) {
                await initializeDingTalkJsApi();
            }

            // 发送PV统计
            const eventType = 'PV_' + path.replace(/^\//, '');
            console.log('👋成功发送PV统计:', eventType);
            await trackingStore.reportUserEvent({
                eventType: eventType,
                eventDetail: route?.fullPath || path
            });
        } catch (error) {
            console.error('PV统计发送失败:', error);
        }
    } else {
        console.log('用户未登录，跳过PV统计');
    }
};

export default router;
