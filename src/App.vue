<script setup>
import Toast from 'primevue/toast';
import { ref, onUnmounted } from 'vue';

import DebugTools from '@/components/common/DebugTools.vue';
const debugTools = ref(null);

// 添加快捷键支持
const handleKeyDown = event => {
    // 按下 Ctrl+Shift+D 打开调试工具
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        debugTools.value?.show();
        event.preventDefault();
    }
};

// 在开发和测试环境中添加快捷键支持
if (import.meta.env.MODE !== 'production') {
    window.addEventListener('keydown', handleKeyDown);

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
        window.removeEventListener('keydown', handleKeyDown);
    });
}
</script>

<template>
    <Toast />
    <router-view v-slot="{ Component, route }">
        <keep-alive include="Home">
            <component :is="Component" :key="route.fullPath" />
        </keep-alive>
    </router-view>
    <DebugTools ref="debugTools" />
</template>

<style scoped lang="scss"></style>
