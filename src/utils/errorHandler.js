/**
 * 全局错误处理服务
 * 处理未捕获的Promise拒绝和JavaScript错误
 */

import { logWarn } from '@/services/ftwoService.js';

// 错误收集队列
let errorQueue = [];
let isReporting = false;

// 错误类型枚举
const ERROR_TYPES = {
    UNHANDLED_REJECTION: 'unhandledrejection',
    JAVASCRIPT_ERROR: 'javascript_error',
    VUE_ERROR: 'vue_error',
    RESOURCE_ERROR: 'resource_error'
};

/**
 * 错误信息标准化
 * @param {Error|any} error - 错误对象
 * @param {string} type - 错误类型
 * @param {Object} context - 上下文信息
 * @returns {Object} 标准化的错误信息
 */
function normalizeError(error, type, context = {}) {
    const timestamp = Date.now();
    const url = window.location.href;
    const userAgent = navigator.userAgent;

    // 提取错误信息
    let message = '';
    let stack = '';
    let name = '';

    if (error instanceof Error) {
        message = error.message;
        stack = error.stack;
        name = error.name;
    } else if (typeof error === 'string') {
        message = error;
    } else if (error && typeof error === 'object') {
        message = error.message || error.reason || JSON.stringify(error);
        stack = error.stack || '';
        name = error.name || '';
    } else {
        message = String(error);
    }

    return {
        type,
        message,
        stack,
        name,
        timestamp,
        url,
        userAgent,
        context,
        // 添加环境信息
        environment: {
            mode: import.meta.env.MODE,
            isDingTalk: window.dd !== undefined,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        }
    };
}

/**
 * 错误上报（使用 ftwo 服务进行错误记录）
 * @param {Object} errorInfo - 标准化的错误信息
 */
function reportError(errorInfo) {
    try {
        // 使用 logWarn 进行错误上报
        logWarn(`🚨 ${errorInfo.type} 错误: ${errorInfo.message}`, errorInfo, {
            stack: errorInfo.stack,
            url: errorInfo.url,
            timestamp: errorInfo.timestamp
        });

        // 同时在开发环境下保留控制台输出
        if (import.meta.env.DEV) {
            console.group(`🚨 ${errorInfo.type} 错误`);
            console.error('错误信息:', errorInfo.message);
            console.error('错误堆栈:', errorInfo.stack);
            console.error('完整信息:', errorInfo);
            console.groupEnd();
        }
    } catch (reportingError) {
        console.error('错误上报失败:', reportingError);
        // 备用方案：如果 ftwo 上报失败，至少在控制台记录
        console.error('原始错误信息:', errorInfo);
    }
}

/**
 * 批量处理错误队列
 */
async function processErrorQueue() {
    if (isReporting || errorQueue.length === 0) {
        return;
    }

    isReporting = true;
    const errors = [...errorQueue];
    errorQueue = [];

    try {
        // 批量处理错误
        await Promise.allSettled(errors.map(error => reportError(error)));
    } catch (error) {
        console.error('批量处理错误失败:', error);
    } finally {
        isReporting = false;
    }
}

/**
 * 添加错误到队列
 * @param {Object} errorInfo - 错误信息
 */
function queueError(errorInfo) {
    errorQueue.push(errorInfo);

    // 立即处理或延迟处理
    if (errorQueue.length === 1) {
        // 第一个错误立即处理
        setTimeout(processErrorQueue, 0);
    } else if (errorQueue.length >= 10) {
        // 错误过多，立即批量处理
        processErrorQueue();
    } else {
        // 延迟处理，避免频繁上报
        setTimeout(processErrorQueue, 1000);
    }
}

/**
 * 处理未捕获的Promise拒绝
 * @param {PromiseRejectionEvent} event - Promise拒绝事件
 */
function handleUnhandledRejection(event) {
    const errorInfo = normalizeError(event.reason, ERROR_TYPES.UNHANDLED_REJECTION, {
        promise: event.promise,
        handled: false
    });

    queueError(errorInfo);

    // // 阻止默认的控制台警告
    // event.preventDefault();
}

/**
 * 处理JavaScript错误
 * @param {ErrorEvent} event - 错误事件
 */
function handleJavaScriptError(event) {
    const errorInfo = normalizeError(
        event.error || new Error(event.message),
        ERROR_TYPES.JAVASCRIPT_ERROR,
        {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        }
    );

    queueError(errorInfo);
}

/**
 * 处理资源加载错误
 * @param {Event} event - 错误事件
 */
function handleResourceError(event) {
    const target = event.target;
    let resourceType = 'unknown';
    let resourceUrl = '';

    if (target.tagName) {
        resourceType = target.tagName.toLowerCase();
        resourceUrl = target.src || target.href || '';
    }

    const errorInfo = normalizeError(`资源加载失败: ${resourceUrl}`, ERROR_TYPES.RESOURCE_ERROR, {
        resourceType,
        resourceUrl,
        target: target.outerHTML ? target.outerHTML.substring(0, 200) : ''
    });

    queueError(errorInfo);
}

/**
 * Vue错误处理器
 * @param {Error} error - Vue错误
 * @param {Object} instance - Vue组件实例
 * @param {string} info - 错误信息
 */
function handleVueError(error, instance, info) {
    const errorInfo = normalizeError(error, ERROR_TYPES.VUE_ERROR, {
        componentName: instance?.$options?.name || instance?.$options?.__file || 'Unknown',
        info,
        props: instance?.$props,
        route: instance?.$route?.path
    });

    queueError(errorInfo);
}

/**
 * 初始化全局错误处理
 * @param {Object} app - Vue应用实例
 */
export function initErrorHandler(app) {
    // 1. 处理未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // 2. 处理JavaScript错误
    window.addEventListener('error', handleJavaScriptError);

    // 3. 处理资源加载错误（捕获阶段）
    window.addEventListener('error', handleResourceError, true);

    // 4. Vue全局错误处理
    if (app) {
        app.config.errorHandler = handleVueError;
    }
}

/**
 * 手动上报错误
 * @param {Error|any} error - 错误对象
 * @param {Object} context - 上下文信息
 */
export function captureError(error, context = {}) {
    const errorInfo = normalizeError(error, 'manual_capture', context);
    queueError(errorInfo);
}

/**
 * 销毁错误处理器
 */
export function destroyErrorHandler() {
    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    window.removeEventListener('error', handleJavaScriptError);
    window.removeEventListener('error', handleResourceError, true);

    // 清空错误队列
    errorQueue = [];
    isReporting = false;
}
