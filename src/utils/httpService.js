import router from '@/router/index.js';
import { useAppStore } from '@/stores/app';
import { captureError } from './errorHandler';

// 存储离线时的请求队列
let offlineRequests = [];
// 网络状态监听标志
let isListeningNetwork = false;

// 初始化网络状态监听
function initNetworkListener() {
    if (isListeningNetwork) {
        return;
    }

    isListeningNetwork = true;

    // 监听网络状态变化
    window.addEventListener('online', handleNetworkChange);
    window.addEventListener('offline', handleNetworkChange);
}

// 处理网络状态变化
function handleNetworkChange(event) {
    if (event.type === 'online' && offlineRequests.length > 0) {
        console.log('网络已恢复，开始重试请求...');
        retryOfflineRequests();
    }
}

// 重试离线请求
async function retryOfflineRequests() {
    const requestsToRetry = [...offlineRequests];
    offlineRequests = [];

    // 使用 Promise.allSettled 来并发处理所有请求
    const results = await Promise.allSettled(
        requestsToRetry.map(async requestOptions => {
            try {
                console.log('重试请求:', requestOptions.url);
                return await request(requestOptions, false); // 不再添加到离线队列中
            } catch (error) {
                console.error('重试请求失败:', error);
                throw error;
            }
        })
    );

    // 可选：处理结果
    results.forEach((result, index) => {
        if (result.status === 'rejected') {
            console.error(`重试请求失败 (${requestsToRetry[index].url}):`, result.reason);
        }
    });
}

function get(url, params = null, headers = {}) {
    const options = {
        url,
        method: 'GET',
        params,
        headers
    };
    return request(options);
}

function post(url, body = {}, params = null, headers = {}) {
    const isFormData = body instanceof FormData;
    const shouldAddJsonHeader = !isFormData;
    const options = {
        url,
        method: 'POST',
        body: body,
        params,
        headers: {
            ...(shouldAddJsonHeader && {
                'Content-Type': 'application/json'
            }),
            ...headers
        }
    };
    return request(options);
}

function put(url, body = {}, headers = {}) {
    const options = {
        url,
        method: 'PUT',
        body,
        headers
    };
    return request(options);
}

function deleteRequest(url, body = {}, headers = {}) {
    const options = {
        url,
        method: 'DELETE',
        body,
        headers
    };
    return request(options);
}

async function request(options, addToOfflineQueue = true) {
    // 初始化网络监听
    initNetworkListener();

    const appStore = useAppStore();

    // 检查网络状态
    if (!navigator.onLine && addToOfflineQueue) {
        console.log('网络离线，请求已加入队列:', options.url);
        offlineRequests.push(options);
        throw new Error('网络连接已断开，请求将在网络恢复后自动重试');
    }

    let _url = options.url;

    // 添加appId参数
    const accountParam = '_appId=aiwen';
    const delimiter = _url.includes('?') ? '&' : '?';
    _url = _url + delimiter + accountParam;

    // 设置请求头
    const _options = {
        method: options.method || 'GET',
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        credentials: 'include' // 添加这一行
    };

    // 添加参数
    if (options.params) {
        _url += '&' + new URLSearchParams(options.params).toString();
    }

    if (options.body) {
        _options.body =
            options.body instanceof FormData ? options.body : JSON.stringify(options.body);
    }

    // 发送请求
    try {
        const response = await fetch(_url, _options);
        const data = await response.json();

        if (data.returncode === 0 || data.ReturnCode === 0) {
            // 请求成功，确保登录状态为true
            appStore.isLoggedIn = true;
            return data;
        } else if (data.returncode === 401 || data.ReturnCode === 401) {
            // 未授权，清除登录状态
            appStore.isLoggedIn = false;

            // 避免在登录页面重复重定向
            const currentRoute = router.currentRoute.value;
            if (currentRoute.path !== '/login') {
                const currentPath = currentRoute.fullPath;
                console.log('检测到401错误，重定向到登录页');

                // 使用router进行重定向，避免页面刷新
                await router.push({
                    path: '/login',
                    query: { redirect: currentPath }
                });
                return;
            }
            // 返回一个rejected Promise，让调用方知道请求失败
            throw new Error('用户未登录或登录已过期');
        } else if (data.returncode === 403 || data.ReturnCode === 403) {
            console.log('检测到403错误，跳转到错误页面');
            await router.push({
                path: '/error',
                query: { errorMessage: data.message || data.Message || '访问被拒绝' }
            });
            throw new Error('访问被拒绝');
        } else {
            return data;
        }
    } catch (err) {
        console.error('request error:', err.message);

        // 捕获HTTP请求错误到全局错误处理
        captureError(err, {
            url: _url,
            method: options.method,
            requestOptions: options,
            isNetworkError: err instanceof TypeError && err.message.includes('network'),
            timestamp: Date.now()
        });

        // 如果是网络错误且需要添加到离线队列
        if (err instanceof TypeError && err.message.includes('network') && addToOfflineQueue) {
            console.log('网络请求失败，已加入重试队列:', options.url);
            offlineRequests.push(options);
            throw new Error('网络请求失败，将在网络恢复后自动重试');
        }

        throw err;
    }
}

export default {
    get,
    post,
    put,
    delete: deleteRequest // 注意：delete 是保留字，需要别名导出
};
