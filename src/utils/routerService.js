import { inject } from 'vue';
import { useRouter } from 'vue-router';

/**
 * 通用路由服务
 * 自动判断当前环境并使用合适的路由方式
 */
export function useRouterService() {
    const router = useRouter();
    // 注入面板上下文
    const panelContext = inject('materialPanelContext', null);

    // 如果注入了面板上下文，则为面板内嵌环境
    const isInPanel = !!panelContext;

    /**
     * 导航到指定页面
     * @param {string} path - 目标路径
     * @param {Object} params - 参数对象
     */
    const navigateTo = (path, params = {}) => {
        if (isInPanel && panelContext.navigateTo) {
            // 在面板内使用面板路由系统
            panelContext.navigateTo(path, params);
        } else {
            // 独立页面使用Vue Router
            router.push({
                path: `${path}`,
                query: params
            });
        }
    };

    /**
     * 返回上一页
     */
    const goBack = () => {
        if (isInPanel && panelContext.goBack) {
            // 在面板内使用面板的返回方法
            panelContext.goBack();
        } else {
            // 独立页面使用浏览器返回
            router.back();
        }
    };

    return {
        navigateTo,
        goBack,
        isInPanel
    };
}
