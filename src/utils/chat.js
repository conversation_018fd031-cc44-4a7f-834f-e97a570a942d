// 兼容时间示例(2017-09-05T13:08:56.080,2017-09-05T13:08:56,2017-09-05)
import dayjs from 'dayjs';
import copy from 'copy-to-clipboard';
import markdownToTxt from 'markdown-to-txt';

// Copy to clipboard
export const copyToClipboard = function (input, type) {
    let result = null;
    if (type === 'text') {
        result = markdownToTxt(input);
    } else {
        result = input;
    }
    copy(result);
    return result;
};

export const formatDate = function (dateString) {
    if (!dateString) {
        return '-';
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

/**
 * markdown-it : md文档渲染组件
 */
import markdownit from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css'; // 引入 highlight.js 的样式文件
import '@/style/highlight-default.min.css'; // 本地引入default样式，避免从CDN加载

export const md = new markdownit({
    html: true, // 允许HTML标签
    linkify: true, // 自动转换链接
    typographer: true, // 启用排版增强（引号、破折号等）
    breaks: true, // 转换换行符为<br>
    highlight: function (str, lang) {
        let code = '';
        if (lang && hljs.getLanguage(lang)) {
            try {
                code =
                    '<pre><code class="hljs">' +
                    hljs.highlight(str, {
                        language: lang,
                        ignoreIllegals: true
                    }).value +
                    '</code></pre>';
            } catch (err) {
                console.log(err);
            }
        } else {
            code = '<pre><code class="hljs">' + md.utils.escapeHtml(str) + '</code></pre>';
        }

        return code;
    }
});

/**
 * 用于修复GPT4-turbo, markdown格式嵌套错误
 * 见issue : https://git.corpautohome.com/dealer-arch/microfrontends-ai/root-config/issues/218
 */
export const fixMarkdownCodeBlocks = function (markdownString) {
    let result = markdownString;
    const regex = /```markdown[\s\S]*?(?:```[\s\S]*?```[\s\S]*?)*```/gm;
    if (regex.test(markdownString)) {
        result = markdownString.replace(/^```markdown/, '````markdown').replace(/```$/, '````');
    }
    return result;
};

// 可能会返回图片，需要等图片全部加载完成后再执行置底的操作。
export const scrollToBottom = function (selector) {
    const element = document.querySelector(selector);
    if (!element) {
        return;
    }

    const images = element.getElementsByTagName('img');
    let loadedImagesCount = 0;

    if (images.length === 0) {
        callback();
        return;
    }

    for (const img of images) {
        if (img.complete) {
            loadedImagesCount++;
        } else {
            img.onload = () => {
                loadedImagesCount++;
                if (loadedImagesCount === images.length) {
                    callback();
                }
            };
            img.onerror = () => {
                loadedImagesCount++;
                if (loadedImagesCount === images.length) {
                    callback();
                }
            };
        }
    }

    if (loadedImagesCount === images.length) {
        callback();
    }

    function callback() {
        // 使用 requestAnimationFrame 确保在下一帧渲染时执行滚动
        requestAnimationFrame(() => {
            element.scrollTop = element.scrollHeight;
        });
    }
};
