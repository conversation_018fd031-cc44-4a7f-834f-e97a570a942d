/**
 * 音频管理器 - 确保同一时间只有一个音频在播放
 * 这种实现方式具有以下优点：
 * 松耦合：音频组件不需要直接相互通信
 * 可扩展：可以轻松添加更多类型的音频组件
 * 可靠：确保在任何时候只有一个音频在播放
 * 用户体验良好：避免了多个音频同时播放造成的混乱
 */
import mitt from 'mitt';

// 创建事件发射器
const emitter = mitt();

// 当前播放的音频ID
let currentPlayingId = null;

const audioManager = {
    /**
     * 注册音频播放
     * @param {string} audioId - 音频唯一标识
     * @returns {boolean} - 是否可以播放
     */
    registerPlay(audioId) {
        // 如果有其他音频正在播放，发出停止信号
        if (currentPlayingId && currentPlayingId !== audioId) {
            emitter.emit('stop-audio', currentPlayingId);
        }

        // 设置当前播放的音频ID
        currentPlayingId = audioId;
        return true;
    },

    /**
     * 注册音频停止
     * @param {string} audioId - 音频唯一标识
     */
    registerStop(audioId) {
        // 只清除当前播放的ID，如果它匹配
        if (currentPlayingId === audioId) {
            currentPlayingId = null;
        }
    },

    /**
     * 订阅停止事件
     * @param {string} audioId - 音频唯一标识
     * @param {Function} callback - 停止回调函数
     */
    onStopRequest(audioId, callback) {
        const handler = (id) => {
            if (id === audioId) {
                callback();
            }
        };

        emitter.on('stop-audio', handler);

        // 返回取消订阅的函数
        return () => {
            emitter.off('stop-audio', handler);
        };
    },

    /**
     * 停止所有音频播放
     */
    stopAll() {
        if (currentPlayingId) {
            emitter.emit('stop-audio', currentPlayingId);
            currentPlayingId = null;
        }
    }
};

export default audioManager;
