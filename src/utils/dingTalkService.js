import * as dd from 'dingtalk-jsapi';
import { useAppStore } from '@/stores/app';
import { logInfo, logError } from '@/services/ftwoService';

/**
 * 获取用于钉钉鉴权的URL（包含完整路由的URL）
 * @returns {string} 用于钉钉鉴权的URL
 */
function getAuthUrl() {
    // 获取完整的当前URL，包含路由部分
    return window.location.href;
}

/**
 * 初始化钉钉 JSAPI
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function initializeDingTalkJsApi() {
    try {
        const appStore = useAppStore();

        // 获取用于钉钉鉴权的URL（完全不带路由的纯净URL）
        const currentUrl = getAuthUrl();

        // 调用接口获取配置
        const config = await appStore.getJsApiConfig({
            url: currentUrl
        });

        if (!config) {
            console.error('获取钉钉配置失败: 返回了空配置');
            alert('获取钉钉配置失败');
            throw new Error('获取钉钉配置失败');
        }

        // 配置钉钉 jsapi
        console.log('准备配置钉钉jsapi, 配置参数:', {
            agentId: config.agentId,
            corpId: config.corpId,
            timeStamp: config.timeStamp,
            nonceStr: config.nonceStr,
            signature: config.signature
        });
        dd.config({
            agentId: config.agentId,
            corpId: config.corpId,
            timeStamp: config.timeStamp,
            nonceStr: config.nonceStr,
            signature: config.signature,
            type: 0,
            jsApiList: [
                'runtime.info',
                'biz.contact.choose',
                'device.notification.confirm',
                'device.notification.alert',
                'device.notification.prompt',
                'biz.ding.post',
                'biz.util.openLink',
                'device.audio.startRecord',
                'device.audio.stopRecord',
                'device.audio.onRecordEnd',
                'device.audio.play',
                'device.audio.pause',
                'device.audio.resume',
                'device.audio.stop',
                'device.audio.onPlayEnd',
                'device.audio.translateVoice',
                'device.audio.download',
                'util.domainStorage.setItem',
                'util.domainStorage.getItem',
                'util.domainStorage.removeItem',
                'biz.util.chooseImage',
                'getLocation',
                'locateInMap'
            ]
        });

        dd.ready(function () {
            logInfo('钉钉JSAPI初始化成功', config.jsApiList?.length || 0, config.corpId);
        });

        // 添加错误处理
        dd.error(function (err) {
            logError('钉钉JSAPI错误', err, err instanceof Error ? err.stack : JSON.stringify(err));
        });
    } catch (error) {
        console.error('错误详情:', JSON.stringify(error));
    }
}
