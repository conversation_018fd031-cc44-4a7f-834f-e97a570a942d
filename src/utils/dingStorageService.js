/**
 * 钉钉存储服务
 * 提供基于钉钉 domainStorage API 的存储操作
 */
import { isDingTalk } from '@/utils/index';
import * as dd from 'dingtalk-jsapi'; // 直接导入
/**
 * 设置存储项
 * @param {string} key - 存储键名
 * @param {any} value - 存储值
 * @returns {Promise<any>} 操作结果
 */
export const setStorageItem = (key, value) => {
    return new Promise((resolve, reject) => {
        if (!isDingTalk()) {

            // 如果不在钉钉环境，使用 localStorage 作为备选
            try {
                localStorage.setItem(key, JSON.stringify(value));
                resolve({ success: true });
            } catch (error) {
                reject(error);
            }
            return;
        }

        dd.util.domainStorage.setItem({
            name: key,
            value: JSON.stringify(value),
            onSuccess: (info) => {
                resolve(info);
            },
            onFail: (err) => {
                console.error('钉钉存储设置失败:', err);
                reject(err);
            }
        });
    });
};

/**
 * 获取存储项
 * @param {string} key - 存储键名
 * @returns {Promise<any>} 存储值
 */
export const getStorageItem = (key) => {
    return new Promise((resolve, reject) => {
        if (!isDingTalk()) {
            // 如果不在钉钉环境，使用 localStorage 作为备选
            try {
                const value = localStorage.getItem(key);
                resolve(value ? JSON.parse(value) : null);
            } catch (error) {
                reject(error);
            }
            return;
        }

        dd.util.domainStorage.getItem({
            name: key,
            onSuccess: (info) => {
                try {
                    // 钉钉返回的是 {value: 'xxx'} 格式
                    const parsedValue = info.value
                        ? JSON.parse(info.value)
                        : null;
                    resolve(parsedValue);
                } catch (error) {
                    console.error('解析存储值失败:', error);
                    resolve(null);
                }
            },
            onFail: (err) => {
                console.error('钉钉存储获取失败:', err);
                reject(err);
            }
        });
    });
};

/**
 * 删除存储项
 * @param {string} key - 存储键名
 * @returns {Promise<any>} 操作结果
 */
export const removeStorageItem = (key) => {
    return new Promise((resolve, reject) => {
        if (!isDingTalk()) {
            // 如果不在钉钉环境，使用 localStorage 作为备选
            try {
                localStorage.removeItem(key);
                resolve({ success: true });
            } catch (error) {
                reject(error);
            }
            return;
        }

        dd.util.domainStorage.removeItem({
            name: key,
            onSuccess: (info) => {
                resolve(info);
            },
            onFail: (err) => {
                console.error('钉钉存储删除失败:', err);
                reject(err);
            }
        });
    });
};
