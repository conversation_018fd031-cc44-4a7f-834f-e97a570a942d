// 业务线配置
export const BUSINESS_CONFIG = {
    // 智慧签约
    SMART_SIGN: {
        BASE_URL: import.meta.env.VITE_SMART_SIGN_BASE_URL,
        PATH: import.meta.env.VITE_SMART_SIGN_PATH
    },
    // 智能问数
    SMART_DATA: {
        BASE_URL: import.meta.env.VITE_SMART_DATA_BASE_URL,
        PATH: import.meta.env.VITE_SMART_DATA_PATH
    },
    // 智慧执行
    SMART_EXECUTION: {
        BASE_URL: import.meta.env.VITE_SMART_EXECUTION_BASE_URL,
        PATH: import.meta.env.VITE_SMART_EXECUTION_PATH
    },
    // 智慧打卡
    SMART_CLOCK: {
        BASE_URL: import.meta.env.VITE_SMART_CLOCK_BASE_URL,
        PATH: import.meta.env.VITE_SMART_CLOCK_PATH
    }
};

// URL 生成器
export const getBusinessUrl = (businessType, params = {}) => {
    const config = BUSINESS_CONFIG[businessType];
    if (!config) {
        throw new Error(`未找到业务线配置: ${businessType}`);
    }

    const url = new URL(config.BASE_URL + config.PATH);

    // 添加 URL 参数
    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            url.searchParams.append(key, value);
        }
    });

    return url.toString();
};

export const getTopLevelPath = (path) => {
    // 处理根路径和空路径
    if (path === '/' || !path) {return '/';}

    // 切割路径并取第一有效段
    const segments = path.split('/').filter(Boolean);
    return `/${segments[0]}`;
};

// 业务线类型枚举
export const BUSINESS_TYPES = {
    SMART_SIGN: 'SMART_SIGN',
    SMART_DATA: 'SMART_DATA',
    SMART_EXECUTION: 'SMART_EXECUTION',
    SMART_CLOCK: 'SMART_CLOCK'
};
