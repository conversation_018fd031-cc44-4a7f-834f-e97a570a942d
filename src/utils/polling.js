/**
 * 异步接口轮询工具
 * @param {Function} requestFn - 请求函数，返回Promise
 * @param {Function} successCondition - 成功条件判断函数，参数为接口返回数据，返回布尔值
 * @param {Object} options - 配置选项
 * @param {number} options.maxRetries - 最大重试次数，默认5次
 * @param {number} options.interval - 轮询间隔(毫秒)，默认2000ms
 * @param {number} options.maxPollingTime - 最大轮询时间(毫秒)，默认300000ms(5分钟)
 * @param {Function} options.onPolling - 轮询中的回调函数，参数为当前重试次数和最新数据
 * @param {Function} options.onSuccess - 成功时的回调函数，参数为成功的数据
 * @param {Function} options.onError - 错误时的回调函数，参数为错误信息
 * @param {Function} options.onTimeout - 超时时的回调函数
 * @returns {Promise<Object>} - 返回Promise，resolve为最终结果，reject为错误信息
 */
export const pollRequest = (requestFn, successCondition, options = {}) => {
    const {
        maxRetries = 5,
        interval = 1000,
        maxPollingTime = 180000, // 默认最长轮询5分钟
        onPolling = () => {
            // 轮询中的默认处理
        },
        onSuccess = () => {
            // 成功时的默认处理
        },
        onError = () => {
            // 错误时的默认处理
        },
        onTimeout = () => {
            // 超时时的默认处理
        }
    } = options;

    let retries = 0;
    let pollingCount = 0;
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
        // 设置超时计时器
        const timeoutId = setTimeout(() => {
            onTimeout();
            reject(new Error('轮询超时'));
        }, maxPollingTime);

        const poll = async () => {
            try {
                // 执行请求
                const response = await requestFn();
                pollingCount++;

                // 调用轮询中回调
                onPolling(pollingCount, response);

                // 检查是否满足成功条件
                if (successCondition(response)) {
                    clearTimeout(timeoutId);
                    onSuccess(response);
                    resolve(response);
                    return;
                }

                // 检查是否超过最大轮询时间
                if (Date.now() - startTime >= maxPollingTime) {
                    clearTimeout(timeoutId);
                    onTimeout();
                    reject(new Error('轮询超时'));
                    return;
                }

                // 继续轮询
                setTimeout(poll, interval);
            } catch (error) {
                retries++;

                // 如果超过最大重试次数，则返回错误
                if (retries >= maxRetries) {
                    clearTimeout(timeoutId);
                    onError(error);
                    reject(error);
                    return;
                }

                // 继续轮询
                setTimeout(poll, interval);
            }
        };

        // 开始轮询
        poll();
    });
};

/**
 * 针对特定returnCode的轮询封装
 * @param {Function} apiFn - API请求函数
 * @param {Object} params - API请求参数
 * @param {Object} options - 轮询配置选项
 * @returns {Promise<Object>} - 返回Promise，resolve为最终结果
 */
export const pollAsyncResult = (apiFn, params, options = {}) => {
    const defaultOptions = {
        maxRetries: 3, // 请求失败最大重试次数
        interval: 2000, // 轮询间隔
        maxPollingTime: 180000, // 最大轮询时间3分钟
        processingcode: 8001, // 处理中的返回码
        successcode: 0, // 成功的返回码
        ...options
    };

    // 定义成功条件：returnCode为0表示成功
    const successCondition = (response) => {
        return response && response.returncode === defaultOptions.successcode;
    };

    // 定义请求函数
    const requestFn = async () => {
        const response = await apiFn(params);

        // 如果返回处理中的状态码，继续轮询
        if (response && response.returncode === defaultOptions.processingcode) {
            return response;
        }

        // 如果是其他错误码，抛出异常
        if (response && response.returncode !== defaultOptions.successcode) {
            console.warn('获取结果失败3');
            throw new Error(
                `API错误: ${response.returncode}, ${response.returnMessage || '未知错误'}`
            );
        }

        return response;
    };

    return pollRequest(requestFn, successCondition, defaultOptions);
};
