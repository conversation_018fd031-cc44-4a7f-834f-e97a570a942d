/**
 * 调试服务 - 提供 VConsole 的初始化和管理功能
 * 仅在开发和测试环境中启用
 */
import VConsole from 'vconsole';
import { isDingTalk, detectDingTalkPlatform, DINGTALK_PLATFORM } from '@/utils/index';

// VConsole 实例
let vConsoleInstance = null;

// 存储键名
const STORAGE_KEYS = {
    VCONSOLE_ENABLED: 'vconsole_enabled'
};

/**
 * 检查是否应该启用 VConsole
 * @returns {boolean} 是否应该启用
 */
export function shouldEnableVConsole() {
    // 只在开发和测试环境启用
    const isDevelopment = import.meta.env.MODE === 'development';
    // const isTest = import.meta.env.MODE === 'test';

    // 检查本地存储中是否有明确的启用/禁用设置
    const storedPreference = localStorage.getItem(STORAGE_KEYS.VCONSOLE_ENABLED);
    if (storedPreference !== null) {
        return storedPreference === 'true';
    }

    // 默认在开发和测试环境启用
    return isDevelopment;
}

/**
 * 初始化 VConsole
 * @param {Object} options - VConsole 配置选项
 * @returns {VConsole|null} VConsole 实例或 null
 */
export function initVConsole(options = {}) {
    // 如果已经初始化，则直接返回实例
    if (vConsoleInstance) {
        return vConsoleInstance;
    }

    // 检查是否应该启用
    if (!shouldEnableVConsole()) {
        return null;
    }

    // 根据钉钉平台类型调整配置
    const platform = detectDingTalkPlatform();
    const defaultOptions = {
        theme: 'dark',
        maxLogNumber: 1000,
        // 在iOS上默认折叠，因为iOS上VConsole可能会影响页面交互
        defaultPluginsConfig: {
            system: {
                // 系统信息默认展开
                isInited: true,
                isShow: true
            },
            network: {
                // 网络请求默认折叠
                isInited: true,
                isShow: false
            },
            element: {
                // 元素查看默认折叠
                isInited: true,
                isShow: false
            },
            storage: {
                // 存储信息默认折叠
                isInited: true,
                isShow: false
            }
        }
    };

    // 合并配置
    const mergedOptions = { ...defaultOptions, ...options };

    try {
        // 创建 VConsole 实例
        vConsoleInstance = new VConsole(mergedOptions);

        // 添加钉钉环境信息
        if (isDingTalk()) {
            console.info('钉钉环境信息:', {
                平台类型: platform,
                用户代理: navigator.userAgent,
                屏幕尺寸: `${window.screen.width}x${window.screen.height}`,
                设备像素比: window.devicePixelRatio
            });
        }

        return vConsoleInstance;
    } catch (error) {
        console.error('VConsole 初始化失败:', error);
        return null;
    }
}

/**
 * 切换 VConsole 的显示状态
 * @returns {boolean} 切换后的状态
 */
export function toggleVConsole() {
    if (!vConsoleInstance) {
        // 如果未初始化，则初始化并显示
        vConsoleInstance = initVConsole();
        localStorage.setItem(STORAGE_KEYS.VCONSOLE_ENABLED, 'true');
        return true;
    }

    // 切换显示状态
    if (vConsoleInstance.isInited) {
        const newState = !vConsoleInstance.showSwitch;
        vConsoleInstance.showSwitch = newState;
        localStorage.setItem(STORAGE_KEYS.VCONSOLE_ENABLED, newState ? 'true' : 'false');
        return newState;
    }

    return false;
}

/**
 * 销毁 VConsole 实例
 */
export function destroyVConsole() {
    if (vConsoleInstance) {
        vConsoleInstance.destroy();
        vConsoleInstance = null;
        localStorage.setItem(STORAGE_KEYS.VCONSOLE_ENABLED, 'false');
    }
}
