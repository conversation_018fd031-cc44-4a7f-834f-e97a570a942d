/**
 * 获取完整的音频URL
 * @param {string} url - 音频文件的相对路径或完整URL
 * @returns {string} 完整的音频URL
 */
export const getFullAudioUrl = function (url) {
    if (!url) {return '';}

    // 如果已经是完整URL（以http或https开头），直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 获取当前页面的origin（包含协议、主机名和端口）
    const origin = window.location.origin;

    // 如果url 里 api 需要替换为 trainer-api
    const path = url.replace('api', 'trainer-api');

    return `${origin}${path}`;
};
