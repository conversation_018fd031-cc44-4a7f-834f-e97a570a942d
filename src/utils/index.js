export function cutSchema(src, holder = '://') {
    if (src && src !== '' && holder) {
        let index = src.indexOf(holder);
        return src.substring(index + 1);
    }
    return src;
}

const FILE_ICON = {
    pdf: 'icon-pdf.png',
    txt: 'icon-txt.png',
    png: 'icon-img.png',
    jpg: 'icon-img.png',
    jpeg: 'icon-img.png',
    csv: 'icon-xls.png',
    xls: 'icon-xls.png',
    xlsx: 'icon-xls.png',
    ppt: 'icon-ppt.png',
    pptx: 'icon-ppt.png',
    doc: 'icon-word.png',
    docx: 'icon-word.png',
    mp3: 'icon-audio.png',
    wav: 'icon-audio.png',
    video: 'icon-video.png',
    '3gp': 'icon-video.png',
    mp4: 'icon-video.png',
    m4v: 'icon-video.png',
    mov: 'icon-video.png',
    rm: 'icon-video.png',
    rmvb: 'icon-video.png',
    avi: 'icon-video.png',
    mkv: 'icon-video.png',
    flv: 'icon-video.png',
    vob: 'icon-video.png',
    wmv: 'icon-video.png',
    asf: 'icon-video.png',
    asx: 'icon-video.png',
    rar: 'icon-rar.png',
    '7z': 'icon-rar.png',
    zip: 'icon-rar.png',
    tar: 'icon-rar.png',
    gzip: 'icon-rar.png',
    other: 'icon-other.png'
};

export function fileIcon(extension) {
    const path = '//x.autoimg.cn/dealer/crmapp/assets/img/product/';
    const key = extension ? extension.toLowerCase() : 'other';
    return path + (FILE_ICON.hasOwnProperty(key) ? FILE_ICON[key] : FILE_ICON['other']);
}

export function checkFile(url) {
    const extension = fileExtension(url);
    const key = extension ? extension.toLowerCase() : 'other';
    return key !== 'other' && FILE_ICON.hasOwnProperty(key);
}

/**
 * 安全获取用户代理字符串
 * @param {boolean} toLowerCase - 是否转换为小写，默认为 true
 * @returns {string} 用户代理字符串，如果获取失败返回空字符串
 */
export function getUserAgent(toLowerCase = true) {
    try {
        const userAgent = (navigator && navigator.userAgent) || '';
        return toLowerCase ? userAgent.toLowerCase() : userAgent;
    } catch (error) {
        console.warn('获取用户代理字符串失败:', error);
        return '';
    }
}

/**
 * 安全检查是否包含指定字符串
 * @param {string} searchString - 要搜索的字符串
 * @param {boolean} ignoreCase - 是否忽略大小写，默认为 true
 * @returns {boolean} 是否包含指定字符串
 */
export function userAgentIncludes(searchString, ignoreCase = true) {
    const userAgent = getUserAgent(ignoreCase);
    const search = ignoreCase ? searchString.toLowerCase() : searchString;
    return userAgent.includes(search);
}

export function isDingTalk() {
    return userAgentIncludes('dingtalk');
}

/**
 * 判断当前环境是否为钉钉PC端
 * @returns {boolean} 是否为钉钉PC端
 */
export function isDingTalkPC() {
    if (!isDingTalk()) {
        return false;
    }

    // 钉钉PC端特有的UA标识
    return (
        userAgentIncludes('dingtalk-win') ||
        userAgentIncludes('dingtalk-mac') ||
        (userAgentIncludes('dingtalk') &&
            (userAgentIncludes('windows') || userAgentIncludes('macintosh')))
    );
}

/**
 * 判断当前环境是否为钉钉移动端
 * @returns {boolean} 是否为钉钉移动端
 */
export function isDingTalkMobile() {
    if (!isDingTalk()) {
        return false;
    }

    // 如果是钉钉环境但不是PC端，则认为是移动端
    return !isDingTalkPC();
}

/**
 * 钉钉平台类型枚举
 */
export const DINGTALK_PLATFORM = {
    UNKNOWN: 'unknown',
    IOS: 'ios',
    ANDROID: 'android',
    PC: 'pc'
};

/**
 * 本地存储键名
 */
const STORAGE_KEYS = {
    DINGTALK_PLATFORM: 'dingtalk_platform_type'
};

/**
 * 检测当前钉钉平台类型
 * @returns {string} 平台类型，值为 DINGTALK_PLATFORM 中的一种
 */
export function detectDingTalkPlatform() {
    // 首先检查本地存储中是否已有平台信息
    const cachedPlatform = localStorage.getItem(STORAGE_KEYS.DINGTALK_PLATFORM);
    if (cachedPlatform) {
        return cachedPlatform;
    }

    // 如果不在钉钉环境中，直接返回 UNKNOWN
    if (!isDingTalk()) {
        const platform = DINGTALK_PLATFORM.UNKNOWN;
        localStorage.setItem(STORAGE_KEYS.DINGTALK_PLATFORM, platform);
        return platform;
    }

    // 检测平台类型
    let platform = DINGTALK_PLATFORM.UNKNOWN;
    if (userAgentIncludes('dingtalk')) {
        if (userAgentIncludes('iphone') || userAgentIncludes('ipad')) {
            platform = DINGTALK_PLATFORM.IOS;
        } else if (userAgentIncludes('android')) {
            platform = DINGTALK_PLATFORM.ANDROID;
        } else if (userAgentIncludes('windows') || userAgentIncludes('macintosh')) {
            platform = DINGTALK_PLATFORM.PC;
        } else {
            platform = DINGTALK_PLATFORM.UNKNOWN;
        }
    } else {
        platform = DINGTALK_PLATFORM.UNKNOWN;
    }

    // 将结果缓存到本地存储
    localStorage.setItem(STORAGE_KEYS.DINGTALK_PLATFORM, platform);
    return platform;
}

/**
 * 检查特定的钉钉 JSAPI 是否在当前平台可用
 * @param {string} apiName - JSAPI 名称，例如 'ui.webViewBounce.disable'
 * @returns {boolean} 是否可用
 */
export function isDingTalkApiSupported(apiName) {
    const platform = detectDingTalkPlatform();

    // 特定 API 的平台兼容性配置
    const apiCompatibility = {
        'ui.webViewBounce.disable': [DINGTALK_PLATFORM.IOS, DINGTALK_PLATFORM.ANDROID],
        'biz.navigation.setTitle': [
            DINGTALK_PLATFORM.IOS,
            DINGTALK_PLATFORM.ANDROID,
            DINGTALK_PLATFORM.PC
        ],
        'dd.setNavigationTitle': [
            DINGTALK_PLATFORM.IOS,
            DINGTALK_PLATFORM.ANDROID,
            DINGTALK_PLATFORM.PC
        ],
        'device.notification.vibrate': [DINGTALK_PLATFORM.IOS, DINGTALK_PLATFORM.ANDROID],
        'device.audio.startRecord': [DINGTALK_PLATFORM.IOS, DINGTALK_PLATFORM.ANDROID]
    };

    // 检查 API 是否在兼容性配置中
    if (!apiCompatibility[apiName]) {
        console.warn(`未知的钉钉 API: ${apiName}`);
        return false;
    }

    // 检查当前平台是否支持该 API
    return apiCompatibility[apiName].includes(platform);
}
