import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import './style.scss';
import PrimeVue from 'primevue/config';
import 'primevue/resources/themes/aura-light-blue/theme.css';
import 'primeicons/primeicons.css';
import 'github-markdown-css';
import registerPrimeVue from './registerPrimeVue';
import ToastService from 'primevue/toastservice';
import ConfirmationService from 'primevue/confirmationservice';
import { initVConsole } from '@/utils/debugService';
import { initErrorHandler } from '@/utils/errorHandler';

// 初始化 VConsole（仅在开发和测试环境）
initVConsole();

// 初始化全局错误处理
initErrorHandler();

const app = createApp(App);

app.use(createPinia());
app.use(router);

// 配置 PrimeVue，使用内置的中文本地化
app.use(PrimeVue, {
    locale: {
        startsWith: '开始于',
        contains: '包含',
        notContains: '不包含',
        endsWith: '结束于',
        equals: '等于',
        notEquals: '不等于',
        noFilter: '无筛选',
        lt: '小于',
        lte: '小于等于',
        gt: '大于',
        gte: '大于等于',
        dateIs: '等于',
        dateIsNot: '不等于',
        dateBefore: '早于',
        dateAfter: '晚于',
        clear: '清除',
        apply: '确定',
        matchAll: '匹配所有',
        matchAny: '匹配任意',
        addRule: '添加规则',
        removeRule: '删除规则',
        accept: '是',
        reject: '否',
        choose: '选择',
        upload: '上传',
        cancel: '取消',
        completed: '完成',
        pending: '待处理',
        dayNames: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
        dayNamesShort: ['日', '一', '二', '三', '四', '五', '六'],
        dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
        monthNames: [
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月'
        ],
        monthNamesShort: [
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月'
        ],
        chooseYear: '选择年份',
        chooseMonth: '选择月份',
        chooseDate: '选择日期',
        prevDecade: '上一个十年',
        nextDecade: '下一个十年',
        prevYear: '上一年',
        nextYear: '下一年',
        prevMonth: '上个月',
        nextMonth: '下个月',
        prevHour: '上一小时',
        nextHour: '下一小时',
        prevMinute: '上一分钟',
        nextMinute: '下一分钟',
        prevSecond: '上一秒',
        nextSecond: '下一秒',
        am: '上午',
        pm: '下午',
        today: '今天',
        weekHeader: '周',
        firstDayOfWeek: 1,
        dateFormat: 'yy年mm月dd日',
        weak: '弱',
        medium: '中',
        strong: '强',
        passwordPrompt: '请输入密码',
        emptyFilterMessage: '无可用选项',
        searchMessage: '有 {0} 个结果可用',
        selectionMessage: '已选择 {0} 项',
        emptySelectionMessage: '未选择项目',
        emptySearchMessage: '未找到结果',
        emptyMessage: '无可用选项'
    }
});

registerPrimeVue(app);
app.use(ToastService);
app.use(ConfirmationService);

app.mount('#app');
