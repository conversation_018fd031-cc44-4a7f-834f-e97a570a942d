import { ref, computed } from 'vue';
import useChatStore from '@/stores/chat';
import { useRouter } from 'vue-router';
import { useTrackingService } from '@/services';
/**
 * bot选择逻辑的组合式函数
 * @param {Object} options - 配置选项
 * @param {Function} options.onSuccess - 成功选择bot后的回调
 * @param {Function} options.onClose - 关闭面板的回调
 * @returns {Object} - 暴露的状态和方法
 */
export function useBotSelector(options = {}) {
    const { onSuccess, onClose } = options;

    // store and router
    const chatStore = useChatStore();
    const router = useRouter();
    const trackingService = useTrackingService();
    const elfBots = computed(() => chatStore.elfBots);
    const selectedBot = computed(() => chatStore.selectedElfBot);

    // 底部面板相关状态
    const showBottomPanel = ref(false);
    const panelHeightPercent = ref(1);
    const showRouterView = ref(false);
    const showIframeView = ref(false);
    const routerPath = ref('');
    const iframeConfig = ref({});

    /**
     * 重置面板状态
     */
    const resetPanel = () => {
        showBottomPanel.value = false;
        showIframeView.value = false;
        showRouterView.value = false;
        routerPath.value = '';
        iframeConfig.value = {};
        panelHeightPercent.value = 1;

        if (onClose) {
            onClose();
        }
    };

    /**
     * 关闭路由视图
     */
    const closeRouterView = () => {
        showRouterView.value = false;
        routerPath.value = '';
    };

    /**
     * 根据bot类型选择合适的操作
     * @param {Object} bot - 选中的bot对象
     */
    const selectBot = async bot => {
        // 根据elfType处理不同的操作
        if (bot.elfType === 1) {
            // 聊天类型，设置选中状态
            chatStore.setSelectedElfBot(bot);

            if (onSuccess) {
                onSuccess(bot);
            }

            // 业务逻辑成功后发送埋点
            await trackingService.sendBotSelectTracking(bot);
            return;
        } else if (bot.elfType === 2) {
            // 外部链接 - 通过iframe展示
            const url = new URL(bot.elfUrl);
            const queryParams = Object.fromEntries(url.searchParams.entries());

            showBottomPanel.value = true;
            showIframeView.value = true;
            panelHeightPercent.value = Number(queryParams.panelHeightPercent) || 1;

            iframeConfig.value = {
                src: url.href,
                props: queryParams
            };

            // 业务逻辑成功后发送埋点
            await trackingService.sendBotSelectTracking(bot);
        } else if (bot.elfType === 3) {
            // 内部路由 - 通过动态组件展示
            const urlParts = bot.elfUrl.split('?');
            routerPath.value = urlParts[0];

            let queryParams = {};
            if (urlParts.length > 1) {
                const searchParams = new URLSearchParams('?' + urlParts[1]);
                searchParams.forEach((value, key) => {
                    queryParams[key] = value;
                });
            }

            showBottomPanel.value = true;
            showRouterView.value = true;

            panelHeightPercent.value = Number(queryParams.panelHeightPercent) || 1;

            // 业务逻辑成功后发送埋点
            await trackingService.sendBotSelectTracking(bot);
        } else if (bot.elfType === 4) {
            // 内部路由push跳转，添加来源标识
            const targetUrl = bot.elfUrl;
            router.push({
                path: targetUrl,
                query: {
                    fromGenie: '1'
                }
            });

            // 业务逻辑成功后发送埋点
            await trackingService.sendBotSelectTracking(bot);
        } else {
            // 未知类型处理
            console.warn(`未知的elfType: ${bot.elfType}，作为普通bot处理`);
            // 即使是未知类型，也记录埋点以便分析
            await trackingService.sendBotSelectTracking(bot);
        }
    };

    /**
     * 清除选中的bot
     */
    const clearSelectedBot = () => {
        chatStore.setSelectedElfBot(null);
        if (onSuccess) {
            onSuccess(null);
        }
    };

    /**
     * 从URL获取并设置bot
     * @param {string} botId - URL中的botId参数
     */
    const setBotFromUrl = async botId => {
        if (!botId) {
            return;
        }

        // 在elfBots中查找匹配的bot
        const matchedBot = elfBots.value.find(bot => Number(bot.id) === Number(botId));

        if (matchedBot.id) {
            await selectBot(matchedBot);
        } else {
            console.warn(`未找到ID为${botId}的bot`);
        }
    };

    return {
        // 状态
        elfBots,
        selectedBot,
        showBottomPanel,
        panelHeightPercent,
        showRouterView,
        showIframeView,
        routerPath,
        iframeConfig,

        // 方法
        selectBot,
        resetPanel,
        closeRouterView,
        clearSelectedBot,
        setBotFromUrl
    };
}
