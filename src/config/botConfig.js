/**
 * 机器人配置数据
 * 统一管理所有智能机器人的配置信息，包括头像、标题、副标题和预设问题
 */
export const botConfigs = {
    1: {
        title: '智能客服',
        subtitle: '您可以向我咨询以下问题',
        welcomeTitle: '你好！我是你的 “ AI助手 ”',
        welcomeSubtitle:
            'Hi小可爱，我是家家精灵，你的提效小助理~可以尝试从《会话》《找人》《问事》《取物》《签约》等场景来和我交流。',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-chat2.png',
        presets: [
            { title: '找人', desc: '一汽奥迪网销是谁？' },
            { title: '问事', desc: '如何更新车型报价？' },
            { title: '取物', desc: '物料获取' },
            { title: '签约', desc: '我要签约' }
        ]
    },
    2: {
        title: '智慧执行',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ AI活动助手 ”',
        welcomeSubtitle: '关于营销活动创建&线索&驻店&补贴等相关问题，欢迎与我交流。',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-execution.png',
        presets: [
            { title: '营销活动创建问题', desc: '如何上线集客专题？上线活动专题谁审核？' },
            { title: '营销活动线索问题', desc: '商家反馈线索有效率低，找谁处理？' },
            { title: '驻店培训问题', desc: '驻店培训下单问题，咨询哪位？' },
            { title: '厂商线索问题', desc: '专题直接对接厂商线索找哪位？' }
        ]
    },
    10: {
        title: '智能问数',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ 车商助手 ” ',
        welcomeSubtitle: '关于商家基础运营及销量成交价情况等相关指标问题，欢迎与我交流。',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-data2.png',
        presets: [
            {
                title: '运营及经营诊断报告',
                desc: '报告解读',
                iconUrl:
                    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-chat/report1.png',
                description: '运营及经营诊断报告'
            },
            {
                title: '顾问入店拜访前预览',
                desc: '拜访话术',
                iconUrl:
                    'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-chat/huashu.png',
                description: '顾问入店拜访前预览'
            }
        ]
    },
    11: {
        title: '智通车商',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ 车商助手 ”',
        welcomeSubtitle:
            '专注i车商运维难题。聚焦指标定义、后台各版块设置与操作，有问即答，专业高效，助你轻松运营！',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-copilot.png',
        presets: [
            {
                title: '运营分',
                desc: '在哪里查看得分？'
            },
            {
                title: '问题解答',
                desc: '商擎考核规则是什么？'
            },
            {
                title: '问题解答',
                desc: '怎么查询各个车型的发文数量？'
            },
            {
                title: '问题解答',
                desc: '什么是车型报价覆盖度'
            }
        ]
    },
    12: {
        title: '智效研发',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ 研发百事通 ”',
        welcomeSubtitle: '一键获取系统地址、文档和常见问题解答，让开发更高效！',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-copilot.png',
        presets: [
            {
                title: 'QA',
                desc: 'QA质量平台地址'
            },
            {
                title: '拨测',
                desc: '拨测场景地址'
            },
            {
                title: '协作平台',
                desc: '协作平台地址'
            },
            {
                title: '智效店铺',
                desc: '智效店铺商家端地址'
            }
        ]
    },
    13: {
        title: '智展随问',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ 展厅助手 ”',
        welcomeSubtitle:
            '关于展厅搭建、红包活动、权益审核、技术故障排查等全链路问题，欢迎与我交流！',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-copilot.png',
        presets: [
            {
                title: '展厅搭建',
                desc: '新版展厅的C端露出流程是什么？'
            },
            {
                title: '红包活动',
                desc: '红包为什么会自动下线？'
            },
            {
                title: '权益审核',
                desc: '商家无权益会导致什么影响？'
            },
            {
                title: '技术故障排查',
                desc: '终端问题导致短信收不到怎么办？'
            }
        ]
    },
    16: {
        title: '智慧活动',
        subtitle: '请选择您想咨询的问题',
        welcomeTitle: '你好！我是你的 “ 营销活动AI助手 ”',
        welcomeSubtitle:
            '可以帮助查询、排查、解决营销活动相关各种问题。比如：818专题、TM活动、驻店工单等',
        avatarUrl:
            'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/logo/smart-copilot.png',
        presets: [
            {
                title: '活动创建',
                desc: '合同号AD-JXS-202506-004425，不能建活动？'
            },
            {
                title: '活动查询',
                desc: '活动687603的合同号是多少？'
            },
            {
                title: '活动任务',
                desc: '活动687603为什么没有pmo任务？'
            },
            {
                title: '活动场地',
                desc: '活动687603选不了场地信息？'
            }
        ]
    }
};

/**
 * 获取指定机器人配置
 * @param {string|number} botId - 机器人ID
 * @returns {Object} 机器人配置对象
 */
export function getBotConfig(botId) {
    const botIdNum = Number(botId);
    return botConfigs[botIdNum] || botConfigs[1]; // 默认返回 botId 1 的配置
}
