module.exports = {
    plugins: {
        'postcss-pxtorem': {
            rootValue: 14, // 根据设计稿设置，通常设计稿是750px宽，可以设置为37.5或75
            propList: ['*'], // 需要转换的属性列表，*表示所有属性都进行转换
            unitPrecision: 5, // 转换后的rem值的小数点位数
            selectorBlackList: [], // 不进行转换的选择器列表
            replace: true, // 替换包含px的规则
            mediaQuery: false, // 允许在媒体查询中转换px
            minPixelValue: 0, // 最小的转换数值，低于这个值的px不进行转换
        },
    },
};
