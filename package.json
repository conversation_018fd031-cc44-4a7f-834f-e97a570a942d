{"name": "chat-knowledge-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build:prod": "vite build --mode production && node scripts/upload-to-cdn.js", "build:test": "vite build --mode test && node scripts/upload-to-cdn.js", "build:prod:cdn": "vite build --mode production && node scripts/upload-to-cdn.js", "upload:cdn": "node scripts/upload-to-cdn.js", "preview": "vite preview", "serve:dev": "vite --mode development", "serve:test": "vite --mode test", "serve:prod": "vite --mode production", "upload-server": "node scripts/upload-server.js", "lint:fix-all": "eslint \"src/**/*.{vue,js}\" --fix --quiet", "prepare": "husky"}, "dependencies": {"@auto/s3-static-sdk": "^2.0.9", "@vueuse/core": "^12.5.0", "chart.js": "^4.4.7", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "diff-dom": "^5.1.4", "dingtalk-jsapi": "^3.0.46", "echarts": "^5.6.0", "github-markdown-css": "^5.8.1", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-to-txt": "^2.0.1", "mitt": "^3.0.1", "pdfjs-dist": "^5.1.91", "pinia": "^2.2.2", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primevue": "^3.53.0", "qrcode.vue": "^3.6.0", "swiper": "10.3.1", "vconsole": "^3.15.1", "vue": "^3.4.37", "vue-echarts": "^7.0.3", "vue-pdf-embed": "^2.1.2", "vue-router": "^4.4.3", "vue3-circle-progress": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/preset-env": "^7.26.0", "@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^5.1.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/eslint-config-prettier": "^7.0.0", "code-inspector-plugin": "^0.15.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "eslint": "^7.32.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "^7.20.0", "express": "^5.1.0", "husky": "^9.1.7", "lint-staged": "^16.1.0", "multer": "1.4.5-lts.2", "postcss-pxtorem": "^6.1.0", "prettier": "2.8.8", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.77.8", "terser": "^5.39.0", "vite": "^5.4.1", "vite-plugin-compression": "^0.5.1"}, "lint-staged": {"*.{js,vue}": ["eslint --fix"]}, "packageManager": "pnpm@8.15.4"}