server {
    listen       80;
    server_name  localhost;
    location / {
       
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
         try_files $uri $uri/ /index.html;
        # 清除html的缓存
        add_header Cache-Control no-store;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }


    location ^~/api/ {
      proxy_pass http://aiwen-ding-api-dev.mulan.corpautohome.com/api/;
    }

    # smart-data-api 代理 (重写路径为 /api)
    location ^~/smart-data-api/ {
      proxy_pass http://dealercrmapi.yz.test.autohome.com.cn/api/;
    }

    # smart-data-api2 代理 (去掉前缀)
    location ^~/smart-data-api2/ {
      proxy_pass http://caraddl.api.autohome.com.cn/;
    }
}
