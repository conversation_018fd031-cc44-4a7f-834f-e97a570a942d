server {
    listen       80;
     proxy_connect_timeout 120;
        proxy_send_timeout 120;
        proxy_read_timeout 120;
        client_max_body_size 100m;
    server_name  localhost;
    location / {
       
        # root不允许自定义，只能保存在html下
        root   html;
        index  index.html index.htm;
        # 404返回首页
        try_files $uri $uri/ /index.html;
        # 清除html的缓存
        add_header Cache-Control no-store;
    }
    # 要求：1)不允许设置访问日志格式与保存路径，http层已设定好

    # 之家云k8s健康检查地址
    location /health {
      access_by_lua_block {
        ngx.say("up");
        ngx.exit(200);
      }
    }
    
    location ^~/api/ {
      proxy_pass http://aiwending.api.corpautohome.com/api/;
    }

    location ^~/trainer-api/ {
      proxy_pass http://trainer.api.corpautohome.com/api/;
    }
    
    # 添加 dutyListApi 代理
    location /auto-research {
        # 禁止传递请求体
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";  # 清空 Content-Length
        proxy_pass https://www.autohome.com.cn/ashx/editorblog/AjaxContentList?type=article&editorid=7711;
        proxy_set_header Host www.autohome.com.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # smart-data-api 代理 (重写路径为 /api)
    location ^~/smart-data-api/ {
      proxy_pass http://crmapi.lq.autohome.com.cn/api/;
    }

    # smart-data-api2 代理 (去掉前缀)
    location ^~/smart-data-api2/ {
      proxy_pass http://caraddl.api.autohome.com.cn/;
    }
   
}
