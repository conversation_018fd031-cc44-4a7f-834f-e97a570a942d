# 环境配置说明

## 域名配置

### 开发环境
- api域名：`aiwen-ding-api-dev.mulan.corpautohome.com`
- url域名：`aiwen-ding-dev.mulan.corpautohome.com`
- 用途：测试公司环境，连接 OA mock 接口

### 测试环境
- api域名：`aiwen-ding-api.mulan.corpautohome.com`
- url域名：`aiwen-ding.mulan.corpautohome.com`
- 用途：线上公司环境，连接 OA 正式接口

### 线上环境
- api域名：`aiwending.api.corpautohome.com`
- url域名：`aiwen.autohome.com.cn`
- 用途：线上公司环境，连接 OA 正式接口


# ShimmerBorder 组件 使用说明

一个用于显示闪烁边框和加载效果的Vue3组件，提供多种自定义选项。

## 使用方法

```vue
<template>
    <div class="loading-container">
        <!-- 默认圆形光效 -->
        <ShimmerBorder>
            <div class="your-content">内容区域</div>
        </ShimmerBorder>

        <!-- 线性光效 -->
        <ShimmerBorder effectType="linear">
            <div class="your-content">内容区域</div>
        </ShimmerBorder>

        <!-- 自定义颜色 -->
        <ShimmerBorder
            primaryColor="rgba(76, 175, 80, 0.6)"
            secondaryColor="rgba(33, 150, 243, 0.6)"
        >
            <div class="your-content">内容区域</div>
        </ShimmerBorder>
    </div>
</template>

<script setup>
import ShimmerBorder from '@/components/common/ShimmerBorder.vue';
</script>
```

## 属性

| 属性名         | 类型    | 默认值                    | 说明                                                 |
| -------------- | ------- | ------------------------- | ---------------------------------------------------- |
| loading        | Boolean | true                      | 是否显示加载动画效果                                 |
| customStyle    | Object  | {}                        | 自定义样式对象，会与组件默认样式合并                 |
| borderRadius   | String  | '12px'                    | 边框圆角                                             |
| primaryColor   | String  | 'rgba(0, 170, 255, 0.6)'  | 主要颜色                                             |
| secondaryColor | String  | 'rgba(126, 87, 194, 0.6)' | 次要颜色                                             |
| effectType     | String  | 'circular'                | 光效类型，可选值: 'circular'(圆形) 或 'linear'(线性) |

## 示例

可以查看 `ShimmerBorderDemo.vue` 文件，了解组件的各种使用方式和效果。

## 注意事项

1. 组件使用了 CSS 变量，确保在较旧的浏览器中提供兼容性支持
2. 组件默认占满容器宽高，请确保父容器具有合适的尺寸
3. 对于内容区域，建议使用相对定位，并设置较高的 z-index，以确保内容显示在动画效果之上
