{"root": true, "env": {"browser": true, "es6": true, "node": true}, "globals": {"console": "readonly", "process": "readonly", "Buffer": "readonly", "__dirname": "readonly", "__filename": "readonly", "global": "readonly", "window": "readonly", "document": "readonly", "navigator": "readonly", "location": "readonly"}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["vue"], "overrides": [{"files": ["**/*.vue"], "extends": ["plugin:vue/vue3-essential", "prettier"], "rules": {"vue/multi-word-component-names": "off", "vue/no-side-effects-in-computed-properties": "error", "vue/no-reserved-component-names": "off", "vue/no-mutating-props": "error", "vue/require-valid-default-prop": "error", "vue/no-unused-vars": "error", "vue/no-use-v-if-with-v-for": "error", "vue/no-multiple-template-root": "off", "vue/no-v-model-argument": "off", "vue/no-v-for-template-key": "off", "curly": "error", "no-empty-function": "error", "require-await": "error", "no-shadow": "error", "no-async-promise-executor": "error", "init-declarations": "error", "no-dupe-keys": "error", "no-await-in-loop": "error", "require-atomic-updates": "off"}}, {"files": ["**/main.js"], "rules": {"vue/multi-word-component-names": "off", "vue/no-reserved-component-names": "off"}}, {"files": ["**/*.{js,mjs,cjs}"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"curly": "error", "no-empty-function": "error", "require-await": "error", "no-shadow": "error", "no-async-promise-executor": "error", "init-declarations": "error", "no-dupe-keys": "error", "no-await-in-loop": "error", "require-atomic-updates": "off", "vue/multi-word-component-names": "off", "vue/no-reserved-component-names": "off", "no-unsafe-negation": "error"}}], "ignorePatterns": ["node_modules/**", "dist/**", "public/**", "src/primeFlex.css", "src/normalize.css", "src/flag.css", "**/*.min.js", "pnpm-lock.yaml"]}