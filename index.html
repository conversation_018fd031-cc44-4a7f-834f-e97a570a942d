<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,
                    minimum-scale=1.0,
                    maximum-scale=1.0,user-scalable=no,viewport-fit=cover" />
                    <!--replaceToBlank-->
                    <!--
                        以下meta标签静态页面防盗链专用，开发页面禁止出现 
                        replaceToBlank 标记的注释不会被打包到dist中 可用于页面任何位置
                      -->
                    <meta name="referrer" content="never">
                    <!--replaceToBlank-->
    <meta name="wpk-bid" content="dta_1_3232953638">
    <link rel="stylesheet"
        href="https://z.autoimg.cn/dealer_microfe_aidev/libs/primevue/primeicons/4.1.0/primeicons.min.css" />
    <title>家家精灵</title>
    <script src="https://x.autoimg.cn/dealer/ftwo/202108-lts/index.js" crossorigin="anonymous"></script>
    <script src="https://z.autoimg.cn/dealer_microfe_aidev/libs/ding-fe/dingtalk.open.js"></script>
    <script>
        var isOnline = location.host.indexOf('aiwen.autohome.com.cn') > -1;
        if (isOnline) {
            window.ftwo &&
                window.ftwo.init({
                    appKey: 'd1bcbc7725fe4593a9c5098a18f18307',
                    performance: {
                        rum: {
                            sampleRate: 100,
                        },
                    },
                });
        }
    </script>
    <script>
        (function (doc, win) {
            const docEl = doc.documentElement;
            const resizeEvt =
                'orientationchange' in window
                    ? 'orientationchange'
                    : 'resize';
            const userAgent = (navigator && navigator.userAgent) || '';
            const isDingTalk = userAgent.includes('DingTalk');
            const isMobile = /mobile|android|iphone|ipad/i.test(userAgent);

            const recalc = function () {
                var clientWidth = docEl.clientWidth;
                if (!clientWidth) return;
                if (isDingTalk) {
                    if (isMobile) {
                        // 375px宽度的设备上设置为14px 先固定 14px，不同的手机都展示的一样即可，
                        docEl.style.fontSize =
                            14 + 'px';
                    } else {
                        docEl.style.fontSize = 12 + 'px';
                    }
                } else {
                    docEl.style.fontSize = 14 + 'px';
                }
            };
            if (!doc.addEventListener) return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener('DOMContentLoaded', recalc, false);
        })(document, window);
    </script>
    <script>
        var isDingtalk = navigator && /DingTalk/.test(navigator.userAgent);
        var isProductEnv = window && window.location && window.location.host
            && window.location.host.indexOf('127.0.0.1') === -1
            && window.location.host.indexOf('localhost') === -1
            && window.location.host.indexOf('192.168.') === -1
            && window.location.host.indexOf('10.168.') === -1
        // 如果有其它测试域名，请一起排掉，减少测试环境对生产环境监控的干扰
        if (isProductEnv) {
            !(function (c, i, e, b) {
                var h = i.createElement("script");
                var f = i.getElementsByTagName("script")[0];
                h.type = "text/javascript";
                h.crossorigin = true;
                h.onload = function () {
                    c[b] || (c[b] = new c.wpkReporter({ bid: "dta_1_3232953638" }));
                    c[b].installAll()
                };
                f.parentNode.insertBefore(h, f);
                h.src = e
            })(window, document, "https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js", "__wpk");
        }</script>
</head>

<body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
</body>

</html>