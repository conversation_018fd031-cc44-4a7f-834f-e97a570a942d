# Swiper.js 在 Vue 项目中的使用指南

本指南旨在提供在 Vue 3 (Composition API with `<script setup>`) 项目中集成和使用 `Swiper.js` 的标准方法。

## 1. 确认依赖

在开始之前，请先检查项目的 `package.json` 文件，确认 `swiper` 和 `swiper/vue` 是否已经作为依赖项存在。

```json
"dependencies": {
  "swiper": "^10.3.1",
  "vue": "^3.4.0"
}
```
如果不存在，请通过以下命令安装：
```bash
npm install swiper
```

## 2. 在组件中使用

以下是在 Vue 组件中集成 Swiper 的完整步骤。

### 2.1. 脚本部分 (`<script setup>`)

你需要导入 `Swiper` 核心组件、所需的模块以及相关的 CSS 文件。

```vue
<script setup>
import { ref } from 'vue';

// 1. 导入 Swiper Vue 组件
import { Swiper, SwiperSlide } from 'swiper/vue';

// 2. 导入 Swiper 核心模块
// Autoplay: 自动播放
// Pagination: 分页器（小圆点）
// EffectFade: 淡入淡出效果
// Navigation: 前进后退按钮
import { Autoplay, Pagination, EffectFade, Navigation } from 'swiper/modules';

// 3. 导入 Swiper 样式
// 核心样式
import 'swiper/css';
// 分页器样式
import 'swiper/css/pagination';
// 淡入淡出效果样式
import 'swiper/css/effect-fade';
// 前进后退按钮样式
import 'swiper/css/navigation';

// 准备 Swiper 模块
const modules = [Autoplay, Pagination, EffectFade, Navigation];

// 轮播图数据
const banners = ref([
    { image: 'url/to/image1.jpg', title: 'Banner 1' },
    { image: 'url/to/image2.jpg', title: 'Banner 2' },
    { image: 'url/to/image3.jpg', title: 'Banner 3' },
]);
</script>
```

### 2.2. 模板部分 (`<template>`)

使用 `<swiper>` 和 `<swiper-slide>` 组件构建轮播图的 HTML 结构。

-   `:modules`: 绑定需要启用的模块。
-   `:slides-per-view`: 设置容器中同时显示的幻灯片数量。
-   `:loop`: 开启无缝循环模式。
-   `:autoplay`: 配置自动播放，`delay` 为切换延迟，`disableOnInteraction` 设置为 `false` 可以在用户手动操作后继续自动播放。
-   `:pagination`: 开启分页器，并设置为可点击。
-   `:effect`: 设置切换效果，如 `'fade'`（淡入淡出）、`'slide'`（滑动，默认）、`'coverflow'`（3D流）。

```vue
<template>
    <swiper
        :modules="modules"
        :slides-per-view="1"
        :space-between="0"
        :loop="true"
        :autoplay="{
            delay: 3000,
            disableOnInteraction: false
        }"
        :pagination="{ clickable: true }"
        :effect="'fade'"
        class="custom-swiper-container"
    >
        <swiper-slide v-for="(banner, index) in banners" :key="index">
            <img :src="banner.image" :alt="banner.title" class="banner-image" />
            <div class="banner-content">
                <h3>{{ banner.title }}</h3>
            </div>
        </swiper-slide>
    </swiper>
</template>
```

### 2.3. 样式部分 (`<style>`)

通常，Swiper 的默认样式不能完全满足设计需求。你可以使用 `:deep()` 选择器来覆盖其内部元素的样式。

```vue
<style lang="scss" scoped>
.custom-swiper-container {
    width: 100%;
    height: 150px; /* 根据需要调整高度 */
    border-radius: 12px;
    overflow: hidden;

    .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* 使用 :deep() 覆盖 Swiper 内部样式 */
    :deep(.swiper-pagination-bullet) {
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.7);
        opacity: 1;
        transition: all 0.3s ease;
    }

    :deep(.swiper-pagination-bullet-active) {
        width: 20px;
        border-radius: 4px;
        background: white;
    }
}
</style>
```

## 3. 关键点总结

-   **模块化导入**：`Swiper 10` 以后，所有功能（如自动播放、分页）都必须作为模块单独导入和注册。
-   **样式导入**：不要忘记导入核心 CSS 和你所使用模块的 CSS。
-   **样式覆盖**：在 `scoped` 样式中，必须使用 `:deep()` 或 `::v-deep` 来修改 Swiper 子组件的样式。
-   **灵活性**：`Swiper` 提供了极其丰富的配置项，可以查阅 [官方文档](https://swiperjs.com/vue) 以了解更多高级用法。
description:
globs:
alwaysApply: false
---
