---
description: 
globs: 
alwaysApply: false
---
# 代码审查指南

## 审查重点

### 1. 逻辑漏洞和边界情况

* **空值处理**：检查 null、undefined、空字符串的处理
* **边界条件**：验证数组越界、数值范围、字符串长度限制
* **异常情况**：确保异常被正确捕获和处理
* **状态管理**：检查状态变化的一致性和完整性

### 2. 性能瓶颈

* **算法复杂度**：识别时间和空间复杂度过高的代码
* **循环优化**：检查嵌套循环和不必要的重复计算
* **内存泄漏**：查找可能的内存泄漏点
* **数据库查询**：优化 SQL 查询和 N+1 问题

### 3. 安全漏洞

* **输入验证**：确保所有用户输入都被正确验证和清理
* **权限控制**：检查访问控制和身份验证逻辑
* **敏感信息**：防止敏感数据泄露或硬编码
* **注入攻击**：防范 SQL 注入、XSS 等安全漏洞

### 4. 可维护性问题

* **代码重复**：识别重复代码并建议提取公共函数
* **依赖关系**：检查模块间的耦合度
* **测试覆盖**：评估测试用例的完整性
* **文档完整性**：确保关键逻辑有适当的注释

## 审查流程

### 第一步：整体架构审查
- [ ] 检查整体设计是否合理
- [ ] 验证模块划分是否清晰
- [ ] 确认数据流是否正确

### 第二步：详细代码审查
- [ ] 逐行检查关键逻辑
- [ ] 验证错误处理机制
- [ ] 确认资源释放逻辑

### 第三步：测试和文档审查
- [ ] 检查测试用例覆盖度
- [ ] 验证文档更新是否及时
- [ ] 确认 API 文档的准确性

## 输出格式

审查完成后，请提供：

1. **问题清单**：按优先级列出发现的问题
2. **改进建议**：为每个问题提供具体的解决方案
3. **实施方案**：说明如何以最小的影响实现修改
4. **验证方法**：建议如何验证修改的正确性

## 严重性分级

* **严重（Critical）**：安全漏洞、数据丢失风险
* **重要（Major）**：性能问题、逻辑错误
* **一般（Minor）**：代码规范、可维护性问题
* **建议（Suggestion）**：代码优化建议
