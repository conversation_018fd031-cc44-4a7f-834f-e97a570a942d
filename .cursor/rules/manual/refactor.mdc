---
description: 
globs: 
alwaysApply: false
---
# 代码重构指南

## 重构目标

当需要重构代码时，请遵循以下目标：

### 提升代码质量

* **可读性更高**：使用清晰的变量名、合理的代码结构
* **易维护**：函数更小、职责单一、遵循单一职责原则
* **易测试**：便于编写单元测试，减少依赖耦合

### 重构原则

1. **保持功能不变**：确保重构后的代码功能与原代码完全一致
2. **最小化影响**：避免大幅修改，减少对其他依赖代码的影响
3. **渐进式改进**：采用小步快跑的方式进行重构

## 重构清单

### 命名优化
- [ ] 使用有意义的变量名和函数名
- [ ] 避免使用缩写和不明确的命名
- [ ] 遵循项目的命名规范

### 函数优化
- [ ] 保持函数职责单一
- [ ] 控制函数长度（建议不超过20行）
- [ ] 减少函数参数数量（建议不超过3个）
- [ ] 提取重复代码为公共函数

### 结构优化
- [ ] 移除不必要的注释和代码
- [ ] 优化条件判断的嵌套层级
- [ ] 使用早期返回模式减少嵌套

## 输出要求

重构完成后，请：

1. **说明修改内容**：详细列出所做的具体更改
2. **解释优化原因**：说明这些更改如何改善代码质量
3. **确认功能完整**：保证原有功能完全保留
