---
description: 
globs: 
alwaysApply: false
---
# 调试问题指导规范

## 概述

当遇到代码问题需要调试时，请按照系统化的方法进行问题分析和解决，确保问题能够被准确定位和有效修复。

## 调试流程

### 1. 问题描述
请提供以下信息：
- **代码片段**：出现问题的具体代码
- **错误日志**：完整的错误信息和堆栈跟踪
- **运行环境**：操作系统、版本、依赖等
- **重现步骤**：导致问题出现的具体操作

### 2. 原因分析
从以下角度思考 5～7 个可能的原因：
- **语法错误**：代码语法是否正确
- **逻辑错误**：业务逻辑是否合理
- **环境问题**：运行环境配置是否正确
- **依赖问题**：第三方库版本兼容性
- **数据问题**：输入数据格式或内容异常
- **并发问题**：多线程或异步处理冲突
- **性能问题**：资源占用或性能瓶颈

### 3. 优先级筛选
从上述原因中筛选出 1～2 个最可能的来源：
- 根据错误信息的特征
- 结合代码逻辑分析
- 考虑问题的复现条件

### 4. 假设验证
针对筛选出的原因提出假设，并采用以下方式验证：
- **添加调试日志**：在关键位置输出变量值
- **断点调试**：使用调试器逐步执行
- **单元测试**：编写测试用例验证假设
- **环境隔离**：在不同环境中测试

### 5. 问题定位
详细分析问题的本质：
- **根本原因**：问题发生的根本原因是什么
- **触发条件**：在什么情况下会出现这个问题
- **影响范围**：问题会影响哪些功能或模块

### 6. 解决方案
提供修复建议：
- **最简单的修复方式**：优先选择风险最小的方案
- **预防措施**：如何避免类似问题再次发生
- **测试验证**：如何验证修复的有效性

## 输出格式

```markdown
## 调试分析报告

### 问题描述
[问题的具体表现和环境信息]

### 可能原因分析
1. [原因1]
2. [原因2]
...

### 重点关注原因
1. [最可能的原因1 - 说明理由]
2. [最可能的原因2 - 说明理由]

### 验证假设
- 假设：[具体假设]
- 验证方法：[验证步骤]
- 验证结果：[结果说明]

### 问题本质
[深入分析问题的根本原因及其发生机制]

### 修复方案
[详细的修复步骤和代码修改建议]
```

## 注意事项

- 保持系统化的分析思路，避免盲目尝试
- 优先考虑对系统影响最小的修复方案
- 记录调试过程，便于后续问题追踪
- 修复后进行充分测试，确保不引入新问题
