---
description: 
globs: 
alwaysApply: false
---
# 错误分析与解决

## 概述

遇到错误或 bug 时，请按照以下全面的分析步骤进行处理，确保从根本上解决问题。

## 错误信息收集

首先，请提供完整的错误信息：

- 错误消息内容
- 错误发生的具体场景
- 复现步骤
- 相关代码片段

## 深度分析框架

不要只解决表面问题，请从以下维度进行全面分析：

### 架构层面分析

- 检查架构设计缺陷
- 分析模块间依赖关系
- 评估数据流和控制流的合理性

### 边界条件分析

- 识别可能触发问题的边界情况
- 检查输入验证和异常处理
- 分析并发和异步操作中的潜在问题

### 代码质量分析

- 检查代码逻辑的正确性
- 分析性能瓶颈和资源使用情况
- 评估错误处理机制的完整性

## 解决方案要求

提供的解决方案应该：

1. **根本原因修复**：解决问题的根本原因，而不是表面症状
2. **预防性设计**：防止类似问题再次发生
3. **可维护性**：确保修复后的代码易于理解和维护
4. **测试覆盖**：包含适当的测试策略

## 输出格式

分析完成后，请按照以下格式输出：

```markdown

### 根本原因分析
[详细说明问题的根本原因和导致问题的关键因素]

### 解决方案
[详细说明具体修复步骤、代码实现建议、测试和验证方法]

### 预防措施
[详细说明防止类似问题的建议、代码规范和最佳实践、监控和预警机制]

```
---

**重要提醒**：在提供解决方案之前，请进行详细分析并解释为什么以及如何从根本上修复问题。优先考虑代码的长期健康，而不是快速的临时修复。