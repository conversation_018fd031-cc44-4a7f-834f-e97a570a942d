---
description: 
globs: 
alwaysApply: true
---
# 代码生成标准规范

## 分析要求

请按照以下三个维度详细解释您生成的代码：

### 1. 功能分析
- **作用说明**：这部分代码的核心功能是什么？
- **业务价值**：解决了什么实际问题？
- **使用场景**：在什么情况下会用到这段代码？

### 2. 实现机制
- **执行流程**：代码是如何一步步实现功能的？
- **关键逻辑**：核心算法或处理逻辑是什么？
- **数据流转**：数据在代码中是如何流动和变化的？

### 3. 设计决策
- **方案对比**：考虑过哪些其他的实现方案？
- **选择理由**：为什么最终选择当前这个方案？
- **权衡考虑**：在性能、可维护性、复杂度等方面做了哪些权衡？

## 输出格式

在提供代码后，请按照以下结构进行说明：

```markdown
## 代码解释

### 功能分析
[详细说明代码的作用和价值]

### 实现机制
[逐步解释代码的执行流程]

### 设计决策
[解释选择此方案的原因和考虑因素]
```

## 注意事项

- 解释应该清晰易懂，避免过于技术化的术语
- 重点突出核心思路和关键决策点
- 适当提及潜在的优化空间或注意事项