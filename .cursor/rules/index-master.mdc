---
description: 
globs: 
alwaysApply: true
---
# 规则索引 - 项目代码规范汇总

本文件为项目的规则索引，汇总了所有代码规范和开发指导文件。

## 核心代码生成规范 (Always Applied)

@.cursor/rules/always/generate-code.mdc
> 代码生成的金标准规范，包含Vue组件、状态管理等核心开发规范

## 智能附加规范 (Auto Attached)

@.cursor/rules/auto-attached/vue-component.mdc
> Vue组件开发的自动附加规范，智能识别并应用

## 按需请求规范 (Agent Requested)

@.cursor/rules/agent-requested/git.mdc
> Git提交规范，包含commit message格式和分支管理策略

@.cursor/rules/agent-requested/pinia.mdc
> Pinia状态管理规范，包含store结构和composable函数最佳实践

@.cursor/rules/agent-requested/changelog.mdc
> 变更日志编写规范，包含版本记录和发布说明格式

## 手动操作规范 (Manual)

@.cursor/rules/manual/debug.mdc
> 调试和问题排查指南，包含调试工具使用和问题定位方法

@.cursor/rules/manual/review.mdc
> 代码审查规范，包含review checklist和质量标准

@.cursor/rules/manual/refactor.mdc
> 代码重构指南，包含重构策略和安全重构实践

@.cursor/rules/manual/fix.mdc
> 问题修复指南，包含bug修复流程和测试验证方法

## 历史规范 (Archived)

@.cursor/rules/old.mdc
> 历史规范文档，包含过往的开发规范和已废弃的实践
