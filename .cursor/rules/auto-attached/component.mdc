---
description: 
globs: *.vue
alwaysApply: false
---
# Vue 组件代码标准规范

此规范基于项目中表现最佳的组件实践，所有新创建的 Vue 组件都应遵循此标准。

## 组件结构标准

### 1. 模板部分 (Template)
```vue
<template>
    <div
        class="component-name"
        :class="{
            'condition-class': condition,
            'another-class': anotherCondition,
        }"
    >
        <slot></slot>
    </div>
</template>
```

**要求：**
- 使用语义化的根 class 名称，与组件名保持一致
- 条件类使用对象语法，每个条件占一行
- 优先使用 slot 来提供灵活性
- 属性换行对齐，提高可读性

### 2. 脚本部分 (Script Setup)
```vue
<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// Props 定义 - 必须包含完整的类型、默认值和注释
const props = defineProps({
    // 属性说明注释
    propName: {
        type: String,
        default: 'defaultValue'
    },
    // 带验证器的属性
    direction: {
        type: String,
        default: 'bottom',
        validator: (value) => ['bottom', 'left', 'right'].includes(value)
    },
    // 布尔类型属性
    autoStart: {
        type: Boolean,
        default: true
    }
});

// 响应式状态
const isVisible = ref(false);

// 计算属性
const computedValue = computed(() => {
    return someCalculation.value;
});

// 方法定义 - 必须包含 JSDoc 注释
/**
 * 方法说明
 * @param {string} param - 参数说明
 */
const methodName = (param) => {
    // 方法实现
};

// 暴露给父组件的方法
defineExpose({
    methodName,
    anotherMethod
});

// 生命周期钩子
onMounted(() => {
    // 组件挂载后的逻辑
});

onUnmounted(() => {
    // 组件卸载前的清理逻辑
});
</script>
```

**要求：**
- 使用 `<script setup>` 语法
- 按顺序组织：imports → props → 响应式状态 → 计算属性 → 方法 → 暴露的方法 → 生命周期
- 所有 props 必须定义类型、默认值和注释
- 方法必须使用 JSDoc 注释
- 复杂的验证逻辑使用 validator 函数

### 3. 样式部分 (Style)
```vue
<style lang="scss" scoped>
.component-name {
    // 基础样式
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

    // 修饰符类
    &.modifier-class {
        transform: translateX(-20px);
    }

    // 嵌套元素
    .child-element {
        // 子元素样式
    }
}
</style>
```

**要求：**
- 使用 `lang="scss"` 和 `scoped`
- 类名采用 kebab-case 格式
- 使用 BEM 命名规范
- 合理使用 CSS 变量和过渡动画
- 修饰符使用 `&.modifier` 语法

## 命名规范

### 组件命名
- 文件名：PascalCase（如：FadeInSection.vue）
- 组件内 class：kebab-case（如：fade-in-section）
- Props：camelCase（如：autoStart）
- 方法：camelCase（如：startAnimation）
- 响应式变量：camelCase（如：isVisible）

### 注释规范
- Props 必须有中文注释说明用途
- 复杂方法必须使用 JSDoc 格式
- 业务逻辑复杂的地方添加行内注释

## 最佳实践

1. **单一职责**：每个组件只负责一个明确的功能
2. **可复用性**：通过 props 和 slot 提供灵活性
3. **性能优化**：合理使用 computed 缓存计算结果
4. **类型安全**：所有 props 都要定义类型和验证
5. **可维护性**：代码结构清晰，注释完善
6. **响应式设计**：考虑不同屏幕尺寸的适配

## 代码示例对比

### ❌ 不推荐的写法
```vue
<template>
    <div class="comp">
        <div v-if="show">content</div>
    </div>
</template>

<script setup>
const props = defineProps(['data', 'visible']);
const show = ref(props.visible);
</script>

<style>
.comp { color: red; }
</style>
```

### ✅ 推荐的写法
```vue
<template>
    <div 
        class="fade-section"
        :class="{ 'is-visible': isVisible }"
    >
        <slot></slot>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
    // 是否自动显示
    autoShow: {
        type: Boolean,
        default: true
    },
    // 延迟时间(毫秒)
    delay: {
        type: Number,
        default: 0
    }
});

const isVisible = ref(false);

/**
 * 开始显示动画
 */
const startAnimation = () => {
    setTimeout(() => {
        isVisible.value = true;
    }, props.delay);
};

defineExpose({
    startAnimation
});

onMounted(() => {
    if (props.autoShow) {
        startAnimation();
    }
});
</script>

<style lang="scss" scoped>
.fade-section {
    opacity: 0;
    transition: opacity 0.3s ease;

    &.is-visible {
        opacity: 1;
    }
}
</style>
```

此规范将确保所有新创建的组件都具有一致的代码质量和可维护性。

