---
description: 进行 git commit时，参考此规范
globs: 
alwaysApply: false
---
# Git 提交规范

## 概述

统一的 Git 提交信息格式，确保项目历史记录清晰、易于理解和追踪。

## 提交信息格式

### 基本结构

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 组成部分

#### 1. Type（类型）
- **feat**: 新功能
- **fix**: 修复 bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 重构代码
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 2. Scope（范围）
- 可选，用圆括号包围
- 说明本次修改的影响范围
- 例如：`components`、`utils`、`auth` 等

#### 3. Subject（主题）
- 简洁描述本次提交的内容
- 使用现在时态
- 首字母小写
- 结尾不加句号
- 长度控制在 50 字符以内

#### 4. Body（正文）
- 可选
- 详细描述修改的原因和内容
- 每行不超过 72 字符

#### 5. Footer（页脚）
- 可选
- 用于关闭 issue 或描述不兼容变更
- 例如：`Closes #123` 或 `BREAKING CHANGE: ...`

## 示例

### 简单提交
```
feat(auth): 添加用户登录功能
```

### 完整提交
```
feat(chat): 添加语音输入功能

- 集成语音识别 API
- 添加录音控件组件
- 支持实时语音转文字

Closes #456
```

### 修复 Bug
```
fix(ui): 修复移动端适配问题

修复在小屏幕设备上按钮显示异常的问题
```

## 注意事项

- 提交信息使用中文
- 重要的功能变更需要在 body 中详细说明
- 重点提炼用户本次代码更新 从需求层面的意图。
