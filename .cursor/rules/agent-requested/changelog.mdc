---
description: 当你需要进行Changelog 变更日志时，阅读此规范
globs: 
alwaysApply: false
---
# Changelog 变更日志最佳实践

## 概述

变更日志（Changelog）是一个文件，其中包含了按时间顺序排列的项目每个版本的显著变更记录。本规范基于 [Keep a Changelog](mdc:https:/keepachangelog.com) 标准，确保项目版本记录清晰、易读且对用户友好。

## 基本原则

### 📋 核心理念
- **为人类而写**：变更日志是给人看的，不是机器
- **易于导航**：每个版本和章节都应该可以链接
- **时序清晰**：最新版本在最前面
- **分类明确**：相同类型的变更应该分组
- **完整记录**：每个版本都应该有对应的条目

### 🎯 必须遵循的格式

#### 文件命名
- **推荐**：`CHANGELOG.md`
- **可接受**：`HISTORY.md`、`RELEASES.md`
- **避免使用**：各种大小写混合的变体

#### 日期格式
- **标准格式**：`YYYY-MM-DD`（ISO 8601）
- **示例**：`2024-03-15`
- **原因**：国际标准，避免地区差异歧义

## 标准结构模板

```markdown
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](mdc:https:/keepachangelog.com/en/1.1.0),
and this project adheres to [Semantic Versioning](mdc:https:/semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- 计划中的新功能

### Changed
- 计划中的变更

### Deprecated
- 即将废弃的功能

### Removed
- 即将移除的功能

### Fixed
- 待修复的问题

### Security
- 安全相关的改进

## [1.0.0] - 2024-03-15

### Added
- 新增用户认证系统
- 添加多语言支持
- 实现数据导出功能

### Changed
- 优化数据库查询性能
- 更新UI设计风格
- 改进错误处理机制

### Deprecated
- 旧版API v1接口（将在v2.0.0中移除）

### Removed
- 移除已废弃的旧功能

### Fixed
- 修复移动端显示问题
- 解决内存泄漏问题

### Security
- 修复XSS安全漏洞
- 加强密码加密强度

## [0.9.0] - 2024-02-01

### Added
- 添加新的功能特性

[unreleased]: https://github.com/example/project/compare/v1.0.0...HEAD
[1.0.0]: https://github.com/example/project/compare/v0.9.0...v1.0.0
[0.9.0]: https://github.com/example/project/releases/tag/v0.9.0
```

## 变更类型分类

### ✅ 标准分类
- **Added** 新增功能：全新的功能特性
- **Changed** 功能变更：现有功能的修改
- **Deprecated** 功能废弃：即将移除的功能
- **Removed** 功能移除：本版本中移除的功能
- **Fixed** 问题修复：各种bug修复
- **Security** 安全相关：安全漏洞修复

### 📝 编写要求

#### 每个条目应该：
- 使用简洁明了的语言
- 聚焦用户影响，而非技术实现
- 包含相关的issue或PR编号
- 避免使用过于技术化的术语

#### 示例（好的写法）：
```markdown
### Added
- 新增暗色主题模式，用户可在设置中切换 (#123)
- 添加批量导出功能，支持Excel和CSV格式 (#456)

### Fixed
- 修复在Safari浏览器中图片显示异常的问题 (#789)
- 解决大文件上传时的超时问题 (#101)
```

#### 示例（避免的写法）：
```markdown
### Changed
- 重构了UserService类
- 更新依赖版本
- 优化代码
```

## 特殊情况处理

### 🚫 撤回版本（Yanked Releases）
```markdown
## [0.5.0] - 2024-01-15 [YANKED]

### Note
此版本因严重安全漏洞被撤回，请升级到最新版本。
```

### 🔄 未发布变更
始终在顶部保持 `[Unreleased]` 段落：
- 便于跟踪即将发布的变更
- 发布时只需修改标题和日期

### 🔗 版本链接
在文件末尾添加版本比较链接：
```markdown
[unreleased]: https://github.com/username/repo/compare/v1.0.0...HEAD
[1.0.0]: https://github.com/username/repo/compare/v0.9.0...v1.0.0
```

## 反面教材 - 避免的错误做法

### ❌ 直接使用Git日志
```markdown
## 错误示例
- fix typo
- merge branch feature/new-ui
- update README
- refactor: extract common utils
```

### ❌ 忽略废弃提醒
不在变更日志中标明废弃功能，导致用户升级时代码破坏。

### ❌ 日期格式不一致
```markdown
## 错误示例
- 03/15/2024 (美式)
- 15/03/2024 (欧式)  
- Mar 15, 2024 (英式)
```

### ❌ 分类混乱
把bug修复写在Added分类中，或者把新功能写在Fixed分类中。

## 自动化工具建议

### 推荐工具
- **GitHub Release Notes**：自动从PR和issue生成
- **release-changelog-builder-action**：GitHub Action自动化
- **standard-version**：自动化版本管理和changelog生成
- **changesets**：现代化的变更集管理

### 工具配置示例
```yaml
# .github/workflows/changelog.yml
name: Generate Changelog
on:
  push:
    tags: ['v*']
jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - uses: mikepenz/release-changelog-builder-action@v5
        with:
          configuration: ".github/changelog-config.json"
```

## 最佳实践检查清单

### ✅ 发布前检查
- [ ] 版本号遵循语义化版本控制
- [ ] 日期格式为 YYYY-MM-DD
- [ ] 所有重要变更都已记录
- [ ] 破坏性变更在Removed或Changed中突出标记
- [ ] 安全修复在Security分类中
- [ ] 版本链接正确设置
- [ ] 语言简洁明了，面向最终用户

### 🔍 质量检验
- 用户能够快速理解每个版本的主要变化吗？
- 升级指导信息是否清晰？
- 是否遗漏了重要的破坏性变更？
- 变更描述是否过于技术化？

## 团队协作建议

### 👥 流程规范
1. **开发阶段**：在Unreleased中及时更新变更
2. **Code Review**：检查changelog更新是否准确
3. **发布准备**：移动Unreleased内容到新版本
4. **发布后**：检查版本链接是否正确

### 📚 培训要点
- 强调changelog是用户文档，不是开发日志
- 培训团队成员识别什么是"显著变更"
- 建立变更分类的一致理解
- 定期review changelog质量

## 结语

优秀的changelog是软件项目的重要组成部分，它体现了对用户的尊重和对项目质量的追求。遵循这些规范，将让你的项目更专业、更易维护，用户体验更好。

记住：**不要让朋友把git日志倾倒到changelog中！**
