---
description: 生成状态管理代码时，参考此规范。
globs: 
alwaysApply: false
---
## Pinia Store 标准

### 1. Store 结构模板
```javascript
import { defineStore } from 'pinia';
import httpService from '@/utils/httpService';

// 存储键名常量 - 统一管理本地存储的键名
const STORAGE_KEYS = {
    SETTING_NAME: 'module_setting_name',
    CACHE_DATA: 'module_cache_data'
};

export const useModuleStore = defineStore('moduleName', {
    // 状态定义为返回初始状态的函数
    state: () => ({
        // 基础状态 - 添加注释说明用途
        isLoading: false,
        // 业务数据
        dataList: [],
        // 配置项
        settings: {
            autoRefresh: true,
            refreshInterval: 30000
        }
    }),

    // 定义 getters - 计算属性
    getters: {
        // 获取特定条件的数据
        getItemById: (state) => (id) => {
            return state.dataList.find(item => item.id === id);
        },
        
        // 统计类 getter
        totalCount: (state) => state.dataList.length
    },

    // 定义 actions - 方法
    actions: {
        /**
         * 设置加载状态
         * @param {boolean} loading - 加载状态
         */
        setLoading(loading) {
            this.isLoading = loading;
        },

        /**
         * 获取数据列表
         * @param {Object} params - 查询参数
         * @returns {Promise} 返回请求结果
         */
        async fetchDataList(params = {}) {
            try {
                this.setLoading(true);
                const url = '/api/module/list';
                const response = await httpService.post(url, params);
                
                if (response.returncode === 0) {
                    this.dataList = response.result || [];
                }
                
                return response;
            } catch (error) {
                console.error('获取数据列表失败:', error);
                throw error;
            } finally {
                this.setLoading(false);
            }
        },

        /**
         * 清空数据
         */
        clearData() {
            this.dataList = [];
            this.settings = {
                autoRefresh: true,
                refreshInterval: 30000
            };
        }
    }
});
```

### 2. Store 规范要求

**命名规范：**
- Store 名称：useXxxStore
- 文件名：kebab-case（如：user-profile.js）
- 常量：UPPER_SNAKE_CASE

**结构规范：**
- 导入依赖在顶部
- 常量定义在导入之后
- state 必须是函数返回对象
- 所有方法必须添加 JSDoc 注释
- 异步方法必须有错误处理

## 错误处理标准

### 1. Store 中的错误处理
```javascript
async fetchData(params) {
    try {
        this.setLoading(true);
        const response = await httpService.post('/api/data', params);
        
        if (response.returncode === 0) {
            this.data = response.result;
        } else {
            throw new Error(response.message || '请求失败');
        }
        
        return response;
    } catch (error) {
        console.error('获取数据失败:', error);
        // 根据错误类型进行不同处理
        if (error.code === 'NETWORK_ERROR') {
            // 网络错误处理
        }
        throw error; // 重新抛出让调用者处理
    } finally {
        this.setLoading(false);
    }
}
```
## 最佳实践

1. **单一职责**：每个 Store 只管理一个领域的状态
3. **错误边界**：所有异步操作都要有错误处理
4. **性能优化**：合理使用 computed 和 readonly
6. **可测试性**：方法职责单一，便于单元测试

## 代码示例对比

### ❌ 不推荐的写法
```javascript
// Store
export const useStore = defineStore('store', {
    state: () => ({ data: null }),
    actions: {
        async getData() {
            const res = await fetch('/api');
            this.data = await res.json();
        }
    }
});

// Composable
export function useLogic() {
    const data = ref(null);
    const getData = () => { /* 逻辑 */ };
    return { data, getData };
}
```

### ✅ 推荐的写法
```javascript
// Store
export const useDataStore = defineStore('data', {
    state: () => ({
        dataList: [],
        isLoading: false
    }),
    actions: {
        /**
         * 获取数据列表
         * @returns {Promise} 请求结果
         */
        async fetchDataList() {
            try {
                this.isLoading = true;
                const response = await httpService.get('/api/data');
                this.dataList = response.result || [];
                return response;
            } catch (error) {
                console.error('获取数据失败:', error);
                throw error;
            } finally {
                this.isLoading = false;
            }
        }
    }
});